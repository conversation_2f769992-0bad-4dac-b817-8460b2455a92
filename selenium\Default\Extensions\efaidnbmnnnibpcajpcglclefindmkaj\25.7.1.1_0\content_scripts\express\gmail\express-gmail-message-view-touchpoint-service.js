/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import state from"../../gmail/state.js";import{getArrayElementBasedOnSelector}from"../../gmail/util.js";import{sendAnalytics}from"../../gsuite/util.js";import SingleClickCTA from"../single-click-cta.js";const messageViewTouchpoint="gmailMessageView",EXPRESS_PROCESSED_ATTRIBUTE="express-processed",_getMessageView=()=>getArrayElementBasedOnSelector(document,"messageView","messageView"),_getMessageViewImageElement=e=>{const t=e.previousElementSibling;return t&&"IMG"===t.tagName?t:null},_positionExpressButton=(e,t,s)=>{s.style.position="relative",_setButtonCoordinates(e,t,s);new ResizeObserver((()=>{_setButtonCoordinates(e,t,s)})).observe(s)},_setButtonCoordinates=(e,t,s)=>{const n=t.getBoundingClientRect(),i=s.getBoundingClientRect(),a=i.right-n.right+8,o=n.top-i.top+8;e.style.position="absolute",e.style.right=`${a}px`,e.style.top=`${o}px`,e.style.opacity="0",e.style.display="none"},_showExpressButton=e=>{"Y"!==e.getAttribute("impression-analytics-logged")&&(e.setAttribute("impression-analytics-logged","Y"),sendAnalytics(["DCBrowserExt:Express:GmailMessageView:EntryPoint:Shown"])),e.style.opacity="1",e.style.display="block"},_hideExpressButton=e=>{e.style.opacity="0",e.style.display="none"},_showImageActionTray=e=>{e.style.opacity="1"},_hideImageActionTray=e=>{e.style.opacity="0"},_addHoverEventListeners=(e,t,s)=>{t.addEventListener("mouseenter",(()=>{_showExpressButton(e),_showImageActionTray(s)})),s.addEventListener("mouseenter",(()=>{_showExpressButton(e)})),t.addEventListener("mouseleave",(t=>{t.relatedTarget&&(e.contains(t.relatedTarget)||s.contains(t.relatedTarget))||_hideExpressButton(e)})),e.addEventListener("mouseleave",(n=>{n.relatedTarget&&(t.contains(n.relatedTarget)||s.contains(n.relatedTarget))||(_hideExpressButton(e),_hideImageActionTray(s))})),s.addEventListener("mouseleave",(s=>{s.relatedTarget&&(t.contains(s.relatedTarget)||e.contains(s.relatedTarget))||_hideExpressButton(e)}))},_getClickCallback=e=>t=>{chrome.runtime.sendMessage({main_op:"launch-express",imgUrl:e,intent:t,touchpoint:"gmailMessageView"})},addExpressMessageViewTouchpoint=async()=>{const e=_getMessageView();e&&e.length>0&&e.forEach((async e=>{const t=e.getElementsByClassName(state?.expressConfig?.selectors?.imageActionTray??"a6S");if(t&&t.length>0)for(const e of t){const t=e.parentElement,s=_getMessageViewImageElement(e);if(s&&!e.hasAttribute("express-processed")){e.setAttribute("express-processed","Y");const n=new SingleClickCTA,i=await n.renderMenuButton(_getClickCallback(s.src),"gmailMessageView");_positionExpressButton(i,s,t),t.appendChild(i),_addHoverEventListeners(i,s,e),n.attachTooltip("bottom")}else e?.setAttribute("express-processed","Y")}}))};export{addExpressMessageViewTouchpoint};