/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{common as e}from"./common.js";import{getVersionConfig as t}from"./api-util.js";import{util as n}from"./util.js";import{SETTINGS as i}from"./settings.js";import{dcLocalStorage as o}from"../common/local-storage.js";import{floodgate as c}from"./floodgate.js";import{loggingApi as r}from"../common/loggingApi.js";let s;s||(s=new function(){this.setCommonItems=function(){o.setItem("dcApiUri",e.getDcApiUri()),o.setItem("locale",n.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale"))),o.setItem("env",e.getEnv()),o.setItem("viewerImsClientId",e.getViewerIMSClientId()),o.setItem("imsContextId",e.getImsContextId()),o.setItem("viewerImsClientIdSocial",e.getViewerIMSClientIdSocial()),o.setItem("imsURL",e.getIMSurl()),o.setItem("imsLibUrl",e.getImsLibUrl()),o.setItem("signInUrl",e.getSignInUrl()),o.setItem("genAIPricingUri",e.getGenAIPricingUri()),o.setItem("sidepanelUrl",e.getSidepanelUrl())},this.updateVariables=async function(t){try{this.setCommonItems();let n=0!=t&&1!=t&&-1!=t,c=!(!n||t===i.READER_VER||t===i.ERP_READER_VER);o.setItem("cdnUrl",e.deriveCDNURL(e.getEnv())),o.setItem("isDeskTop",n),o.setItem("isAcrobat",c),o.getItem("theme")||o.setItem("theme","auto");const r={"dc-cv-enable-download-dialog":"edd","dc-cv-rapid-rendition-view":"rrv","dc-cv-lin-rrv":"lrrv","dc-cv-save-location-on-options":"isSaveLocationPrefEnabled","dc-cv-enable-splunk-logging":"splunkLoggingEnable","dc-cv-enable-embed-viewer":"ev","dc-cv-ext-menu-dark-mode":"enableExtMenuDarkMode","dc-cv-alloy-on":"ao","dc-register-access-token-expired":"rate","dc-cv-alloy-on-ext-menu":"aoem","dc-cv-image-print":"ip","dc-cv-document-properties":"sdp","dc-cv-show-digital-signature":"sds","dc-cv-enable-cdn-versioning":"enableCDNVersioning","dc-cv-gen-ai":"genAI","dc-cv-premium-icon":"pi","dc-cv-mega-verbs":"mv","dc-cv-keyboard-shortcut":"ks","dc-cv-inline-rs":"rs","dc-cv-enable-CSRF":"enableCSRF","dc-cv-night-mode":"supportNightMode","dc-cv-thumbnail-plg-test":"tpt","dc-cv-local-file-access-tp":"lft","dc-cv-upsell-full-screen-chrome":"fsu","dc-cv-doc-cloud-share":"dcs","dc-cv-enable-genai-limits-api":"egal","dc-cv-other-tab-signout":"ots","dc-cv-cdn-sign-in":"csi","dc-cv-settings-window":"openSettingsInWindow","dc-cv-localised-gif":"showLocalisedGif","dc-cv-enable-guest-genai":"gga","dc-cv-reset-counter":"rc","dc-cv-decrease-cooldown":"dc","dc-cv-enable-pnb":"pnb","dc-cv-subscription-v2-api":"subv2","dc-cv-tp-cookies-block-track":"enableTpcBlockAnalytics","dc-cv-s3-download":"s3d","dc-cv-ims-profile-switcher":"ips","dc-cv-resolve-ims-promise-error":"ripe","dc-cv-fg-rearch":"fgRearch","dc-cv-enable-local-fonts":"lf","dc-cv-onboarding-demo":"od","dc-cv-kw-gn-touchpoint":"kwgn","dc-sp-url-change-on-scroll-logging":"enableURLChangeOnScrollLogging","dc-sp-enable-fab-drag":"enableFabDrag","dc-cv-disable-prefetch-requests":"ignorePrefetchRequests","dc-cv-redact-all-tools":"rat","dc-sp-url-hash-scrolling-logging":"enableURLHashScrollingLogging","dc-sp-url-hash-scrolling-feature":"enableURLHashScrollingFeature"};navigator.onLine&&(r["dc-cv-offline-support-disable"]="offlineSupportDisable");const s=await Promise.all(Object.entries(r).map((([e,t])=>this.checkFeatureEnable({flagName:e,storageKey:t}).then((e=>[t,e]))))),a=Object.fromEntries(s),l=a.isSaveLocationPrefEnabled,d=a.splunkLoggingEnable,g=a.supportNightMode;!l&&o.getItem("saveLocation")?o.removeItem("saveLocation"):l&&!o.getItem("saveLocation")&&o.setItem("saveLocation","ask"),!g&&o.getItem("isDarkPageThemeEnabled")&&o.removeItem("isDarkPageThemeEnabled"),this.enableSplunk(d),o.removeItem("allownft"),o.getItem("anonUserUUID")||e.createAnonUserUUID()}catch(e){console.error("Error checking feature flags:",e)}},this.checkFeatureEnable=async function(e){const{flagName:t,storageKey:n}=e,i=await c.hasFlag(t);return n&&o.setItem(n,!!i),i},this.fetchAndUpdateVersionConfig=async function(){if(o.getItem("enableCDNVersioning"))try{const e=await t();if(e){const t={...i("prod",e.prod),...i("non_prod",e.non_prod)};o.setItem("version-config",t)}}catch(e){n.consoleLog(new Error(`Version Config failure: ${e}`))}function e(e,t){for(var n in e.extension)if(e.extension[n].id===t)return{iv:e.extension[n].internal_version,ev:e.extension[n].external_version}}function i(t,n){if(!n)return;let i={};const o=e(n,"all_extensions"),c=e(n,chrome.runtime.id);return i[`ev_${t}`]="inherit"!==c?.ev?c?.ev:o?.ev,i[`iv_${t}`]="inherit"!==c?.iv?c?.iv:o?.iv,i}},this.isTabURLFromExternalTouchPoint=e=>e&&this.isPDFURLFromExternalTouchPoint(this.extractUrlValue("pdfurl",e)),this.isPDFURLFromExternalTouchPoint=e=>e&&new URL(e)?.searchParams?.has("acrobatPromotionSource"),this.extractUrlValue=(e,t)=>{t||(t=window.location.href);try{const n=new URL(t);return new URLSearchParams(n.search).get(e)}catch(e){return}},this.enableSplunk=e=>{if(r.registerLogInterval(e),e){let e=c.getFeatureMeta("dc-cv-enable-splunk-logging")||"{}";try{e=JSON.parse(e)}catch(e){console.error("Error parsing feature meta for splunk logging: ",e)}o.setItem("allowedLogIndex",e.index),o.setItem("disableLogWhitelisting",e.disableLogWhitelisting)}}});export const viewerModuleUtils=s;