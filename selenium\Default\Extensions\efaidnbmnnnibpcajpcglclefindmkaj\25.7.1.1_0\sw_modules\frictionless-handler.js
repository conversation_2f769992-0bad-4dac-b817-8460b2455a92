/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{util as e}from"./util.js";import{communicate as t}from"./communicate.js";import{analytics as i}from"../common/analytics.js";import{common as s}from"./common.js";import{Proxy as o}from"./proxy.js";import{dcLocalStorage as n}from"../common/local-storage.js";import{sendPingEventHandler as _}from"../common/util.js";const r={SIGNED_IN:"signed_in",LIMITS:"limits",COMPLETE_CONVERSION:"complete_conversion"},a={SEARCH:"search",TREFOIL:"trefoil"},c={PRE_PROCESSING_ERROR:"pre-processing-error",PROCESSING_ERROR:"processing-error",FILE_UPLOAD_START:"file-upload-start",FILE_UPLOAD_COMPLETE:"file-upload-complete",DROPZONE_DISPLAYED:"dropzone-displayed",STARTUP_ERROR:"startup-error"},p={DISMISS:"dismiss",NONE:"none"},l={LOAD_FRICTIONLESS:"load-frictionless",SEND_EXTERNAL_MESSAGE:"send-external-msg",SHOW_FRICTIONLESS_ERROR:"show-frictionless-error",CLEAR_FRICTIONLESS_ERROR:"clear-frictionless-error"},d={LIMITS_TIME:"limits_time",SIGNED_IN_TIME:"signed_in_time",DROPZONE_DISPLAY_TIME:"dropzone_display_time",STARTUP_TO_FRAME_LOAD:"startup_to_iframe_load"},f="word-to-pdf",m="ppt-to-pdf",E="jpg-to-pdf",O="png-to-pdf",I="excel-to-pdf",R="createpdf",S="compress-pdf",u="pdf-to-word",T="pdf-to-excel",L="pdf-to-ppt",N="pdf-to-image",g={CREATE_PDF:"create_pdf",COMPRESS_PDF:"compress_pdf",EXPORT_PDF:"export_pdf",ORGANISE_PDF:"organize_pdf",DOWNLOAD:"download",UPLOAD:"upload",PROTECT_PDF:"protect_pdf",COMBINE_PDF:"combine_pdf",OCR_PDF:"ocr_pdf"},D="reorder-pages",P="rotate-pages",F="delete-pages",A="split-pdf",M="extract-pages",C="insert-pdf",h="crop-pages",w="number-pages",b="add-comment",v="fillsign",k="sendforsignature",W="chat-pdf",U="combine-pdf",y="protect-pdf",G="ocr-pdf";let x=null;x||(x=new function(){const x={};function H(){this.received_limits=!1,this.limits_time=null,this.received_signedin=!1,this.signed_in_time=null,this.signed_in=!1,this.received_display=!1,this.dropzone_display_time=null,this.received_error=!1,this.error_title=null,this.error_description=null,this.pdf_action=null,this.workflow=null,this.startup_time=null,this.iframe_onload_time=null,this.iframe_call_time=null}function K(e){return x[e]||(x[e]=new H),x[e]}this.proxy=o.proxy.bind(this),this.LOG=(...e)=>s.LOG(...e);const j=function(e,t,s,o){const n=K(e);if(1==n.received_limits&&1==n.received_signedin&&1==n.received_display){if(s===a.SEARCH){if(1==n.has_free_ops){let n=t.panel_op;B(e,t),V(e,t),t.panel_op=n;let _=o?o.toUpperCase().replace(/\-/g,"_"):"UNKNOWN";i.event(i.e.FRICTIONLESS_WIDGET_LOADED,{TOOL:_,WORKFLOW:s})}}else if(Z(e,t),1==n.has_free_ops){let e=t.pdf_action?t.pdf_action.toUpperCase().replace(/\-/g,"_"):"UNKNOWN";i.event(i.e.FRICTIONLESS_WIDGET_LOADED,{TOOL:e,WORKFLOW:t.frictionless_workflow})}!function(e){delete x[e]}(e)}},z=function(e,t,s,o,n){const _=K(e);_.received_limits=!0,_.limits_time=n.timeStamp;const r=t.limits;if(!s||!r)return!1;_.has_free_ops=function(e,t){let i=null;switch(e){case f:case m:case E:case O:case I:case R:i=g.CREATE_PDF;break;case S:i=g.COMPRESS_PDF;break;case u:case T:case L:case N:i=g.EXPORT_PDF;break;case y:i=g.PROTECT_PDF;break;case G:i=g.OCR_PDF;break;case U:i=g.COMBINE_PDF;break;case P:case D:case F:i=g.ORGANISE_PDF;break;case A:case M:case C:case v:case h:case W:case w:case b:case k:i=g.DOWNLOAD}if(null===i)return!1;let s=t[i];return s&&(s.can_process||s.can_download)||!1}(s,r);const c=_.has_free_ops?"UnderLimit":"OverLimit";i.event(i.e.FRICTIONLESS_CONVERSION_LIMITS,{WORKFLOW:o,RESULT:c}),Y(e,n,d.LIMITS_TIME),o!==a.SEARCH||_.has_free_ops?j(e,n,o,s):X(e,n)},X=function(e,i){i.content_op=p.DISMISS,"search"==i.frictionless_workflow?t.sendMessage(i,!1):t.sendMessageToPopup(i,!1)},Y=function(e,i,s){if("false"===n.getItem("logAnalytics"))return;const o=K(e),_=i.panel_op;let r={content:"timing_info"};r.workflow=i.frictionless_workflow,function(e,t,i){switch(i){case d.STARTUP_TO_FRAME_LOAD:t.startup_time&&(t.iframe_onload_time&&(e.startup_to_iframe_load=t.iframe_onload_time-t.startup_time),t.iframe_call_time&&(e.startup_to_iframe_call=t.iframe_call_time-t.startup_time));break;case d.SIGNED_IN_TIME:t.startup_time&&t.signed_in_time&&(e.startup_to_signin=t.signed_in_time-t.startup_time);break;case d.LIMITS_TIME:t.startup_time&&t.limits_time&&(e.startup_to_limits=t.limits_time-t.startup_time);break;case d.DROPZONE_DISPLAY_TIME:t.startup_time&&t.dropzone_display_time&&(e.startup_to_display=t.dropzone_display_time-t.startup_time)}}(r,o,s),i.data=r,i.panel_op=l.SEND_EXTERNAL_MESSAGE,"search"==i.frictionless_workflow?t.sendMessage(i,!1):t.sendMessageToPopup(i,!1),i.panel_op=_},Z=function(e,i){i.panel_op=l.LOAD_FRICTIONLESS,i.content_op=p.NONE,i.hide_spinner=!0,"search"==i.frictionless_workflow?t.sendMessage(i,!1):t.sendMessageToPopup(i,!1),i.hide_spinner=void 0},B=function(e,i){i.panel_op=l.SEND_EXTERNAL_MESSAGE,i.data={valid:"true"},t.sendMessage(i,!1)},V=function(e,i){_(),i.content_op=p.NONE,i.panel_op=l.LOAD_FRICTIONLESS,i.frame_visibility="visible",t.sendMessage(i,!1)};this.startNewInteraction=function(e){return x[e]=new H,x[e]},this.externalMsgHandler=function(s,o,n){const _=s.data,f=o.tab.id,m=t.getTabLastMessage(f);let E=!1;switch(_.content_op){case r.SIGNED_IN:!function(e,t,s,o,n){const _=K(e);_.received_signedin=!0,_.signed_in_time=n.timeStamp,_.signed_in=t.is_signed_in;const r=_.signed_in?"SignedIn":"SignedOut";i.event(i.e.FRICTIONLESS_USER_SIGNEDIN,{WORKFLOW:o,RESULT:r}),Y(e,n,d.SIGNED_IN_TIME),j(e,n,o,s)}(f,_,m.pdf_action,m.frictionless_workflow,s),E=!0;break;case r.LIMITS:z(f,_,m.pdf_action,m.frictionless_workflow,s),E=!0;break;case r.COMPLETE_CONVERSION:!function(t,s,o,n){if(!s||!s.conversion_url)return!1;e.createTab(s.conversion_url),i.event(i.e.FRICTIONLESS_SWITCH_TAB,{WORKFLOW:o}),X(t,n)}(f,_,m.frictionless_workflow,s),E=!0}if(!E&&_.dc_hosted_event){const e=_.dc_hosted_event.event,o=_.dc_hosted_event.event_data;switch(e){case c.PRE_PROCESSING_ERROR:case c.PROCESSING_ERROR:!function(e,i,s,o){o.panel_op=l.SHOW_FRICTIONLESS_ERROR,o.content_op=p.NONE,o.error_title=i,o.error_description=s,"search"==o.frictionless_workflow?t.sendMessage(o,!1):t.sendMessageToPopup(o,!1)}(0,o.title,o.description,s);break;case c.FILE_UPLOAD_START:i.event(i.e.FRICTIONLESS_FILE_UPLOAD_STARTED,{WORKFLOW:m.frictionless_workflow}),function(e,i){const s=i.panel_op;i.panel_op=l.CLEAR_FRICTIONLESS_ERROR,"search"==i.frictionless_workflow?t.sendMessage(i,!1):t.sendMessageToPopup(i,!1),i.panel_op=s}(0,s);break;case c.FILE_UPLOAD_COMPLETE:i.event(i.e.FRICTIONLESS_FILE_UPLOAD_COMPLETED,{WORKFLOW:m.frictionless_workflow});break;case c.DROPZONE_DISPLAYED:!function(e,t,i,s){const o=K(e);o.received_display=!0,o.dropzone_display_time=s.timeStamp,Y(e,s,d.DROPZONE_DISPLAY_TIME),j(e,s,i,t)}(f,m.pdf_action,m.frictionless_workflow,s);break;case c.STARTUP_ERROR:!function(e,t,i){t===a.SEARCH?X(e,i):t===a.TREFOIL&&Z(e,i)}(f,workflow,m);const e=m.pdf_action?m.pdf_action.toUpperCase().replace(/\-/g,"_"):"UNKNOWN";i.event(i.e.FRICTIONLESS_WIDGET_STARTUP_ERROR,{TOOL:e})}}delete m.timeStamp},this.sendFrictionlessURL=async function(e,i,o){e.panel_op=l.LOAD_FRICTIONLESS,e.frictionless_uri=s.getFrictionlessUri(),e.env=s.getEnv();let n=K(e.tabId);!e.startup_time||n.startup_time&&n.startup_time===e.startup_time||(n=this.startNewInteraction(e.tabId),n.startup_time=e.startup_time),t.sendMessageToPopup(e,!1)},this.handleTimingInformationFromWidget=function(e,i,s){let o=K(e.tabId);const n=e.timing_op;if(delete e.timing_op,t.getTabLastMessage(e.tabId)){t.updateTabMessage(e.tabId,e);const i=t.getTabLastMessage(e.tabId);o.startup_time=o.startup_time||e.startup_time,o.iframe_onload_time=o.onload_time||e.iframe_onload_time,o.iframe_call_time=o.iframe_call_time||e.iframe_call_time,n&&Y(e.tabId,i,n)}}},t.registerHandlers({"get-frictionless-url":x.proxy(x.sendFrictionlessURL),external_msg:x.proxy(x.externalMsgHandler),timing_info:x.proxy(x.handleTimingInformationFromWidget)}));export const frictionlessHandler=x;