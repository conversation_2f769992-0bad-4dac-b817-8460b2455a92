/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{util as e}from"./util.js";import{common as t}from"./common.js";import{analytics as a}from"../common/analytics.js";import{Proxy as s}from"./proxy.js";import{floodgate as i}from"./floodgate.js";import{sharepointModule as r}from"./sharepoint-module.js";import{privateApi as o}from"./private-api.js";import{viewerModuleUtils as n}from"./viewer-module-utils.js";import{SETTINGS as l}from"./settings.js";import{dcLocalStorage as c,dcSessionStorage as d}from"../common/local-storage.js";import{OFFSCREEN_DOCUMENT_PATH as p}from"../common/constant.js";import{CACHE_PURGE_SCHEME as u}from"./constant.js";import{closeExpress as m,getModalSessionId as g,isExpressContextMenuEnabled as h,isGmailNativeViewerExpressMenuEnabled as f,isGoogleImagePreviewExpressMenuEnabled as b,isWhatsappHoverExpressMenuEnabled as v,isWhatsappPreviewExpressMenuEnabled as I,isGmailMessageViewExpressMenuEnabled as E,launchExpress as w,removeSessionFromUnloadedExpressSessions as F,toggleExpressTouchpoints as T}from"./express.js";import{loggingApi as _}from"../common/loggingApi.js";import{resetDVSessionCount as S,getDVSessionCountString as P,fetchDefaultViewershipConfig as D}from"../content_scripts/gsuite/util.js";import{setDVExperimentCodeForAnalytics as R}from"../common/util.js";import{getActiveExperimentAnalyticsArray as C,setExperimentCodeForAnalytics as x,removeExperimentCodeForAnalytics as A}from"../common/experimentUtils.js";import{tempURLBufferIndexedDB as L}from"../common/temporaryURLBufferIndexDB.js";let N=null,M={},y={};class O{constructor(){this.tabs={},this.version=-1,this.NMHConnStatus=!0,this.activeTab=void 0,this.isAllowedLocalFileAccess=!1}proxy(...e){return s.proxy.bind(this)(...e)}LOG(...e){return t.LOG(...e)}setIsAllowedLocalFileAccess(e){this.isAllowedLocalFileAccess=e,c.setItem("isAllowedLocalFileAccess",e)}registerHandlers(t){y=e.extend(y,t)}registerModule(e,t){M[e]=t}getModule(e){return M[e]}getTabLastMessage(e){return this.tabs[e]}updateTabMessage(t,a){this.tabs[t]?this.tabs[t]=e.extend(this.tabs[t],a):this.tabs[t]=a}setNMHConnectionStatus(e){this.NMHConnStatus=e}legacyShim(){return this.version<=1}setVersion(e){this.version=e}resetPersistPrefCount(){c.setItem("persist-menu-closed",0)}incrementPersistPrefCount(e){let t=c.getItem("persist-menu-closed");t<10&&"false"!==c.getItem("always-show-pdf-menu")&&(t++,c.setItem("persist-menu-closed",t)),t>=10&&(c.setItem("always-show-pdf-menu","false"),e||a.event(a.e.PERSIST_PDF_OPENPDF_PREF_OFF))}diffDays(e,t){const a=Math.abs(e-t);return Math.floor(a/864e5)}enableReprompt(){if(!c.getItem("repromptCount")||parseInt(c.getItem("repromptCount"))<l.REPROMPT_DORMANT_USER_ATTEMPTS){let e=Date.now(),t=c.getItem("fteDenied")&&10===parseInt(c.getItem("fteDenied")),a=c.getItem("pdfViewer"),s=l.VIEWER_ENABLED,i=!c.getItem("reprompt-user-timestamp"),r=c.getItem("repromptCount")?c.getItem("repromptCount"):0,o=c.getItem("reprompt-user-timestamp")&&this.diffDays(e,c.getItem("reprompt-user-timestamp"))>=l.REPROMPT_DORMANT_USER_TIME_INTERVALS[r];return s&&!a&&t&&(o||i)}return!1}async isEnablePersistMenu(e){if(e&&"mime-native"===e.viewer&&(l.IS_ERP_READER||l.IS_READER)&&c.getItem("openInAcrobatEnable")&&"admin"!==c.getItem("installSource"))return!1;if(await o.isInstalledViaUpsell()&&!c.getItem("pdfViewer"))return!1;if(e&&"mime"===e.viewer)return!1;if(l.VIEWER_ENABLED&&(l.IS_ACROBAT&&!l.VIEWER_ENABLED_FOR_ACROBAT||c.getItem("fteDenied")||"true"!==c.getItem("pdfViewer")||this.resetPersistPrefCount()),l.REPROMPT_DORMANT_USER&&this.enableReprompt()){let e=c.getItem("repromptCount")?parseInt(c.getItem("repromptCount"))+1:1;c.setItem("repromptCount",e),c.setItem("fteDenied",9),c.setItem("persist-menu-closed",9),c.removeItem("always-show-pdf-menu")}return"false"!==c.getItem("always-show-pdf-menu")&&(c.getItem("persist-menu-closed")<10?!(this.legacyShim()&&(!l.VIEWER_ENABLED||c.getItem("pdfViewer"))):"true"===c.getItem("always-show-pdf-menu")&&(this.resetPersistPrefCount(),!0))}isViewerEnabled(){return!(!l.VIEWER_ENABLED||l.IS_ACROBAT&&!l.VIEWER_ENABLED_FOR_ACROBAT||"true"!==c.getItem("pdfViewer"))}async relayMessageToContentScript(e,t,a){if(1==e.newUI&&"dismiss"===e.content_op)e.persist=!1,null!=e.tabId&&(this.tabs[e.tabId].persist=!1),this.incrementPersistPrefCount(e.fteClosed);else if(1==e.newUI&&"pdf_menu"===e.panel_op){await this.isEnablePersistMenu(e)?null!=e.tabId&&(this.tabs[e.tabId].persist=!0):e.persist=!1,this.resetPersistPrefCount()}this.sendMessage(e,t,a)}isViewerEnabledOrFromExternalTouchPoint(e){return this.isViewerEnabled()||n.isPDFURLFromExternalTouchPoint(e)}sendViewerStartupInformation(t,s,o){if(this.isViewerEnabledOrFromExternalTouchPoint(s)&&e.isViewerURL(s)||t.mimeHandled){if("viewer-startup"===t.main_op&&(delete t.main_op,t.isFrictionlessEnabled=l.FRICTIONLESS_ENABLED,t.isSharePointEnabled=r.isFeatureEnabled(),t.isSharePointURL=!1),t.isSharePointEnabled)try{let e=new URL(s).searchParams.get("pdfurl");t.isSharePointURL=r.isAllowListedSharePointURL(e)}catch(e){}t.mimeHandled&&(a.event(a.e.VIEWER_EXTN_PDF_OPENED,{tabURL:s}),s.startsWith("file://")?a.event(a.e.VIEWER_PDF_LOCAL_FILE):a.event(a.e.VIEWER_PDF_OPENED_MIME_HANDLER)),t.isFillnSignEnabled=l.FILL_N_SIGN_ENABLED,i.getFeaturesAndGroups().then((({featureFlags:e})=>{t.featureFlags=e,o(t)})).catch((a=>{o(t),e.consoleLog(a)}))}}async sendViewerPreviewInformation(e,t){this.sendMessage({dend_op:"viewer-type",viewer:"mime",tabId:e.tabId,is_pdf:!0}),await n.updateVariables(this.version),t()}handlerTabs(e){e&&e.tabId&&"outermost_frame"===e.frameType&&communicate.filterLoadedTabs(e)}async filterLoadedTabs(e){const t=await o.isMimeHandlerAvailable(),a=c.getItem("loadedTabsInfo");if(a&&a.tabsInfo){const s=a.tabsInfo||[];if(t){if(s.includes(e.tabId)){const t=s.filter((t=>t!==e.tabId));t.length>0?c.setItem("loadedTabsInfo",{tabsInfo:t}):c.removeItem("loadedTabsInfo")}}else{-1!==s.findIndex((t=>t.id==e.tabId))&&this.getCurrentTabInfoAndUpdateLoadedTabs()}}}async getCurrentTabInfoAndUpdateLoadedTabs(){const e=await o.isMimeHandlerAvailable(),t=c.getItem("loadedTabsInfo"),a=t?.tabsInfo;if(!e&&a){const e=`chrome-extension://${chrome.runtime.id}/*`;let t=await chrome.tabs.query({url:e});t=t.filter((e=>e.url!==chrome.runtime.getURL("browser/js/local-fte.html"))),t.length?c.setItem("loadedTabsInfo",{tabsInfo:t}):c.removeItem("loadedTabsInfo")}}handler(t,s,r){var n,l=this;if(!t)return;if(this.dump(t,"Communicate Handler receive: "),t.mimeHandled){var d={id:t.tabId,url:t.url};s.tab=d}const h=`chrome-extension://${chrome.runtime.id}/browser/js/popup.html`,f=`chrome-extension://${chrome.runtime.id}/${p}`,b=`chrome-extension://${chrome.runtime.id}/resources/SidePanel/index.html`,v=s.url.split("?")[0];if([h,f,b].includes(v)&&(s.tab=t.tab,delete t.tab),s&&s.tab&&(t.tabId=s.tab.id,this.activeTab||(this.activeTab=s.tab.id),t.main_op)){switch(n=t.main_op,delete t.main_op,a.logBrowserAnalytics(t),this.tabs[t.tabId]&&this.tabs[t.tabId].suppress_frictionless&&(t.suppress_frictionless=this.tabs[t.tabId].suppress_frictionless),t.version=this.version,t.NMHConnStatus=this.NMHConnStatus,n){case"embedded-pdf-touch-point-added":chrome.tabs.sendMessage(t.tabId,{type:"added-embedded-pdf-touch-point",frameId:t?.frameId,position:t?.position}),chrome.tabs.sendMessage(t.tabId,{action:"reRenderShowOneChild"});break;case"hideAIMarkerPopup":chrome.tabs.sendMessage(t.tabId,{content_op:"hideAIMarkerPopup",href:t.href});break;case"updateIframeHeight":chrome.tabs.sendMessage(t.tabId,{content_op:"updateIframeHeight",href:t.href,menuOpen:t.menuOpen});break;case"whatsapp-express-touch-point-added":case"outlook-touch-point-added":chrome.tabs.sendMessage(t.tabId,{action:"reRenderShowOneChild"});break;case"log-error":_.error(t.log);break;case"log-info":_.info(t.log);break;case"embedded-pdf-touch-point-fte":chrome.tabs.sendMessage(t.tabId,{type:"add-fte-for-embedded-pdf-touch-point"},{frameId:t.frameId});break;case"outlook-fte-render":chrome.tabs.sendMessage(t.tabId,{type:"add-fte-for-outlook-touch-point"});break;case"changeDefaultViewershipForSurface":this.handleChangeDefaultViewershipForSurface(t);break;case"acrobat-gmail-fte-config":this.gmailFteConfigUpdated(t);break;case"get-express-modal-session-id":r({sessionId:g(t.tabId)});break;case"express-init":this.expressInit(r);break;case"whatsapp-express-init":this.whatsappExpressInit(r);break;case"gmail-express-init":this.gmailExpressInit(r);break;case"google-image-preview-express-init":this.googleImagePreviewExpressInit(r);break;case"gmail-init":this.gmailInit(r);break;case"acrobat-gdrive-fte-state":this.gdriveFteStateUpdated(t);break;case"gdrive-init":this.gdriveInit(r,t.tabId);break;case"outlook-init":this.outlookInit(r);break;case"embedded-pdf-touch-point-config":this.embeddedPDFTouchPointConfig(t,r,s);break;case"gdrive-download-init":this.gDriveDownloadPageInit(r);break;case"viewer-preview":return this.sendViewerPreviewInformation(t,r),!0;case"get-features&groups":return i.getFeaturesAndGroups(t.cachePurge).then((e=>r(e))),!0;case"getFloodgateFlag":i.hasFlag(t.flag,t.cachePurge).then(r);break;case"getFloodgateMeta":r(i.getFeatureMeta(t.flag));break;case"openRecentFileLink":let d=t.recent_file_url;new URLSearchParams(t.recent_file_url)?.has("acrobatPromotionSource")&&(d=chrome.runtime.getURL("viewer.html")+"?pdfurl="+encodeURIComponent(t.recent_file_url)+"&pdffilename="+encodeURIComponent(t.file_name)),e.createTab(d);break;case"openOnboardingTutorialFile":let p=new URL(t.onboardingDemoFileURL);p.searchParams.set("blob-uri",t.blobParam),p.searchParams.set("returnURL",t.pdfURL),p.searchParams.set("returnTabId",t.tabId),p=p.toString(),e.createTab(p);break;case"returnToYourFile":const h=`chrome-extension://${chrome.runtime.id}/*`;chrome.tabs.query({url:h},(a=>{let s=a?.find((e=>e.id==t.returnTabId));s?chrome.tabs.update(s.id,{active:!0}):e.createTab(t.returnURL)}));break;case"openLocalFileThoughFilePicker":const f=chrome.runtime.getURL("viewer.html")+"?pdfurl="+encodeURIComponent(t.fileURL)+"&pdffilename="+encodeURIComponent(t.file_name)+(t.openGenAIAssistantPanel?"&ogap="+t.openGenAIAssistantPanel:"")+(t.openLocalFileSource?"&olfs="+t.openLocalFileSource:"");e.createTab(f);break;case"viewer-type":setTimeout((()=>{this.sendMessage({dend_op:"viewer-type",viewer:t.viewer,tabId:t.tabId,is_pdf:!0})}),500);case"init-floodgate":setTimeout((()=>{i.init(),i.getFeaturesAndGroups()}),2e3);break;case"complete_conversion":e.createTab(t.conversion_url);break;case"analytics":break;case"pdf-contentType-event":i.hasFlag("dc-cv-content-type-pdf-analtyics",u.NO_CALL).then((e=>{"true"===c.getItem("pdfViewer")&&e&&a.event(a.e.PDF_CONTENT_TYPE_EVENT)}));break;case"relay_to_content":this.relayMessageToContentScript(t,t.shouldCache??!0,r);break;case"viewer-startup":t.main_op="viewer-startup",this.sendViewerStartupInformation(t,s.tab.url,r);break;case"check-cookie":r(c.getItem(t.key));break;case"set-cookie":c.setItem(t.key,t.value);break;case"check-mime-viewer-availability":o.isMimeHandlerAvailable().then((e=>{e?"false"===c.getItem("pdfViewer")||"true"===c.getItem("cdnFailure")?this.sendMessage({dend_op:"viewer-type",tabId:s.tab.id,viewer:"mime-native",is_pdf:!0}):t.url&&t.url.startsWith("file://")&&(this.isAllowedLocalFileAccess||(a.event(a.e.VIEWER_PDF_LOCAL_FILE_IGNORED),l.sendMessage({dend_op:"viewer-type",tabId:s.tab.id,viewer:"mime-native",is_pdf:!0}))):this.sendMessage({dend_op:"viewer-type",tabId:s.tab.id,viewer:"native",is_pdf:!0})}));break;case"uninstall":o.setViewerState("disabled"),c.setItem("pdfViewer","false"),c.setItem("always-show-pdf-menu","false"),e.mimeReloadAllTabs(),c.removeItem("userEligibleForUninstall"),chrome.runtime.setUninstallURL(""),a.event(a.e.UNINSTALL_DIALOG_UNINSTALLED_SUCCESSFUL),setTimeout((()=>{chrome.management.uninstallSelf()}),1e3);break;case"caret_mode_toggle_handler":chrome.runtime.sendMessage({main_op:"relay_to_content",content_op:"caret_mode_toggle_handler",status:t.toggleCaretModeValue});break;case"initialise-popup":this.initialisePopup(s.tab,r);break;case"addExperimentCodeForAnalytics":x(t.experimentCode);break;case"cancelWebpageConversion":case"clearStatus":this.cancelConversion(this.tabs[s.tab.id]);break;case"triggerBufferSave":chrome.runtime.sendMessage({main_op:"triggerBufferSave"});break;case"closeExpressApp":m(s.tab.id);break;case"toggle-express-touch-points":T(t.allowed);break;case"removeSessionFromUnloadedExpressSessions":F(s.tab.id,t.touchpoint,t.domain);break;case"handleViewerFloodgateResponse":i.handleViewerFloodgateResponse(t.fgResponse);break;case"launch-express":w({srcUrl:t.imgUrl,intent:t.intent,touchpoint:t.touchpoint},s.tab);break;case"local-storage-remove":c.removeItem(t?.key);break;case"getExperimentCodes":r(C());break;default:return y[n]?(a.setOp({preview:"Copy",image_preview:"Image",send:"Send",fillsign:"FillSign",export:"Export",acom:"GotoAcom",to_pdf:"ConvertToPdf"}[t.handleResult]),y[n](t,s,r)):void e.consoleLog("failed to find handler for: "+n)}return!0}}gmailSelectors(){return Promise.resolve((()=>{const e=i.getFeatureMeta("dc-cv-gmail-selectors"),t=i.getFeatureMeta("dc-cv-gmail-selectors-list-view"),a=i.getFeatureMeta("dc-cv-gmail-selectors-native-viewer");let s={};try{s.messageView=JSON.parse(e),s.listView=JSON.parse(t),s.nativeViewer=JSON.parse(a)}catch(e){}return s})())}gmailFteToolTipConfig(){return Promise.resolve((()=>{let e=i.getFeatureMeta("dc-cv-gmail-fte-tooltip");try{e=JSON.parse(e)}catch(t){e={tooltip:{shortCoolDown:7,longCoolDown:60,maxFteCount:-1,resetDay:-1}}}return e})())}initStorageForGSuiteFlows(e,t,a){c.setItem(`${e}-pdf-dv-feature-enablement-status`,t?.toString()),c.setItem(`${e}-pdf-dv-feature-control-enablement-status`,a?.toString()),R(e),c.getItem("cdnUrl")||n.updateVariables(this.version)}fetchFeatureEnablementStatusForDefaultViewership(e){return i.hasFlag(`dc-cv-${e}-default-viewership`)}async gmailInit(t){const[a,s,r,o,n,l,c,d,p,u]=await Promise.all([this.gmailSelectors(),i.hasFlag("dc-cv-gmail-attachment-card-prompt"),i.hasFlag("dc-cv-gmail-selectors-list-view"),i.hasFlag("dc-cv-gmail-fte-tooltip"),i.hasFlag("dc-cv-gmail-drive-link-attachment-prompt"),this.gmailFteToolTipConfig(),chrome.storage.local.get("acrobat-touch-points-in-other-surfaces"),D("gmail"),this.fetchFeatureEnablementStatusForDefaultViewership("gmail"),i.hasFlag("dc-cv-gmail-default-viewership-control")]);this.initStorageForGSuiteFlows("gmail",p,u);t({enableAttachmentCardPromptInGmail:s,enableListViewPromptInGmail:r,enableDriveLinkAttachmentPromptInGmail:n,touchPointSettingEnabled:this.touchPointSetting(c),selectors:a,acrobatPromptText:e.getTranslation("gsuiteOpenWithAcrobat"),gmailFteToolTipStrings:this.getSurfaceFTEToolTipStrings(p,"gmail"),fteConfig:l,enableGmailFteToolTip:o,isAcrobatDefaultForSurface:"true"===d,enableDefaultViewershipFeatureForGmail:p,isUserPartOfExperimentControlOrTreatmentForGmailDV:u||p})}touchPointSetting(e){return"false"!==e["acrobat-touch-points-in-other-surfaces"]}getSurfaceFTEToolTipStringsForGdriveNonDV(){return{title:e.getTranslation("acrobatGsuiteFteToolTipTitleNonDV"),description:e.getTranslation("acrobatGsuiteFteToolTipDescriptionNonDV",e.getTranslation("surfaceNameGdrive")),button:e.getTranslation("closeButton")}}getSurfaceFTEToolTipStrings(t,a){let s,i;return"gmail"===a?i=e.getTranslation("surfaceNameGmail"):"gdrive"===a&&(i=e.getTranslation("surfaceNameGdrive")),s=t&&!c.getItem(`${a}-pdf-default-viewership-user-disabled`)?{title:e.getTranslation("acrobatGsuiteFteToolTipTitleDV"),description:e.getTranslation("acrobatGsuiteFteToolTipDescriptionDV",i),button:e.getTranslation("closeButton")}:{title:e.getTranslation("acrobatGsuiteFteToolTipTitleNonDV"),description:e.getTranslation("acrobatGsuiteFteToolTipDescriptionNonDV",i),button:e.getTranslation("closeButton")},s}async embeddedPDFTouchPointConfig(t,a,s){const r=await i.hasFlag("dc-cv-embedded-pdf-touch-point"),o="false"===c.getItem("acrobat-touch-points-in-other-surfaces"),n="en-US"===c.getItem("locale")||"en-GB"===c.getItem("locale");let l;try{l=JSON.parse(i.getFeatureMeta("dc-cv-embedded-pdf-touch-point"))}catch(e){l={tooltip:{shortCoolDown:7,longCoolDown:60,maxFteCount:-1,resetDay:-1},blockedDomains:["outlook.office.com","attachments.office.net"]}}a({enableEmbeddedPDFTouchPoint:r&&!o&&n,touchPointConfig:l,touchPointText:e.getTranslation("embeddedPDFOpenInAcrobatTooltip"),fteToolTipStrings:{title:e.getTranslation("embeddedPDFTouchPointFTEHeader"),description:e.getTranslation("embeddedPDFTouchPointFTEBody"),button:e.getTranslation("closeButton")},menuItemStrings:{hideIconForNow:e.getTranslation("embeddedPDFHideIconForNow"),openPDFInAcrobat:e.getTranslation("embeddedPDFOpenPDFInAcrobat")},tabId:t?.tabId,frameId:s?.frameId})}async outlookInit(t){const[a,s]=await Promise.all([i.hasFlag("dc-cv-outlook-pdf-touch-point"),i.hasFlag("dc-cv-outlook-pdf-touch-point-control")]);let r,o,n;const l="false"===c.getItem("acrobat-touch-points-in-other-surfaces"),d="en-US"===c.getItem("locale")||"en-GB"===c.getItem("locale");try{const e=JSON.parse(i.getFeatureMeta("dc-cv-outlook-pdf-touch-point"));r=e?e.tooltip:{},o=e?e.selectors:{},n=!!e&&e.fteEnabled}catch(e){r={shortCoolDown:7,longCoolDown:60,maxFteCount:-1,resetDay:-1}}d&&a?x("OT"):d&&s?x("OTC"):(A("OT"),A("OTC")),t({enableOutlookPDFTouchPoint:a&&!l&&d,selectors:o,fteConfig:r,touchPointString:e.getTranslation("gsuiteOpenWithAcrobat"),fteToolTipStrings:{title:e.getTranslation("outlookPDFTouchPointFTEHeader"),description:e.getTranslation("outlookPDFTouchPointFTEBody"),button:e.getTranslation("closeButton")},errorToastStrings:{title:e.getTranslation("outlookErrorToastTitle"),description:e.getTranslation("outlookErrorToastDescription")},enableOutlookFteTooltip:n})}async gdriveInit(t){const[a,s,r,o,n,l,d,p,u,m,g,h,f,b,v,I,E,w,F]=await Promise.all([i.hasFlag("dc-cv-gdrive-open-in-extension"),i.getFeatureMeta("dc-cv-gdrive-selectors"),i.hasFlag("dc-cv-gdrive-fte-tooltip"),i.getFeatureMeta("dc-cv-gdrive-fte-tooltip"),i.hasFlag("dc-cv-gdrive-grid-view-touchpoint"),i.getFeatureMeta("dc-cv-gdrive-grid-view-selectors"),i.hasFlag("dc-cv-gdrive-list-view-touchpoint"),i.getFeatureMeta("dc-cv-gdrive-list-view-selectors"),i.getFeatureMeta("dc-cv-gdrive-pdf-selectors"),i.hasFlag("dc-cv-gdrive-triple-dot-menu-analytics"),i.hasFlag("dc-cv-gdrive-list-grid-view-fte-tooltip"),chrome.storage.local.get("acrobat-touch-points-in-other-surfaces"),i.hasFlag("dc-cv-gdrive-grid-view-touchpoint-migration"),i.hasFlag("dc-cv-gdrive-list-view-touchpoint-migration"),i.hasFlag("dc-cv-gdrive-search-bar-analytics"),i.getFeatureMeta("dc-cv-gdrive-search-selectors"),D("gdrive"),this.fetchFeatureEnablementStatusForDefaultViewership("gdrive"),i.hasFlag("dc-cv-gdrive-default-viewership-control")]);let T,_,S,P,R,C,x;try{T=s?JSON.parse(s):{},_=o?JSON.parse(o):{},S=l?JSON.parse(l):{},P=p?JSON.parse(p):{},R=I?JSON.parse(I):{},x=u?JSON.parse(u):{},S.pdfSvgPath=x?.svg,P.pdfSvgPath=x?.svg,T.GoogleDriveListView=P,T.GoogleDriveGridView=S,T.GoogleDriveSearchBar=R,C=this.touchPointSetting(h)}catch(e){_={tooltip:{shortCoolDown:7,longCoolDown:60,maxFteCount:-1,resetDay:-1}},C=!1}const A=n||f,L=d||b;n&&c.setItem("gvt","dc-cv-gdrive-grid-view-touchpoint"),d&&c.setItem("lvt","dc-cv-gdrive-list-view-touchpoint"),t({acrobatTouchPointEnabled:C,enableOpenInExtension:a,acrobatPromptText:e.getTranslation("gsuiteOpenWithAcrobat"),selectors:T,fteToolTipStrings:this.getSurfaceFTEToolTipStrings(w,"gdrive"),fteToolTipStringsForNativeViewNonDV:this.getSurfaceFTEToolTipStringsForGdriveNonDV(),enableGDriveGridViewTouchPoint:A,enableGDriveListViewTouchPoint:L,enableGDriveTripleDotMenuAnalytics:m,enableGDriveSearchBarAnalytics:v,fteConfig:_,enableFteToolTip:r,enableFteToolTipForListGridView:g,isAcrobatDefaultForSurface:"true"===E,enableDefaultViewershipFeatureForGoogleDrive:w,isUserPartOfExperimentControlOrTreatmentForGoogleDriveDV:F||w}),this.initStorageForGSuiteFlows("gdrive",w,F)}gdriveFteStateUpdated(e){chrome.tabs.query({url:["https://drive.google.com/*"]},(function(t){t?.forEach((function(t){t.id!==e.tabId&&chrome.tabs.sendMessage(t.id,{content_op:"acrobatGdriveFteStateUpdated",fteState:e?.fteState})}))}))}async gDriveDownloadPageInit(e){const[t,a]=await Promise.all([i.getFeatureMeta("dc-cv-gdrive-download-page-selectors"),chrome.storage.local.get("acrobat-touch-points-in-other-surfaces")]);let s,r;try{s=JSON.parse(t),r=this.touchPointSetting(a)}catch(e){r=!1}e({selectors:s,acrobatTouchPointEnabled:r})}gmailFteConfigUpdated(e){chrome.tabs.query({url:["https://mail.google.com/*"]},(function(t){t?.forEach((function(t){t?.id!==e.tabId&&chrome.tabs.sendMessage(t.id,{content_op:"acrobatGmailFteStateUpdated",fteState:e?.fteState})}))}))}async handleChangeDefaultViewershipForSurface(e){if(!await this.fetchFeatureEnablementStatusForDefaultViewership(e.surfaceId))return;const t={gmail:"https://mail.google.com/*",gdrive:"https://drive.google.com/*"},s=`${e.surfaceId}-pdf-default-viewership`;if(c.getItem(s)!==e.isDefault?.toString()){const i=s+"-user-disabled";e.isDefault?e.isDefault&&(c.removeItem(i),a.event(`DCBrowserExt:${e.surfaceId}:DefaultViewership:Enabled`,{eventContext:e.source}),S(e.surfaceId)):(c.setItem(i,"true"),P(e.surfaceId).then((t=>{a.event(`DCBrowserExt:${e.surfaceId}:DefaultViewership:Disabled`,{dvDisableSessionCount:t,eventContext:e.source})}))),c.setItem(s,e.isDefault?.toString()),R(e.surfaceId),chrome.tabs.query({url:t[e.surfaceId]},(function(t){t?.forEach((t=>{e.tabId===t.id&&e.syncToOtherTabsOnly||chrome.tabs.sendMessage(t.id,{content_op:"changeDefaultViewershipForSurface",isDefault:e.isDefault,surfaceId:e.surfaceId,source:e.source})}))}))}}async expressInit(t){const[a]=await Promise.all([h()]),s=a.enableExpressContextMenu,r=a.enableExpressOptionsPagePreference,o=a.enableExpressTooltip,n=i.getFeatureMeta("dc-cv-express-context-menu-tooltip");let l;try{l=JSON.parse(n)}catch(e){l={minimumImageDimension:{height:100,width:150},imageCount:5}}const c=i.getFeatureMeta("dc-cv-express-context-menu-tooltip-allow-list");let d={allowListArray:[]};if(c)try{d.allowListArray=JSON.parse(c)}catch(e){}t({enableExpressContextMenu:s,shouldEnableExpressTouchpointsPreference:r,showExpressTooltip:o,imageDimension:l.minimumImageDimension,imageCount:l.imageCount,allowInfo:d,expressTooltipStrings:{title:e.getTranslation("expressFteTitle"),description:e.getTranslation("expressFteDescription"),footer:e.getTranslation("expressFTEFooter"),popoverTitle:e.getTranslation("expressPopoverFTETitle"),popoverDescription:e.getTranslation("expressPopoverFTEDescription")}})}async whatsappExpressInit(t){const a=await I(),s=await v(),r=i.getFeatureMeta("dc-cv-express-whatsapp-preview"),o=i.getFeatureMeta("dc-cv-express-whatsapp-hover");let n,l;try{n=JSON.parse(r)}catch(e){n=null}try{l=JSON.parse(o)}catch(e){l=null}const c=await i.hasFlag("dc-cv-express-whatsapp-fte");t({enableWhatsappPreviewExpressMenu:a.enableWhatsappPreviewExpressMenu,enableWhatsappHoverExpressMenu:s.enableWhatsappHoverExpressMenu,enableWhatsappPreviewExpressFte:c,enableExpressOptionsPagePreference:a.enableExpressOptionsPagePreference||s.enableExpressOptionsPagePreference,selectors:{...n?.selectors,...l?.selectors},fteStrings:{title:e.getTranslation("expressContextualFteTitle","WhatsApp"),description:e.getTranslation("expressContextualFteDescription"),ctaLabel:e.getTranslation("expressContextualFteCtaLabel")}})}async gmailExpressInit(e){const t=await f(),a=await E(),s=i.getFeatureMeta("dc-cv-express-gmail-native-viewer"),r=i.getFeatureMeta("dc-cv-express-gmail-message-view"),o=await i.hasFlag("dc-cv-express-gmail-nv-single-cta");let n,l;try{n=JSON.parse(s)}catch(e){n=null}try{l=JSON.parse(r)}catch(e){l=null}e({...t,...a,enableExpressOptionsPagePreference:t.enableExpressOptionsPagePreference||a.enableExpressOptionsPagePreference,selectors:{...n?.selectors,...l?.selectors},singleCTAEnabled:o})}async googleImagePreviewExpressInit(e){const t=await b(),a=i.getFeatureMeta("dc-cv-express-google-image-preview"),s=await i.hasFlag("dc-cv-express-google-image-preview-single-cta");let r;try{r=JSON.parse(a)}catch(e){r=null}e({...t,selectors:r?.selectors,singleCTAEnabled:s})}async initialisePopup(a,s){let i={hostedURL:t.getPopupCDNUrl()};if(this.tabs[a.id]){i=e.extend(i,this.tabs[a.id]);const t=`chrome-extension://${chrome.runtime.id}`;a.url.startsWith(t)&&(i.is_extension_url=!0);["chrome://extensions/",t,"chrome://newtab/","https://chrome.google.com","https://acrobat.adobe.com/link/file/?uri=","https://acrobat.adobe.com/id/urn:","https://acrobat.adobe.com/link/review/?uri="].some((e=>a.url.startsWith(e)))&&(i.isBlacklistedUrl=!0);if(!["downloading","pdf_downloading","waiting","in_progress","complete"].includes(i.current_status)||i.isBlacklistedUrl){const e=i.is_pdf;this.clearStatus(i),i.is_pdf=e}e.isLocalFileUrl(a.url)&&(i.is_pdf=!0,i.isFillnSignEnabled=l.FILL_N_SIGN_ENABLED),i.isAcrobat=e.isAcrobatAvailable(this.version),i.autoOpenPDFInAcrobat="false"!==c.getItem("ViewResultsPref")}s(i)}echoRequest(t){var s,i,r;if(!(l.CHROME_VERSION<l.SUPPORTED_VERSION)){if(!this.avoidUrl(t.url)){var o=t.id||t.tabId;return this.tabs[o]&&("cancelled"!==(s=this.tabs[o]).current_status&&"pdf_opened"!==s.current_status&&"pdf_failure"!==s.current_status&&"viewer_menu"!==s.panel_op||(r=s.is_pdf,this.clearStatus(s),s.is_pdf=r),s.current_status&&(s.panel_op="status"),i=s.is_pdf?"pdf_menu":"html_menu",s.panel_op&&"load-frictionless"===s.panel_op&&(delete s.panel_op,a.event(a.e.FRICTIONLESS_WIDGET_CLOSED)),"resize_window"===s.content_op&&(delete s.content_op,delete s.window_height),s.panel_op=s.panel_op||i,"html_menu"===i&&(s.persist=!1),s.incognito=t.incognito,e.isLocalFileUrl(t.url)&&(s.panel_op="pdf_menu",s.is_pdf=!0,s.isFillnSignEnabled=l.FILL_N_SIGN_ENABLED),t.url.startsWith(`chrome-extension://${chrome.runtime.id}`)?(s.panel_op="viewer_menu",s.is_viewer=!0):s.is_viewer=!1,e.consoleLog("repeat cached request: "+s.panel_op)),s}this.disable(t.id)}}setGlobals(e){this.globals=e}dump(t,a){var s,i=[a];for(s in t)t.hasOwnProperty(s)&&i.push("  "+s+": "+t[s]);e.consoleLog(i.join("\n"))}sendMessage(t,s=!0,i){var r,o=t.tabId;this.dump(t,"Sending message:"),t.version=this.version,e.consoleLog("sendMessage version",t),s&&(this.tabs[o]=e.extend(this.tabs[o],t)),t.NMHConnStatus=this.NMHConnStatus,t.show_frictionless=!0,t.is_edge=e.isEdge(),"flickr"===t.panel_op&&(r=a.e.FLICKR_OFFER_SHOWN),r&&a.checkAndLogAnalytics(r),e.sendMessage(t,this.globals,i),delete t.mimeHandled,delete this.tabs[o].mimeHandled}deferMessage(e){"undefined"==typeof setTimeout?this.sendMessage(e):setTimeout(this.proxy(this.sendMessage,e),0)}sendMessageToPopup(t,a=!0){const s=t.tabId;t.version=this.version,a&&(this.tabs[s]=e.extend(this.tabs[s],t)),t.NMHConnStatus=this.NMHConnStatus,t.is_edge=e.isEdge(),chrome.runtime.sendMessage(t)}filenameFromUrl(e){try{const t=decodeURIComponent(e),a=t.split("?")[0].split("/").filter((e=>e.length>0)),s=a.length>0?a[a.length-1]:"untitled";let i=s;const r=s.length-4;return(s.length<4||s.toLowerCase().indexOf(".pdf")!==r)&&(i+=".pdf"),i}catch(e){return"untitled.pdf"}}async pdf_menu(t,s){var i;if(delete(i=this.tabs[s.tab.id]=e.extend(this.tabs[s.tab.id],{tabId:s.tab.id})).dend_op,i.filename=this.filenameFromUrl(t.url),i.panel_op="pdf_menu",i.url=t.url,i.viewer=t.viewer,i.incognito=s.tab.incognito,i.fteFeatureFlag=t.fteFeatureFlag,i.isFillnSignEnabled=l.FILL_N_SIGN_ENABLED,i.isSharePointEnabled=r.isFeatureEnabled(),1==i.isSharePointEnabled&&(i.isSharePointURL=r.isAllowListedSharePointURL(i.url)),1==t.persist){const e=await this.isEnablePersistMenu(t);i.persist=!!e}else i.persist=!1;const o=c.getItem("fteDenied");var n=!(0==t.version||1==t.version||t.version===l.READER_VER||t.version===l.ERP_READER_VER);const d=l.VIEWER_ENABLED&&!c.getItem("pdfViewer")&&(!o||10!==parseInt(o))&&(!n||l.VIEWER_ENABLED_FOR_ACROBAT);if("false"!==c.getItem("always-show-pdf-menu")&&"mime"!==i.viewer&&(1!=i.persist||d||(delete i.fteFeatureFlag,a.event(a.e.PERSIST_PDF_MENU_SHOWN)),this.deferMessage(i)),d){let e,s=c.getItem("repromptCount");switch(t.fteFeatureFlag){case"dc-cv-fte-pdf-redcard":e=a.e.PERSIST_PDF_MENU_RED_CARD_FTE_SHOWN;break;case"dc-cv-fte-pdf-dmb":e=a.e.PERSIST_PDF_MENU_DMB_FTE_SHOWN;break;default:e=a.e.PERSIST_PDF_MENU_FTE_SHOWN}s?a.event(e,{LAUNCH:"Reprompt"}):a.event(e,{LAUNCH:"Launch"})}if(!i.incognito)if("true"!==c.getItem("pdfViewer")||"native"!==i.viewer&&"mime-native"!==i.viewer){if(!c.getItem("pdfViewer")||"false"===c.getItem("pdfViewer")){let e="Acrobat";this.legacyShim()?e="NoApp":this.version!==l.READER_VER&&this.version!==l.ERP_READER_VER||(e="Reader"),"native"!==i.viewer&&"mime-native"!==i.viewer||a.event(a.e.VIEWER_DISABLED_PDF_OPEN_NATIVE_VIEWER,{APPLICATION:e})}}else a.event(a.e.VIEWER_ENABLED_PDF_OPEN_NATIVE_VIEWER)}loaded(t){this.tabs[t]=e.extend(this.tabs[t],{tabId:t,loaded:!0})}setIsPDF(t,a){this.tabs[t]=e.extend(this.tabs[t],{tabId:t,is_pdf:a})}cancelConversion(e){this.getModule("acro-web2pdf").cancelConversion(e.tabId),delete e.current_status,delete e.file_path,delete e.domtitle,delete e.timing,delete e.panel_op,delete e.is_pdf,delete e.newUI}clearStatus(e,t){"in_progress"!==e.current_status&&"downloading"!==e.current_status&&(t||"waiting"!==e.current_status)&&this.cancelConversion(e)}loading(t){var a=t.id;this.tabs[a]=e.extend(this.tabs[a],{tabId:a,loaded:!1}),this.clearStatus(this.tabs[a],!0)}async active(t){this.activeTab=t.tabId,this.tabs[this.activeTab]=e.extend(this.tabs[this.activeTab],{tabId:t.tabId});if(await this.isEnablePersistMenu()){var a=this.echoRequest(this.tabs[this.activeTab]);a&&a.persist&&this.sendMessage(a)}}disable(e){this.tabs[e]&&(this.tabs[e].loaded=!1)}close(e){this.getModule("acro-web2pdf").cancelConversion(e),delete this.tabs[e],this.activeTab===e&&(this.activeTab=null),L.cleanTemporaryURLBufferEntry(e)}tabReplace(e,t){this.close(t),this.loaded(e)}activeTab(){return this.activeTab}noop(){}avoidUrl(t){if(t=t||"",l.VIEWER_ENABLED&&c.getItem("pdfViewer")&&e.isViewerURL(t))return!1;if(this.version===l.ERP_READER_VER)return!0;if(t.startsWith("https://chrome.google.com"))return!0;if(this.version==l.READER_VER&&0==l.FRICTIONLESS_ENABLED)return!t.endsWith(".pdf")&&!t.endsWith(".PDF");if((0==this.version||1==this.version&&0==this.NMHConnStatus)&&!1===l.FRICTIONLESS_ENABLED)return!1;c.getItem("appLocale");const a=this.isAllowedLocalFileAccess&&e.isLocalFileUrl(t);return!t.startsWith("http")&&!a}}N||(N=new O,N.registerHandlers({"send-analytics":N.proxy(N.noop)}));export const communicate=N;