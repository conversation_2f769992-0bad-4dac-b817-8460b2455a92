/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{communicate as e}from"./communicate.js";import{util as t}from"./util.js";import{analytics as o,events as n}from"../common/analytics.js";import{SETTINGS as a}from"./settings.js";import{dcLocalStorage as i,dcSessionStorage as s}from"../common/local-storage.js";import{floodgate as r}from"./floodgate.js";import{privateApi as l}from"./private-api.js";import{common as c}from"./common.js";import{userSubscription as m}from"./user-subscription.js";import{userDetailsAcrobat as d}from"./acrobatUserDetails.js";import{downloadBannerExcludeList as u,LOCAL_FTE_WINDOW as p,ONE_DAY_IN_MS as g}from"../common/constant.js";import{CACHE_PURGE_SCHEME as I,EXPRESS as f}from"./constant.js";import{versionChecks as h}from"./handleVersionChecks.js";import{sendPingEventHandler as E,registerUninstallUrl as w}from"../common/util.js";import{loggingApi as _}from"../common/loggingApi.js";import{offscreenActions as T}from"./offscreen-actions.js";import{viewerModuleUtils as N}from"./viewer-module-utils.js";import{launchExpress as L,toggleExpressTouchpoints as C}from"./express.js";import{indexedDBScript as A}from"../common/indexDB.js";import{floodgateMigration as O}from"./floodgateMigration.js";import{setExperimentCodeForAnalytics as S}from"../common/experimentUtils.js";var F,D,v=new Promise((function(e){D=e})),b={},R=()=>e.getModule("acro-web2pdf"),P=()=>e.getModule("acro-gstate"),M=["http://*/*","https://*/*"];const y="Error in Local File Prompt";async function U(){!async function(){const[e,t]=await Promise.all([chrome.tabs.query({url:"https://mail.google.com/*"}),chrome.tabs.query({url:"https://drive.google.com/*"})]);e?.length>0&&o.event("DCBrowserExt:ExtensionUpdate:Gmail:Open"),t?.length>0&&o.event("DCBrowserExt:ExtensionUpdate:GDrive:Open")}();for(const e of chrome.runtime.getManifest().content_scripts){const t=await chrome.tabs.query({url:e.matches});for(const o of t)(o.url.includes("mail.google.com")||o.url.includes("drive.google.com"))&&(e.css&&chrome.scripting.insertCSS({files:e.css,target:{tabId:o.id}}),chrome.scripting.executeScript({files:e.js,target:{tabId:o.id,allFrames:e.all_frames}}))}}async function x(e){try{if(chrome.sidePanel.setOptions({enabled:!1}),!i.getItem("installUuid")){const e=t.generateUUID();i.setItem("installUuid",e)}e&&(e.reason===chrome.runtime.OnInstalledReason.UPDATE&&e.previousVersion!==chrome.runtime.getManifest().version?(t.verCmp(e.previousVersion,"*********")<0&&await async function(){let e;try{const o=await chrome.tabs.query({});await Promise.allSettled(o.map((({id:e})=>chrome.scripting.executeScript({target:{tabId:e},files:["content_scripts/injectCopyLSIframe.js"]})))),await t.sleep(300),e=await chrome.runtime.sendMessage({main_op:"copy-ls"}),o.map((({id:e})=>chrome.tabs.sendMessage(e,{content_op:"remove-lsCopy"}).catch((()=>null))))}catch(e){}finally{"succeed"!==e?(i.setItem("retryOnNextPage",!0),o.event(o.e.LOCAL_STORAGE_MIGRATION_FAILED)):o.event(o.e.LOCAL_STORAGE_MIGRATION_SUCCESS)}}(),i.getItem("installVersion")||i.setItem("installVersion",e.previousVersion),i.setItem("installType",e.reason),i.getItem("thirdPartyCookieChecked")&&i.removeItem("thirdPartyCookieChecked"),i.getItem("tpcBlockAnalyticsEnable")&&i.removeItem("tpcBlockAnalyticsEnable")):e.reason===chrome.runtime.OnInstalledReason.INSTALL&&((()=>{const e=`https://chromewebstore.google.com/detail/*/${chrome.runtime.id}*`,t=`https://microsoftedge.microsoft.com/addons/detail/*/${chrome.runtime.id}*`;chrome.tabs.query({url:[e,t]},(e=>{if(chrome.runtime.lastError)F=null;else for(let t in e){const o=new URLSearchParams(new URL(e[t].url).search);if(o.has("mv")){F=encodeURIComponent(o.get("mv"));break}}}))})(),i.setItem("installVersion",chrome.runtime.getManifest().version),i.setItem("installType",e.reason)),i.getItem("offlineSupportDisable")||i.setItem("offlineSupportDisable",!0)),v.then((({env:t,msg:n})=>{const s=i.getItem("installSource");if(i.setItem("installSource",t.installType),w(),A.clearExpiredBlobBuffers().catch((e=>{_.error({message:"Failed to clear old file buffer entries on startup",error:e})})),A.clearFileHashOldEntries().catch((e=>{_.error({message:"Failed to clear old blob cache entries on startup",error:e})})),"true"===n.repromptDone&&o.event(o.e.EXTENSION_REPROMPT_TRACKING,{REASON:e.reason,TYPE:t.installType}),e.reason===chrome.runtime.OnInstalledReason.UPDATE&&e.previousVersion!==chrome.runtime.getManifest().version)U(),O.init(),o.event(o.e.EXTENSION_UPDATE,{installReason:e.reason,previousVersion:e.previousVersion||"none"}),G(t,!0,s);else if(e.reason===chrome.runtime.OnInstalledReason.INSTALL||e.previousVersion===chrome.runtime.getManifest().version){switch(U(),o.event(o.e.EXTENSION_INSTALLED,{installReason:e.reason,previousVersion:e.previousVersion||"none"}),o.logEventOnce(o.e.EXTENSION_INSTALLED_NEW,{storageKey:"installAnalyticsLogged",installReason:e.reason,previousVersion:e.previousVersion||"none"}),t.installType){case"admin":o.event(o.e.EXTENSION_INSTALLED_ADMIN);break;case"development":o.event(o.e.EXTENSION_INSTALLED_DEVELOPMENT);break;case"other":o.event(o.e.EXTENSION_INSTALLED_OTHER);break;case"normal":l.isInstalledViaUpsell().then((e=>{if(e)o.event(o.e.EXTENSION_INSTALLED_UPSELL);else{o.event(o.e.EXTENSION_INSTALLED_DIRECT);let e="store_direct";F&&(e=decodeURIComponent(F)),o.event(o.e.EXTENSION_INSTALLED_SOURCE,{SOURCE:e})}}));break;case"sideload":o.event(o.e.EXTENSION_INSTALLED_SIDE_LOADED),n.installMonth&&n.installYear?o.event(o.e.EXTENSION_INSTALLED_SIDE_LOADED_MONTH_YEAR,{MONTH:n.installMonth,YEAR:n.installYear}):o.event(o.e.EXTENSION_INSTALLED_SIDE_LOADED_MONTH_YEAR,{MONTH:"None",YEAR:"None"}),a.IS_READER&&o.event(o.e.EXTENSION_INSTALLED_SIDE_LOADED_SOURCE,{SOURCE:n.source});break;default:o.event(o.e.EXTENSION_INSTALLED_DEFAULT)}G(t)}}))}catch(e){}}function B(){try{if(navigator.onLine||!1===i.getItem("offlineSupportDisable")){const e=i.getItem("pdfViewer"),t=i.getItem("killSwitch"),n=i.getItem("cdnUrl");"false"===e&&"on"===t&&async function(e){const t=new AbortController,o=t.signal;let n=!1;setTimeout((()=>{n||t.abort()}),5e3);const a=await fetch(e,{signal:o});if(n=!0,200===a.status)return await a.text();return new Error(a.statusText)}(n).then((e=>{-1===e.toString().indexOf("<meta name='killSwitch' content='off'/>")&&-1===e.toString().indexOf('<meta name="killSwitch" content="off"/>')||(i.setItem("pdfViewer",!0),l.setViewerState("enabled"),i.setItem("killSwitch","off"),o.event(o.e.VIEWER_KILL_SWITCH_OFF_SUCCESS))})).catch((e=>{o.event(o.e.VIEWER_KILL_SWITCH_OFF_FAILED)}))}}catch(e){o.event(o.e.VIEWER_KILL_SWITCH_OFF_FAILED)}}function V(e){return t&&t.isChromeOnlyMessage(e)&&t.isEdge()&&(e+="Edge"),t&&t.getTranslation?t.getTranslation(e):chrome.i18n.getMessage(e)}function W(e){return(e.title||V("web2pdfUntitledFileName")).replace(/[<>?:|\*"\/\\'&\.]/g,"")}function k(e,o){if(!e&&!o)return!1;try{const t=e.pageUrl||o.url,n=new URL(t);if(n.protocol&&["http:","https:"].includes(n.protocol))return!0}catch(e){t.consoleError(e)}return!1}async function H(e,t){chrome.tabs.sendMessage(t.id,{type:"removeActionableCoachmark"}),i.setItem("touchpoint",e.touchpoint),o.event(`${o.e.OPEN_SIDE_PANEL_RECIEIVED}:${e.touchpoint||"Unspecified"}`);try{await chrome.sidePanel.open({tabId:t.id}),_.info({message:"Opening sidepanel success",touchpoint:e.touchpoint})}catch(t){_.error({message:"Opening sidepanel failed",error:t.toString(),touchpoint:e.touchpoint})}}function j(e,t){E(),"convertPageContextMenu"===e.menuItemId?function(e,t){k(e,t)&&(o.event(o.e.CONTEXT_MENU_CONVERT_PAGE),R().handleConversionRequest({tabId:t.id,caller:P().web2pdfCaller.MENU,action:P().web2pdfAction.CONVERT,context:P().web2pdfContext.PAGE,url:e.pageUrl||t.url,domtitle:W(t)}))}(e,t):"appendPageContextMenu"===e.menuItemId?function(e,t){k(e,t)&&(o.event(o.e.CONTEXT_MENU_APPEND_PAGE),R().handleConversionRequest({tabId:t.id,caller:P().web2pdfCaller.MENU,action:P().web2pdfAction.APPEND,context:P().web2pdfContext.PAGE,url:e.pageUrl||t.url,domtitle:W(t)}))}(e,t):"convertLinkTargetToPDFContextMenu"===e.menuItemId?function(e,t){k(e,t)&&(o.event(o.e.CONTEXT_MENU_CONVERT_LINK),R().handleConversionRequest({tabId:t.id,caller:P().web2pdfCaller.MENU,action:P().web2pdfAction.CONVERT,context:P().web2pdfContext.LINK,url:e.linkUrl,domtitle:W(t)}))}(e,t):"appendLinkTargetToExistingPDFContextMenu"===e.menuItemId?function(e,t){k(e,t)&&(o.event(o.e.CONTEXT_MENU_APPEND_LINK),R().handleConversionRequest({tabId:t.id,caller:P().web2pdfCaller.MENU,action:P().web2pdfAction.APPEND,context:P().web2pdfContext.LINK,url:e.linkUrl,domtitle:W(t)}))}(e,t):(Object.values(f.VERBS).includes(e.menuItemId.replace("ContextMenu",""))||"expressMenu"===e.menuItemId)&&function(e,t){if(!k(e,t))return;const n=new URL(t.url).hostname;"expressMenu"===e.menuItemId?e.intent="editImage":e.intent=e?.menuItemId?.replace("ContextMenu",""),o.event(o.e.CONTEXT_MENU_EXPRESS_VERB,{domain:n,TARGET:e?.linkUrl?"Link":"Image",VERB:e?.intent}),i.setItem(f.CONTEXT_MENU_INTERACTION_DONE,"true"),e.touchpoint="ContextMenu",L(e,t)}(e,t)}function G(e,n=!1,a=!1){"false"!==i.getItem("fte")&&(n&&i.getItem("pdfViewer")||(i.getItem("installTimestamp")||i.setItem("installTimestamp",Date.now()),setTimeout((()=>{chrome.storage.managed.get("OpenHelpx",(function(s){let r=!s||"false"!==s.OpenHelpx;o.event(r?o.e.VIEWER_FTE_OPEN_HELPX_ENABLED:o.e.VIEWER_FTE_OPEN_HELPX_DISABLED),n&&o.event(o.e.USE_ACROBAT_DEF_OWNERSHIP_ON_UPDATE),function(){try{i.getItem("pdfViewer")||(i.setItem("viewer-enabled-source","ownership-install"),i.setItem("pdfViewer","true"),l.setViewerState("enabled"),t.isEdge()?(m.initiateUserPolling(),o.event(o.e.USE_ACROBAT_IN_EDGE_AUTO_ENABLED)):o.event(o.e.USE_ACROBAT_IN_CHROME_AUTO_ENABLED))}catch(e){o.event(o.e.LOCAL_STORAGE_DISABLED)}}(),i.setItem("fte","false"),a&&(r=!1);const d=t.isEdge()&&("admin"===e.installType||"sideload"===e.installType);r&&!d&&async function(){let e=chrome.i18n.getMessage("@@ui_locale");["ca","da","en_GB","es","fi","hr","it","ko","nl","pt_BR","ru","sl","tr","zh_CN","cs","de","en_US","eu","fr","hu","ja","nb","pl","ro","sk","sv","uk","zh_TW"].includes(e)||(e="en_US");const o=t.isEdge()?"Edge":"Chrome";return`${c.getWelcomePdfUrlHost()}/dc-chrome-extension/mv/${e}/Acrobat-for-${o}.pdf`}().then((e=>t.createTab(e,(e=>{const t=(new Date).getTime();b={...e,timestamp:t},chrome.tabs.onUpdated.addListener((function t(n,a){e.id==n&&"complete"===a.status&&(o.event(o.e.WELCOME_PDF_LOADED),chrome.tabs.onUpdated.removeListener(t))}))}))))}))}),2e3)))}function X(){const e="true"===i.getItem("pdfViewer")?"enabled":"disabled";l.setViewerState(e)}function $(e){const t=e.UsageMeasurement,o=t&&"false"===t.newValue?"false":"true";i.setItem("ANALYTICS_OPT_IN_ADMIN",o)}function Y(){const e=i.getItem("pdfViewer");let t="neverTaken";return"true"===e?t="enabled":"false"===e&&(t="disabled"),t}async function q(e){const t=i.getItem("localFileConfig");t?e&&!t?.localFilePermission&&function(e,t){K(t?.promptCount>0?o.e.LOCAL_FILE_PROMPT_ACCESS_ALREADY_GRANTED:o.e.LOCAL_FILE_PROMPT_NO_SHOW_ACCESS_GRANTED);t.localFilePermission=e,i.setItem("localFileConfig",t)}(e,t):async function(e){const t=await le(),n={promptCount:0,localFilePermission:e,eligibleDate:new Date(Date.now()).toISOString()};i.setItem("localFileConfig",n),function(e){if("localFileFte"===e)return;let t;switch(e){case"localFilePrompt":t="LFP";break;case"localFileBlockingPrompt":t="LFF";break;case"localFileFteControl":t="LFC";break;default:_.error({message:y+`setLocalFilePromptExperimentAnalyticsCode: Invalid localFileCohort ${e}`})}S(t)}(t),o.event(o.e.LOCAL_FILE_PROMPT_LANDING_IN_COHORT)}(e)}function K(e){if(!e)return;const t=new Date,n=`${t.getUTCFullYear()}${(t.getUTCMonth()+1).toString().padStart(2,"0")}`;chrome.storage.local.get([e],(t=>{const a=t[e]?.lastSentYearMonth;(!a||n>a)&&(o.event(e),chrome.storage.local.set({[e]:{lastSentYearMonth:n}}))}))}async function J(){try{const e=await chrome.extension.isAllowedFileSchemeAccess();await q(e);const{tabId:t,url:a}=i.getItem("localFileFteData");i.removeItem("localFileFteData");const s=i.getWithTTL("LocalFileAccessTouchpointsFromViewer");i.removeItem("LocalFileAccessTouchpointsFromViewer");const r=i.getWithTTL("downloadBanner");i.removeItem("downloadBanner");const l=i.getItem("localFileConfig"),c=i.getWithTTL("LocalFileAccessBannerChallengerTouchpointsFromViewer");i.removeItem("LocalFileAccessBannerChallengerTouchpointsFromViewer");const m=i.getWithTTL("LocalFileAccessBannerControlTouchpointsFromViewer");if(i.removeItem("LocalFileAccessBannerControlTouchpointsFromViewer"),e){try{if(t||s||r||l){const{url:e}=t?await chrome.tabs.get(t):{};e===a&&(s?function(e,t){o.event(n.LOCAL_FILE_ACCESS_TOUCHPOINT_PERMISSION_GRANTED),e?o.event(n.LOCAL_FILE_ACCESS_BANNER_CHALLENGER_TOUCHPOINT_PERMISSION_GRANTED):t&&o.event(n.LOCAL_FILE_ACCESS_BANNER_CONTROL_TOUCHPOINT_PERMISSION_GRANTED)}(c,m):r?await async function(){o.event(n.DOWNLOAD_BANNER_PERMISSION_GRANTED);const e=i.getItem("lastOpenTabId");await chrome.scripting.executeScript({target:{tabId:e},files:["content_scripts/injectBannerIframe.js"]}),chrome.tabs.sendMessage(e,{panel_op:"load-downloadBanner",showToast:!0}),o.event(n.DOWNLOAD_BANNER_TOAST_SHOWN),setTimeout((()=>{chrome.tabs.sendMessage(e,{content_op:"dismissBanner"})}),5e3)}():o.event(n.LOCAL_FTE_PERMISSION_GRANTED),function(){const e=i.getItem("loadedTabsInfo");let t=e?.tabsInfo||[];if(t.length){const e=i.getItem("lastOpenTabId");i.removeItem("lastOpenTabId"),o.event(o.e.REOPENED_TABS_ON_LOCAL_FILE_ACCESS),t=t.sort(((e,t)=>e.index-t.index));const n=[];let a={},s={};t.forEach((e=>{const t=e.url||e.pendingUrl;t.includes("file:///")||n.push(chrome.tabs.create({url:t,index:e.index,active:!1,windowId:e.windowId}))})),Promise.allSettled(n).then((o=>{o.forEach(((o,n)=>{if(o.reason&&o.reason?.message?.includes("No window with id")){_.error({message:"Error in reopening tab",error:o.reason?.message});const e=t[n].windowId,i=t[n].url;a[e]=a[e]?[...a[e],i]:[i]}e&&o?.value?.id&&(s[o.value.id]=t[n].id,e===t[n].id&&setTimeout((()=>{chrome.tabs.highlight({tabs:o.value.index,windowId:o.value.windowId});const e=i.getItem("settingsWindow");e&&(chrome.tabs.remove(e?.id),i.removeItem("settingsWindow"),chrome.tabs.sendMessage(o.value.id,{content_op:"showLocalFileAccessToast"}))}),2e3))})),i.setItem("tabIdMap",s);for(const o in a)chrome.windows.create({url:a[o],focused:!1}).then((n=>{if(e){for(const a of n.tabs){const n=a.url||a.pendingUrl;s[a.id]=t.find((e=>e.url===n&&e.windowId===Number(o)))?.id,s[a.id]===e&&setTimeout((()=>{chrome.tabs.highlight({tabs:a.index,windowId:a.windowId});const e=i.getItem("settingsWindow");e&&(chrome.tabs.remove(e?.id),i.removeItem("settingsWindow"),chrome.tabs.sendMessage(a.id,{content_op:"showLocalFileAccessToast"}))}),2e3)}i.setItem("tabIdMap",s)}}))})),i.removeItem("loadedTabsInfo")}}())}}catch(e){}K(n.LOCAL_FILE_PERMISSION_GRANTED),await async function(){const e=await chrome.tabs.query({url:"file:///*"});for(let t in e){const{id:o,url:n}=e[t];n.toLowerCase().endsWith(".pdf")&&chrome.tabs.reload(o)}}(),function(e,t){const o=i.getItem("settingsWindow");o&&!e&&(chrome.tabs.remove(o?.id),i.removeItem("settingsWindow"),setTimeout((()=>{t&&chrome.tabs.sendMessage(t,{content_op:"showLocalFileAccessToast"})}),2e3))}(s,t)}else i.removeItem("loadedTabsInfo")}catch(e){_.error({message:y+`checkLocalFileAccess: Error checking local file access permissions: ${e}`})}}async function z(n){N.setCommonItems(),(async()=>{i.getItem("appLocale")||i.setItem("appLocale",i.getItem("viewer-locale")||t.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale")))})(),""!==i.getItem("acrobat-gsuite-touch-points")&&""===i.getItem("acrobat-touch-points-in-other-surfaces")&&(i.setItem("acrobat-touch-points-in-other-surfaces",i.getItem("acrobat-gsuite-touch-points")),i.removeItem("acrobat-gsuite-touch-points")),l.isMimeHandlerAvailable().then((e=>{e?s.getItem("startupComplete")||t.mimeReloadAllTabs():J()})),setTimeout(X,1e4),function(){try{t.isEdge()&&i.setItem("IsRunningInEdge","true")}catch(e){}}(),async function(){if(!i.getItem("ANALYTICS_OPT_IN_ADMIN")){const e=await chrome.storage.managed.get("UsageMeasurement"),t=e&&"false"===e.UsageMeasurement?"false":"true";i.setItem("ANALYTICS_OPT_IN_ADMIN",t)}}(),async function(){const e=await chrome.storage.managed.get("DisableGenAI"),t=e&&"true"===e.DisableGenAI?"true":"false";i.setItem("DISABLE_GENAI_BY_ADMIN",t)}();const c="dc-cv-startup-new-analytics";r.hasFlag(c,I.NO_CALL).then((e=>{if(e){const e=r.getFeatureMeta(c);if(e)try{const t=JSON.parse(e);if(t.cachingTime){const e=parseInt(t.cachingTime,10);o.logEventOnce(o.e.EXTENSION_STARTUP_NEW,{storageKey:"startupNewEventLastLogged",timePeriod:e,ownershipStatus:Y()})}}catch(e){}}}));const m=await h(),{repromptDone:u,pdfOwner:p,isAcrobatInstalled:g,isReaderInstalled:f}=m??{};i.getItem("adobeYoloEnable")&&a.ADOBE_YOLO_ENABLED&&!i.getWithTTL("adobe-yolo-freeze")&&d.updateUserDetails(),D({env:n,msg:m}),s.getItem("startupComplete")||(B(),function(t){const o=0==t||1==t&&!1===e.NMHConnStatus||t==a.READER_VER||t==a.ERP_READER_VER;chrome.contextMenus.removeAll((function(){a.IS_READER||o||(chrome.contextMenus.create({id:"convertPageContextMenu",title:V("web2pdfConvertPageContextMenu"),contexts:["page"],documentUrlPatterns:M}),chrome.contextMenus.create({id:"appendPageContextMenu",title:V("web2pdfAppendPageContextMenu"),contexts:["page"],documentUrlPatterns:M}),C(i.getItem("express-touch-points")))}))}(m.ver),"true"!==u&&!0!==u||o.logEventOnce(o.e.ENABLED_AFTER_READER_REPROMPT,{storageKey:"repromptAnalyticsLogged"}),setTimeout((()=>{i.getItem("firstTimeStartup")?i.setItem("firstTimeStartup","false"):i.setItem("firstTimeStartup","true"),o.event(o.e.EXTENSION_STARTUP,{ownershipStatus:Y(),pdfOwner:p,isAcrobatInstalled:g,isReaderInstalled:f})}),5e3),s.setItem("startupComplete",!0),async function(){if("true"!==i.getItem("pdfViewer"))return!1;const e=Date.now();let t=6048e5;const o="dc-cv-offscreen-wp-grace-period";if(!await r.hasFlag(o,I.NO_CALL))return!0;const n=r.getFeatureMeta(o);if(n)try{const e=JSON.parse(n);e.gracePeriod&&(t=parseInt(e.gracePeriod,10))}catch(e){}const a=parseInt(i.getItem("lastPdfOpenTimestamp"),10);return a?e-a<=t:(i.setItem("lastPdfOpenTimestamp",e),!0)}().then((e=>{e&&T.setupWorkerOffscreen({startup:!0})})))}function Z(e){const{id:t,timestamp:n}=b,a=(new Date).getTime();e===t&&a-n<=15e3&&o.event(o.e.WELCOME_PDF_TAB_CLOSED);const s=i.getItem("signInExperimentShown");if(s){const{currTabId:t,timestamp:n}=JSON.parse(s),r=a-n,l="true"===i.getItem("signInExperimentSuppressed");e===t&&r<=15e3&&!l&&o.event(o.e.SIGN_IN_PROMPT_TAB_CLOSED),i.removeItem("signInExperimentShown"),i.removeItem("signInExperimentSuppressed")}}const Q=e=>{const{height:t,width:o}=p;return{height:t,width:o,top:Math.round(.5*(e.height-t)+e.top),left:Math.round(.5*(e.width-o)+e.left)}},ee=e=>{const t=Math.min(Math.round(.8*e.height),900),o=Math.min(Math.round(.9*e.width),1550);return{height:t,width:o,top:Math.round(.5*(e.height-t)+e.top),left:Math.round(.5*(e.width-o)+e.left)}},te=async({windowId:e})=>{const t=await chrome.windows.get(e);if("fullscreen"===t?.state||"locked-fullscreen"===t?.state)return;const{tabId:o,url:n}=i.getItem("localFileFteData"),[a]=await chrome.tabs.query({currentWindow:!0,active:!0}),s=i.getItem("localFteWindow");if(a&&a.id===o&&a.url===n){let{height:e,width:o,top:n,left:a}=Q(t);"localFilePrompt"===await le()&&({height:e,width:o,top:n,left:a}=ee(t)),await chrome.windows.update(s?.id,{height:e,width:o,left:a,top:n,focused:!0})}},oe=async e=>{const t=await le();switch(t){case"localFilePrompt":se(e);break;case"localFileBlockingPrompt":re(e);break;case"localFileFteControl":case"localFileFte":de(e,t);break;default:_.error({message:y+"openLocalFileFTEPrompts: No Valid local file prompt found"})}},ne=e=>{const t=i.getItem("localFileConfig");return!t||!t.localFilePermission&&(e.promptLimit>t.promptCount&&t.eligibleDate<=new Date(Date.now()).toISOString())},ae=(e,t=!1)=>{try{let o=i.getItem("localFileConfig");o?t||(o.promptCount=o.promptCount+1):o={promptCount:1},o.eligibleDate=(e=>{const t=Number(e);return isNaN(t)&&_.error({message:y+`_getLocalFilePromptCooldown: cooldownConfig.settingsCoolDown must be a valid number: ${e}`}),new Date(Date.now()+t*g).toISOString()})(e),i.setItem("localFileConfig",o)}catch(e){_.error({message:y+`updateLocalFilePromptCooldown: Error updating local file prompt cooldown: ${e}`})}},ie=async()=>{let e;try{e=await r.getFeatureMeta("dc-cv-local-file-permission-prompt"),e=JSON.parse(e)}catch(t){_.error({message:y+`_fetchLocalFilePromptCooldownConfig: Error fetching local file prompt cooldown config: ${t}`}),e={promptLimit:5,ignoreCoolDown:7,settingsCoolDown:7,dismissCoolDown:7}}return e},se=async e=>{try{const t=await chrome.windows.get(e.windowId);if(!await me(t))return;const n=await ie();if(ne(n)&&!ce){const{height:a,width:s,top:r,left:l}=ee(t);i.setItem("localFileFteData",{tabId:e.id,url:e.url}),ce=!0;const c=await chrome.windows.create({height:a,width:s,left:l,top:r,focused:e.active,type:"popup",url:chrome.runtime.getURL("browser/js/local-file/local-file-prompt.html")});i.setItem("localFteWindow",c),o.event(o.e.LOCAL_FILE_PERMISSION_PROMPT_SHOWN),ue(c),ae(n?.ignoreCoolDown)}}catch(e){_.error({message:y+`openLocalFilePrompt: Error handling local PDF prompt: ${e}`})}},re=async e=>{try{const t=await(async()=>{let e;try{e=await r.getFeatureMeta("dc-cv-local-file-permission-blocking-prompt"),e=JSON.parse(e)}catch(t){_.error({message:y+`_fetchLocalFileBlockingPromptCooldownConfig: Error fetching local file prompt cooldown config: ${t}`}),e={promptLimit:1,ignoreCoolDown:1,settingsCoolDown:1,dismissCoolDown:1}}return e})();ne(t)&&(i.setItem("localFileFteData",{tabId:e.id,url:e.url}),o.event(o.e.LOCAL_FILE_BLOCKING_PAGE_SHOWN),ae(t?.ignoreCoolDown),chrome.tabs.update(e.id,{url:chrome.runtime.getURL("browser/js/local-file/local-file-blocking-page.html")}))}catch(e){_.error({message:y+`openLocalFileBlockingPrompt: Error handling local PDF blocking: ${e}`})}},le=async()=>{const[e,t,o,n]=await Promise.all([r.hasFlag("dc-cv-local-file-permission-prompt"),r.hasFlag("dc-cv-local-file-permission-blocking-prompt"),r.hasFlag("dc-cv-local-file-permission-control"),r.hasFlag("dc-cv-local-file-fte")]);return e?"localFilePrompt":t?"localFileBlockingPrompt":o?"localFileFteControl":n?"localFileFte":void 0};let ce=!1;const me=async e=>!("fullscreen"===e?.state||"locked-fullscreen"===e?.state),de=async(e,n)=>{const a=await chrome.windows.get(e.windowId);if(!await me(a))return;let s,l;if((e=>{("localFileFteControl"===e&&!i.getItem("localFileConfig")||i.getItem("rc")&&!i.getItem("localFteCounterResetNew"))&&(i.removeItem("localFteCount"),i.removeItem("localFteCooldown"),i.removeItem("localFteCounterReset"),i.setItem("localFteCounterResetNew",!0))})(n),"localFileFteControl"===n){const e=i.getItem("localFteDontShowAgain");l=await(async()=>{let e;try{e=await r.getFeatureMeta("dc-cv-local-file-fte"),e=JSON.parse(e)}catch(t){_.error({message:y+`_fetchLocalFileControlPromptCooldownConfig: Error fetching local file prompt cooldown config: ${t}`}),e={promptLimit:5,ignoreCoolDown:7,settingsCoolDown:7,dismissCoolDown:7}}return e})(),s=ne(l)&&!e}else s=await(async()=>{const e=t.isEdge(),o=i.getWithTTL("localFteCooldown"),n=i.getItem("localFteCount")||0,a=i.getItem("localFteDontShowAgain"),s=await chrome.extension.isAllowedFileSchemeAccess();return!(e||s||a||o||n>=20)})();if(s&&!ce){const{height:t,width:s,top:r,left:c}=Q(a);i.setItem("localFileFteData",{tabId:e.id,url:e.url}),ce=!0;const m=await chrome.windows.create({height:t,width:s,left:c,top:r,focused:e.active,type:"popup",url:chrome.runtime.getURL("browser/js/local-fte.html")});o.event(o.e.LOCAL_FILE_FTE_SHOWN),"localFileFteControl"===n&&ae(l?.ignoreCoolDown),i.setItem("localFteWindow",m),ue(m)}},ue=t=>{const o=t?.tabs?.[0]?.id;o&&setTimeout((()=>{chrome.tabs.setZoom(o,1)}),500),e.registerHandlers({closeLocalFte:()=>chrome.windows.remove(t.id)})},pe=async e=>{const t=i.getItem("localFteWindow");e===t?.id&&(ce=!1,i.removeItem("localFteWindow"),fe())},ge=async e=>{const{tabId:t}=i.getItem("localFileFteData");if(t===e){const e=i.getItem("localFteWindow");chrome.windows.remove(e?.id)}},Ie=e=>{const t=i.getItem("isAllowedLocalFileAccess"),{filename:n,id:a}=e||{};if(n&&a){let e=i.getItem("downloadCount")||0;i.setItem("downloadCount",++e),t||chrome.downloads.search({id:a},(async t=>{const{mime:n,referrer:a,finalUrl:s}=t[0],l=(a||s).split("/")[2];"application/pdf"===n&&(l===chrome.runtime.id?chrome.tabs.query({active:!0,currentWindow:!0},(e=>{chrome.tabs.sendMessage(e[0].id,{main_op:"downloadFileSuccess"})})):!u.includes(l)&&e>=3&&(async()=>{if(!await r.hasFlag("dc-cv-download-banner",I.NO_CALL))return!1;i.getItem("downloadBannerFlag")||(i.setItem("downloadBannerFlag",!0),o.event(o.e.DOWNLOAD_BANNER_ENABLED));const e=i.getItem("downloadBannerData");if(e){const{count:t,lastShown:o,doNotShow:n}=e;if(n||t>=10)return!1;const a=t>=3?12096e5:864e5;if((new Date).getTime()-o<a)return!1}return!0})().then((e=>{e&&chrome.tabs.query({active:!0,currentWindow:!0},(e=>{e[0]?.id&&(chrome.tabs.sendMessage(e[0].id,{panel_op:"load-downloadBanner"}),o.event(o.e.DOWNLOAD_BANNER_SHOWN),(()=>{const e=i.getItem("downloadBannerData")||{};e.count=(e.count||0)+1,e.lastShown=(new Date).getTime(),i.setItem("downloadBannerData",e),e.count>=10&&o.event(o.e.DOWNLOAD_BANNER_MAX_LIMIT_SHOWN)})())}))})))}))}},fe=async()=>{try{const e=await le(),t=i.getItem("settingsWindow");if("localFilePrompt"===e&&!t){const e=await ie();ae(e?.dismissCoolDown,!0),o.event(o.e.LOCAL_FILE_PERMISSION_PROMPT_CLOSED)}}catch(e){_.error({message:y+`handleLocalFilePromptDismiss: Error updating dismiss cooldown ${e}`})}};e.registerHandlers({themeChange:w,reRegisterUninstallUrl:w,localeChange:e=>w({locale:e.locale}),updateLoadedTabsInfo:(e,t)=>{const o=i.getItem("loadedTabsInfo");let n=o?.tabsInfo||[];const a=t.tab;if(o.tabsInfo){const e=n.findIndex((e=>e.id==a.id));-1===e?n.push(a):n[e]={...a}}else n=[a];i.setItem("loadedTabsInfo",{tabsInfo:n})}});export{z as startup,x as registerActions,Z as onWelcomeTabRemoved,j as contextMenuOnClickHandler,$ as updateAnalyticsOptInAdmin,te as refocusLocalFteWindow,ge as onLocalFileClosed,pe as onLocalFteWindowClosed,H as startQAWithWebpage,Ie as onDownloadChanged,oe as openLocalFileFTEPrompts};