/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
(()=>{const e="-shared",t="acrobatDisconnectContentScriptStart",n="acrobatContentScriptDisconnectEnd",o="acrobat-prompt",i="acrobat-fte-tooltip-container",r="acrobat-gdrive-fte-state";let a,c,s,l,d,u,m,p,g,h=!1,v=!1;const w=()=>!chrome.runtime?.id,f=()=>window.location.pathname.includes("/file"),b=e=>{m?.sendAnalytics(e)},E=t=>{if(h||t){const t=document.getElementsByClassName(o);if(t?.length>0){const e=t[0].getElementsByClassName(i);e?.length>0&&b(["DCBrowserExt:GdriveFTE:NativeView:Dismissed"]),t[0].remove()}const n=document.getElementsByClassName(o+e);n?.length>0&&n[0].remove();const r=document.getElementById("acrobat-prompt");r?.remove(),h=!1}},T=()=>((e,t)=>{if(e){const n=d?.config?.selectors,o=n&&n[t];for(let t=0;t<o?.length;t++){let n=e.querySelector(o[t]);if(n)return n}}return null})(document,"fileDetails"),y=e=>{try{return JSON.parse(e?.textContent?.replace(/\\/g,""))}catch(e){return null}},C=e=>{if("Enter"===e.key||32===e.keyCode||"click"===e.type){if(w())return void E();const{acrobatTouchPointClicked:t,getAcrobatTouchPointFteEligibility:n}=u;t(r),d.fteToolTip.touchPointClicked=!0;const o=T(),i=y(o);let a="DCBrowserExt:Gdrive:OpenInExtension:Clicked";"Enter"===e.type&&(a="DCBrowserExt:Gdrive:OpenInExtension:EnterKeyDown"),32===e.keyCode&&(a="DCBrowserExt:Gdrive:OpenInExtension:SpaceKeyDown"),b([[a,{gsuiteFte:n(d?.config?.enableFteToolTip)}]]);let c="GoogleDriveNativeView";!1!==d?.addOnStatus?.isAddOnDefault&&(c+="NDV");const s=chrome.runtime.getURL("viewer.html")+"?pdfurl="+encodeURIComponent(`https://drive.usercontent.google.com/download?id=${i.id}&authuser=${d?.userId}&acrobatPromotionSource=${c}`)+"&pdffilename="+encodeURIComponent(i.title);window.open(s,"_blank")}},D=()=>{const t=document.createElement("div"),n=(()=>{const e=document.createElement("img");e.setAttribute("src",chrome.runtime.getURL("browser/images/acrobat_dc_appicon_128.png"));const t=f()?"acrobat-icon-shared":"acrobat-icon";return e.setAttribute("class",t),e})(),i=(()=>{const e=document.createElement("span"),t=f()?"acrobat-prompt-text-shared":"acrobat-prompt-text";return e.setAttribute("class",t),e.textContent=d?.config?.acrobatPromptText||"Edit with Acrobat",e})(),r=(()=>{const e=document.createElement("div"),t=f()?"acrobat-prompt-tooltip":"acrobat-prompt-tooltip-shared";return e.setAttribute("class",t),e.textContent=d?.config?.acrobatPromptText||"Open in Acrobat",e})(),a=f()?o+e:o;return t.setAttribute("class",a),t.appendChild(n),t.appendChild(r),t.appendChild(i),t.tabIndex=0,t.addEventListener("keydown",(e=>{C(e)}),{signal:d?.eventControllerSignal}),t.addEventListener("click",(e=>{C(e)}),{signal:d?.eventControllerSignal}),t},S=e=>{const t=document.getElementsByClassName(i);"Enter"!==e.key&&"ArrowLeft"!==e.key&&"ArrowRight"!==e.key&&"click"!==e.type||t?.length>0&&(t[0]?.contains(e.target)||(t[0].remove(),b(["DCBrowserExt:GdriveFTE:NativeView:Dismissed"]),document.removeEventListener("click",S),document.removeEventListener("keydown",S)))},F=e=>{const{createFteTooltip:t}=u,n=t(!1===d?.addOnStatus?.isAddOnDefault?d?.config?.fteToolTipStrings:d?.config?.fteToolTipStringsForNativeViewNonDV,"gdriveNativeView");(e=>{e.querySelector(".acrobat-fte-tooltip-button").addEventListener("click",(()=>{e.remove(),b(["DCBrowserExt:GdriveFTE:NativeView:Clicked"]),document.removeEventListener("click",S),document.removeEventListener("keydown",S)}))})(n),document.addEventListener("click",S,{signal:d?.eventControllerSignal}),document.addEventListener("keydown",S,{signal:d?.eventControllerSignal}),e.appendChild(n),s?.sendAnalyticsWithGdriveDVFeatureStatus("DCBrowserExt:GdriveFTE:NativeView:Shown");const{updateFteToolTipCoolDown:o}=u;o(d?.config?.fteConfig?.tooltip,r).then((e=>{d.fteToolTip={...d?.fteToolTip,...e}}))},L=()=>{try{if(h)return;const t=D(),n=(()=>{let e;for(let t=0;t<d?.config?.selectors?.promptParentElement.length&&(e=document.querySelector(d?.config?.selectors?.promptParentElement[t]),!e);t++);return e})(),i=f()?o+"-"+e:o;if(n&&0===n?.getElementsByClassName(i)?.length){if(n.prepend(t),h=!0,window.innerWidth>1080){b([["DCBrowserExt:Gdrive:OpenInExtension:Shown",{gsuiteFte:u?.getAcrobatTouchPointFteEligibility(d?.config?.enableFteToolTip)}]]);const{shouldShowFteTooltip:e}=u,n=d?.config?.fteConfig?.tooltip;e(n,d?.fteToolTip,d?.config?.enableFteToolTip).then((e=>{e&&setTimeout((()=>{F(t)}),1e3)}))}}else E()}catch(e){E()}},A=()=>{const e=T();if(e){const t=y(e);"application/pdf"===t?.mimeType?L():E(),(e=>{d&&!d?.analyticsForImageOpenedSent&&e?.mimeType?.startsWith("image/")&&(m?.sendAnalyticsOncePerMonth("DCBrowserExt:Gdrive:Image:Opened",{eventContext:e.mimeType}),d.analyticsForImageOpenedSent=!0)})(t)}else E()},k=()=>{try{A(),d?.config?.enableGDriveSearchBarAnalytics&&g?.checkForSearchBarDiv(),(d?.config?.enableGDriveSearchBarAnalytics||s?.isDefaultViewer())&&g?.checkForSearchResultsTable(),d?.pageLoaded&&c?.processForTouchPoint()}catch(e){}},x=()=>{if(document?.body)try{const e={childList:!0,subtree:!0,attributes:!0,attributeFilter:["style"]};l=new MutationObserver((function(){l.takeRecords(),w()?(l.disconnect(),R(),d?.disconnectEventListeners()):k()})),l.observe(document?.body,e)}catch(e){}else setTimeout(x,500)},R=()=>{E(!0),c?.removeAllTouchPoints()},G=()=>{w()&&(l&&l.disconnect(),E(),d?.disconnectEventListeners(),window.dispatchEvent(new CustomEvent(n,{detail:{state:d.createStateTransferObject()}})))},O=()=>{window.addEventListener(n,(e=>(e=>{w()||d.updateStateFromTransferObject(e?.detail?.state)})(e)),{signal:d?.eventControllerSignal}),window.dispatchEvent(new Event("orphanContentScript")),window.dispatchEvent(new Event(t)),window.addEventListener(t,G,{signal:d?.eventControllerSignal})},_=()=>{chrome.runtime.sendMessage({main_op:"gdrive-init"},(async e=>{(e?.acrobatTouchPointEnabled||e?.isAcrobatDefaultForSurface)&&(I(e),await(e=>{const t=chrome.runtime.getURL("content_scripts/gdrive/state.js");return import(t).then((t=>{d=t.default,d.config=e,d.acrobatIconPath="browser/images/acrobat_dc_trefoil_24_white.svg"}))})(e),O(),(e?.enableOpenInExtension||window.location?.search?.includes("enableAcrobatPromptInGSuite"))&&(async()=>{[u,c,m,g,s,p]=await Promise.all([import(chrome.runtime.getURL("content_scripts/gsuite/fte-utils.js")),import(chrome.runtime.getURL("content_scripts/gdrive/touchpoint-service.js")),import(chrome.runtime.getURL("content_scripts/gsuite/util.js")),import(chrome.runtime.getURL("content_scripts/gdrive/search-handler.js")),import(chrome.runtime.getURL("content_scripts/gdrive/default-viewership-service.js")),import(chrome.runtime.getURL("content_scripts/gdrive/api-parsing-util.js"))])})().then((()=>{m?.sendAnalyticsOncePerMonth("DCBrowserExt:Gdrive:OpenInExtension:Enable"),(e?.enableFteToolTip||e?.enableFteToolTipForListGridView)&&m?.sendAnalyticsOncePerMonth("DCBrowserExt:GdriveFTE:Enable"),(()=>{if(window?.document?.location?.pathname.includes("/u/"))return void(d.userId=window.document.location.pathname.split("/")[3]);const e=document.createElement("script");document.addEventListener("acrobat-auth-user-id",(t=>{d.userId=t?.detail?.authuser,e.remove()})),e.type="text/javascript",e.src=chrome.runtime.getURL("content_scripts/gdrive/get-auth-user.js"),document.head.appendChild(e)})(),a=chrome.runtime.getURL("browser/images/acrobat_dc_appicon_128.png"),(async()=>{const e=(new Date).getTime();let t={count:0,nextDate:e};t=(await chrome.storage.local.get(r))?.[r]||t;const n=d?.config?.fteConfig?.tooltip;((e,t)=>-1!==e?.resetDay&&t>e?.resetDay)(n,e)&&(t.count=0,t.nextDate=e),d.fteToolTip={...t},t={[r]:t},chrome.storage.local.set(t)})(),s?.sendAnalyticsWithDefaultViewershipFeatureStatus(),B(),(()=>{if(!d?.adobeCleanFontAdded){const e=chrome.runtime.getURL("browser/css/fonts/AdobeClean-Regular.otf"),t=new FontFace("AdobeClean-Regular",`url(${e})`);t.load().then((()=>{document.fonts.add(t)})),d.adobeCleanFontAdded=!0}})(),R(),P(),k(),x()})))}))},P=()=>{d?.config?.enableGDriveTripleDotMenuAnalytics&&(document.addEventListener("click",(e=>{c?.handleAnalyticsForTripleDotMenu(e),c?.handleAnalyticsForTopMenu(e)}),{signal:d?.eventControllerSignal}),document.addEventListener("contextmenu",(e=>{c?.handleAnalyticsForRightClickMenu(e)}),{signal:d?.eventControllerSignal}))},B=()=>{window.addEventListener("load",(()=>{setTimeout((()=>{d.pageLoaded=!0}),500)}))},I=e=>{if(!v&&(v=!0,e?.enableGDriveGridViewTouchPoint||e?.enableGDriveListViewTouchPoint)){const e=document.createElement("script");e.setAttribute("id","acrobat-response-interceptor"),e.src=chrome.runtime.getURL("content_scripts/gdrive/gdrive-inject.js"),e.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(e)}};document.addEventListener("acrobat-addon-status-data",(e=>{setTimeout((()=>{p?.handleGDriveInstalledAppResponse(e)}),0)}),{signal:d?.eventControllerSignal}),document.addEventListener("gdrive-search-bar-api-response",(e=>{s?.isDefaultViewer()&&e?.detail?.searchResponse&&p?.processSearchApiResponse(e)}),{signal:d?.eventControllerSignal}),chrome.runtime.onMessage.addListener((function(e){switch(e.content_op){case"acrobatGdriveFteStateUpdated":(e=>{d.fteToolTip={...d?.fteToolTip,...e?.fteState}})(e);break;case"acrobatTouchPointsDisabled":d?.config?.isAcrobatDefaultForSurface||(l?.disconnect(),R());break;case"changeDefaultViewershipForSurface":"gdrive"===e?.surfaceId&&chrome.runtime.id&&(e?.isDefault?s?.takeDefaultViewerShip():s?.resetDefaultViewership())}})),(async()=>{"drive.google.com"===window?.document?.location?.host&&window.top===window.self&&0===window?.document?.location?.ancestorOrigins.length&&_()})()})();