<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
  viewBox="0 0 100 100" enable-background="new 0 0 0 0" xml:space="preserve">
  <style type="text/css">
	.st0{fill-opacity:0;stroke:#000000;stroke-width:4px;stroke-opacity:0.1;}
	.st1{fill-opacity:0;stroke:#1473E6;stroke-width:4px;}
</style>
  	<circle cx="50" cy="50" r="21" class="st0" />
    <path class="st1" d="M 71 50 A 21 21 0 0 0 50 29">
      <animateTransform 
         attributeName="transform" 
         attributeType="XML" 
         type="rotate"
         dur="1s"
         from="0 50 50"
         to="360 50 50" 
         repeatCount="indefinite" />
  </path>
</svg>