# -*- coding: utf-8 -*-
"""
项目内容分析器
从当前项目中提取相关内容作为LLM输入
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass, asdict
import logging

from src.utils.logger import logger


@dataclass
class ProjectContent:
    """项目内容数据模型"""
    title: str = ""
    summary: str = ""
    world_building: Optional[str] = None
    characters: List[str] = None
    themes: List[str] = None
    genre: str = ""
    target_audience: str = ""
    key_elements: List[str] = None
    content_type: str = ""  # 内容类型：小说、剧本、游戏等
    language: str = "zh-CN"
    
    def __post_init__(self):
        if self.characters is None:
            self.characters = []
        if self.themes is None:
            self.themes = []
        if self.key_elements is None:
            self.key_elements = []
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    def is_empty(self) -> bool:
        """检查项目内容是否为空"""
        return not any([
            self.title, self.summary, self.world_building,
            self.characters, self.themes, self.key_elements
        ])


class ProjectContentAnalyzer:
    """项目内容分析器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.supported_formats = {'.txt', '.md', '.json', '.yaml', '.yml'}
        
        # 常见的项目文件模式
        self.content_patterns = {
            'readme': ['readme*', 'README*'],
            'story': ['story*', '故事*', '小说*', 'novel*'],
            'script': ['script*', '剧本*', 'screenplay*'],
            'world': ['world*', '世界观*', 'worldbuilding*', 'setting*'],
            'character': ['character*', '角色*', 'chars*', 'people*'],
            'outline': ['outline*', '大纲*', 'plot*'],
            'summary': ['summary*', '摘要*', '简介*', 'synopsis*']
        }
        
        # 关键词提取模式
        self.keyword_patterns = {
            'characters': [
                r'(?:角色|人物|主角|配角|character)[:：]\s*([^\n]+)',
                r'(?:姓名|名字|name)[:：]\s*([^\n]+)',
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)',  # 英文人名
                r'([一-龟]{2,4})',  # 中文人名
            ],
            'themes': [
                r'(?:主题|theme)[:：]\s*([^\n]+)',
                r'(?:题材|genre)[:：]\s*([^\n]+)',
                r'(?:类型|type)[:：]\s*([^\n]+)',
            ],
            'genre': [
                r'(?:类型|genre|分类)[:：]\s*([^\n]+)',
                r'(?:题材|category)[:：]\s*([^\n]+)',
            ]
        }
        
        logger.info(f"项目内容分析器初始化完成，项目根目录: {self.project_root}")
    
    def analyze_current_project(self) -> ProjectContent:
        """分析当前项目内容"""
        try:
            logger.info("开始分析当前项目内容...")
            
            # 扫描项目文件
            project_files = self._scan_project_files()
            logger.info(f"找到 {len(project_files)} 个相关文件")
            
            if not project_files:
                logger.warning("未找到任何项目内容文件")
                return ProjectContent()
            
            # 分析文件内容
            content = ProjectContent()
            
            for file_path, file_type in project_files:
                try:
                    file_content = self._read_file_content(file_path)
                    if file_content:
                        self._extract_content_by_type(content, file_content, file_type)
                except Exception as e:
                    logger.warning(f"读取文件失败 {file_path}: {e}")
                    continue
            
            # 后处理和优化
            self._post_process_content(content)
            
            logger.info(f"项目内容分析完成: {content.title or '未知标题'}")
            return content
            
        except Exception as e:
            logger.error(f"项目内容分析失败: {e}")
            return ProjectContent()
    
    def _scan_project_files(self) -> List[tuple]:
        """扫描项目文件"""
        project_files = []
        
        try:
            # 遍历项目目录
            for root, dirs, files in os.walk(self.project_root):
                # 跳过隐藏目录和常见的非内容目录
                dirs[:] = [d for d in dirs if not d.startswith('.') and 
                          d not in {'node_modules', '__pycache__', 'venv', '.git'}]
                
                for file in files:
                    file_path = Path(root) / file
                    
                    # 检查文件扩展名
                    if file_path.suffix.lower() not in self.supported_formats:
                        continue
                    
                    # 确定文件类型
                    file_type = self._determine_file_type(file_path)
                    if file_type:
                        project_files.append((file_path, file_type))
            
            # 按优先级排序
            priority_order = ['readme', 'summary', 'story', 'world', 'character', 'outline', 'script']
            project_files.sort(key=lambda x: priority_order.index(x[1]) if x[1] in priority_order else 999)
            
            return project_files
            
        except Exception as e:
            logger.error(f"扫描项目文件失败: {e}")
            return []
    
    def _determine_file_type(self, file_path: Path) -> Optional[str]:
        """确定文件类型"""
        file_name = file_path.name.lower()
        
        for content_type, patterns in self.content_patterns.items():
            for pattern in patterns:
                if self._match_pattern(file_name, pattern.lower()):
                    return content_type
        
        return 'general'  # 通用文件
    
    def _match_pattern(self, text: str, pattern: str) -> bool:
        """匹配文件名模式"""
        import fnmatch
        return fnmatch.fnmatch(text, pattern)
    
    def _read_file_content(self, file_path: Path) -> Optional[str]:
        """读取文件内容"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    
                    # 检查内容是否合理
                    if len(content.strip()) > 0:
                        return content
                        
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"读取文件失败 {file_path} (编码: {encoding}): {e}")
                    continue
            
            logger.warning(f"无法读取文件 {file_path}，尝试了所有编码")
            return None
            
        except Exception as e:
            logger.error(f"读取文件异常 {file_path}: {e}")
            return None
    
    def _extract_content_by_type(self, content: ProjectContent, text: str, file_type: str):
        """根据文件类型提取内容"""
        try:
            if file_type == 'readme':
                self._extract_readme_content(content, text)
            elif file_type == 'story':
                self._extract_story_content(content, text)
            elif file_type == 'world':
                self._extract_world_content(content, text)
            elif file_type == 'character':
                self._extract_character_content(content, text)
            elif file_type == 'summary':
                self._extract_summary_content(content, text)
            elif file_type == 'outline':
                self._extract_outline_content(content, text)
            else:
                self._extract_general_content(content, text)
                
        except Exception as e:
            logger.warning(f"提取 {file_type} 内容失败: {e}")
    
    def _extract_readme_content(self, content: ProjectContent, text: str):
        """提取README内容"""
        lines = text.split('\n')
        
        # 提取标题（通常是第一行或第一个#标题）
        if not content.title:
            for line in lines[:10]:  # 只检查前10行
                line = line.strip()
                if line.startswith('#'):
                    content.title = line.lstrip('#').strip()
                    break
                elif line and not line.startswith(('---', '===')):
                    content.title = line
                    break
        
        # 提取摘要
        if not content.summary:
            summary_lines = []
            in_summary = False
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if any(keyword in line.lower() for keyword in ['简介', '介绍', '概述', 'description', 'summary']):
                    in_summary = True
                    continue
                
                if in_summary and line.startswith('#'):
                    break
                
                if in_summary or (not content.summary and len(summary_lines) < 5):
                    if not line.startswith(('#', '-', '*', '>')):
                        summary_lines.append(line)
                        if len(summary_lines) >= 3:
                            break
            
            if summary_lines:
                content.summary = ' '.join(summary_lines)
        
        # 提取其他信息
        self._extract_keywords_from_text(content, text)
    
    def _extract_story_content(self, content: ProjectContent, text: str):
        """提取故事内容"""
        content.content_type = "故事"
        
        # 提取开头作为摘要
        if not content.summary:
            paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
            if paragraphs:
                # 取前几段作为摘要，但不超过500字
                summary_text = ""
                for para in paragraphs[:3]:
                    if len(summary_text + para) > 500:
                        break
                    summary_text += para + " "
                content.summary = summary_text.strip()
        
        # 提取角色和关键词
        self._extract_keywords_from_text(content, text)
    
    def _extract_world_content(self, content: ProjectContent, text: str):
        """提取世界观内容"""
        content.world_building = text[:1000]  # 取前1000字符作为世界观
        self._extract_keywords_from_text(content, text)
    
    def _extract_character_content(self, content: ProjectContent, text: str):
        """提取角色内容"""
        # 提取角色名字
        characters = set()
        
        # 使用正则表达式提取角色信息
        for pattern in self.keyword_patterns['characters']:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if isinstance(match, tuple):
                    match = match[0]
                if match and len(match.strip()) > 1:
                    characters.add(match.strip())
        
        # 合并到现有角色列表
        content.characters.extend(list(characters))
        content.characters = list(set(content.characters))  # 去重
    
    def _extract_summary_content(self, content: ProjectContent, text: str):
        """提取摘要内容"""
        if not content.summary:
            content.summary = text.strip()
        
        self._extract_keywords_from_text(content, text)
    
    def _extract_outline_content(self, content: ProjectContent, text: str):
        """提取大纲内容"""
        # 从大纲中提取关键元素
        lines = text.split('\n')
        key_elements = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith(('#', '-', '*', '>')):
                if len(line) > 10 and len(line) < 100:  # 合理长度的关键点
                    key_elements.append(line)
        
        content.key_elements.extend(key_elements[:10])  # 最多10个关键元素
        self._extract_keywords_from_text(content, text)
    
    def _extract_general_content(self, content: ProjectContent, text: str):
        """提取通用内容"""
        self._extract_keywords_from_text(content, text)
    
    def _extract_keywords_from_text(self, content: ProjectContent, text: str):
        """从文本中提取关键词"""
        try:
            # 提取主题
            for pattern in self.keyword_patterns['themes']:
                matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if match and match.strip():
                        content.themes.append(match.strip())
            
            # 提取类型
            if not content.genre:
                for pattern in self.keyword_patterns['genre']:
                    matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
                    if matches:
                        content.genre = matches[0].strip()
                        break
            
            # 去重
            content.themes = list(set(content.themes))
            
        except Exception as e:
            logger.warning(f"提取关键词失败: {e}")
    
    def _post_process_content(self, content: ProjectContent):
        """后处理内容"""
        try:
            # 清理和优化标题
            if content.title:
                content.title = content.title.strip()[:100]  # 限制长度
            
            # 清理摘要
            if content.summary:
                content.summary = content.summary.strip()[:500]  # 限制长度
            
            # 清理角色列表
            content.characters = [char for char in content.characters 
                                if char and len(char.strip()) > 1 and len(char.strip()) < 20][:10]
            
            # 清理主题列表
            content.themes = [theme for theme in content.themes 
                            if theme and len(theme.strip()) > 1 and len(theme.strip()) < 50][:5]
            
            # 设置默认值
            if not content.title:
                content.title = "未命名项目"
            
            if not content.content_type:
                if any(keyword in content.summary.lower() for keyword in ['小说', '故事', 'novel', 'story']):
                    content.content_type = "小说"
                elif any(keyword in content.summary.lower() for keyword in ['剧本', 'script', 'screenplay']):
                    content.content_type = "剧本"
                elif any(keyword in content.summary.lower() for keyword in ['游戏', 'game']):
                    content.content_type = "游戏"
                else:
                    content.content_type = "创作内容"
            
            logger.info(f"内容后处理完成: 标题={content.title}, 类型={content.content_type}")
            
        except Exception as e:
            logger.error(f"内容后处理失败: {e}")
    
    def extract_key_information(self, content: str) -> Dict[str, Any]:
        """提取关键信息（独立方法）"""
        try:
            info = {
                'length': len(content),
                'paragraphs': len([p for p in content.split('\n\n') if p.strip()]),
                'sentences': len([s for s in content.split('。') if s.strip()]),
                'keywords': [],
                'entities': []
            }
            
            # 简单的关键词提取
            words = re.findall(r'[\u4e00-\u9fff]+', content)  # 中文词汇
            word_freq = {}
            for word in words:
                if len(word) >= 2:
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # 取频率最高的词作为关键词
            info['keywords'] = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return info
            
        except Exception as e:
            logger.error(f"提取关键信息失败: {e}")
            return {}
    
    def get_world_building_context(self) -> Optional[str]:
        """获取世界观设定内容"""
        try:
            content = self.analyze_current_project()
            return content.world_building
        except Exception as e:
            logger.error(f"获取世界观内容失败: {e}")
            return None


# 全局实例
_analyzer = None

def get_project_content_analyzer() -> ProjectContentAnalyzer:
    """获取项目内容分析器实例"""
    global _analyzer
    if _analyzer is None:
        _analyzer = ProjectContentAnalyzer()
    return _analyzer