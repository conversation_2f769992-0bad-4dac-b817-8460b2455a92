/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
(()=>{let t,e,o,n,r,c,s=!1;const i="outlook-acrobat-touch-point",a=()=>{const t=document.createElement("script");t.src=chrome.runtime.getURL("content_scripts/outlook/outlook-inject.js"),t.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(t)},u=async(n,c)=>{try{if(s)return void r?.showErrorToast();const i=n?.getAttribute("data"),a=(t=>t&&t.includes("toolbar")?t.substring(0,t.indexOf("#toolbar=")):t)(i),u=new URL(a);t?.token&&u.searchParams.set("token",t.token),u.searchParams.set("acrobatPromotionSource","outlookPDF");const l=u.toString();let d=chrome.runtime.getURL("viewer.html")+"?pdfurl="+encodeURIComponent(l);const h=(()=>{const e=m(t?.config?.selectors?.title,document);return e&&e.innerText?e.innerText:""})();h&&(d+="&pdffilename="+encodeURIComponent(h)),window.open(d,"_blank"),o?.sendAnalytics("Enter"===c?.key?[["DCBrowserExt:Outlook:TouchPoint:EnterKeyDown"]]:" "===c?.key?[["DCBrowserExt:Outlook:TouchPoint:SpaceKeyDown"]]:[["DCBrowserExt:Outlook:TouchPoint:Clicked"]]),e?.acrobatTouchPointClicked("acrobat-outlook-fte-state")}catch(t){o?.sendErrorLog("OutlookTouchPoint","Failure in acrobatTouchPointEventHandler")}},l=e=>{const o=document.createElement("div"),n=(()=>{const t=document.createElement("img");return t.setAttribute("src",chrome.runtime.getURL("browser/images/acrobat_dc_appicon_128.png")),t.setAttribute("class","acrobat-icon"),t})(),r=(()=>{const e=document.createElement("span");return e.setAttribute("class","acrobat-text"),e.textContent=t?.config?.touchPointString||"Open in Acrobat",e})();return o.classList.add(i),o.setAttribute("data-is-focusable","true"),o.appendChild(n),o.appendChild(r),o.addEventListener("click",(t=>u(e,t))),o.addEventListener("keydown",(t=>{"Enter"!==t.key&&" "!==t.key||(t.preventDefault(),u(e,t))})),o},d=()=>{const e=document.getElementsByClassName(i);if(e.length>0){const t=e[0];c?.removeFteTooltip(),t?.remove()}t.lastTabUrl=""},m=(t,e)=>{const n=o?.getElementListForSelectors(t,e);return n.length>0?n[0]:null},h=()=>{try{const e=m(t?.config?.selectors?.nativeViewer,document);if(e){const n=m(t?.config?.selectors?.header,e);if(n){const r=m(t?.config?.selectors?.url,e);r&&(t=>{const e=t.getAttribute("data");return"application/pdf"===t.getAttribute("type")&&e})(r)?((e,n)=>{try{const r=document.getElementsByClassName(i);if(r.length>0){const t=r[0];if(e.lastElementChild===t)return;return void e.append(t)}const c=l(n);e.append(c);const s=window.location.href;t?.lastTabUrl!==s&&(o?.sendAnalytics([["DCBrowserExt:Outlook:TouchPoint:Shown"]]),chrome.runtime.sendMessage({main_op:"outlook-touch-point-added"}),t.lastTabUrl=s)}catch(t){o?.sendErrorLog("OutlookTouchPoint","Failure in addAcrobatTouchPoint")}})(n,r):d()}else d()}else d()}catch(t){o?.sendErrorLog("OutlookTouchPoint","Failure in processForDOMUpdate")}},p=()=>{if(document?.body)try{const e={childList:!0,subtree:!0};n=new MutationObserver((function(){n.takeRecords(),chrome.runtime?.id?h():(n?.disconnect(),d(),t?.disconnectEventListeners())})),n.observe(document?.body,e)}catch(t){}else setTimeout(p,500)},k=()=>{chrome.runtime.onMessage.addListener((t=>{"add-fte-for-outlook-touch-point"===t?.type?c?.addFTE():"acrobatTouchPointsDisabled"===t?.content_op&&(n?.disconnect(),d())}))};chrome.runtime.sendMessage({main_op:"outlook-init"},(async n=>{n?.enableOutlookPDFTouchPoint&&(a(),(async()=>{[t,e,o,r,c]=await Promise.all([import(chrome.runtime.getURL("content_scripts/outlook/state.js")).then((t=>t.default)),import(chrome.runtime.getURL("content_scripts/gsuite/fte-utils.js")),import(chrome.runtime.getURL("content_scripts/gsuite/util.js")),import(chrome.runtime.getURL("content_scripts/outlook/outlook-error-toast-service.js")),import(chrome.runtime.getURL("content_scripts/outlook/outlook-fte-service.js"))])})().then((()=>{t.config=n,o?.sendAnalyticsOncePerMonth("DCBrowserExt:Outlook:TouchPoint:Enabled"),document.addEventListener("acrobat-outlook-token-intercept-response",(e=>{if(e?.detail?.success)s=!1,t&&(t.token=e?.detail?.data);else{s=!0;const t=e?.detail?.error||"Unknown error",n=e?.detail?.status||"No status code";o?.sendErrorLog("OutlookTouchPoint",`Token API failed - Error: ${t}, Status: ${n}`)}}),{signal:t?.eventControllerSignal}),k(),d(),r?.removeErrorToast(),p(),h()})))}))})();