/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{analytics as e}from"../common/analytics.js";import{communicate as t}from"./communicate.js";import{util as n}from"./util.js";import{SETTINGS as r}from"./settings.js";import{dcLocalStorage as s}from"../common/local-storage.js";import{downloadManager as o}from"./download-manager.js";import{privateApi as a}from"./private-api.js";import{viewerModuleUtils as i}from"./viewer-module-utils.js";import{floodgate as c}from"./floodgate.js";import{openLocalFileFTEPrompts as l}from"./ch-context-menu.js";import{offscreenActions as u}from"./offscreen-actions.js";import{CACHE_PURGE_SCHEME as m}from"./constant.js";import{setExperimentCodeForAnalytics as d,removeExperimentCodeForAnalytics as p}from"../common/experimentUtils.js";let f=null,g=!1,E=chrome.runtime.getURL("viewer.html"),h=!1,I={},_={};const v=new Map;function b(e){if(void 0!==e.responseHeaders){const t=D(e.responseHeaders,"content-length");if(t)return t.value}}chrome.extension.isAllowedFileSchemeAccess((e=>{h=e}));const R=e=>{chrome.management.getSelf().then((async t=>{const n=await c.hasFlag("dc-cv-genai-webpages-master"),r="true"===s.getItem("adobeInternal"),o=s.getItem("isAdminUser");if(r||n&&"admin"!==t.installType&&!o){c.hasFlag("dc-cv-enable-genai-webpages")?.then((e=>{!s.getItem("enableGenAIFab")&&e&&s.setItem("enableGenAIFab",`${e}`),e?d("EGW"):p("EGW")}));const{version:t}=chrome.runtime.getManifest();chrome.sidePanel.setOptions({enabled:!0,tabId:e.tabId,path:`resources/SidePanel/index.html?tabId=${e.tabId}&version=${t}`}),s.setItem("sidePanelRegistered",!0)}else s.setItem("sidePanelRegistered",!1);const a=await c.hasFlag("dc-cv-enable-splunk-logging");i.enableSplunk(a)}));const t=c.getFeatureMeta("dc-cv-genai-webpage-blocklist");if(t){const e=JSON.parse(t);s.setItem("genaiWebpageBlockList",e)}c.hasFlag("dc-cv-all-domain-tracking").then((e=>{s.setItem("allDomainTracking",e)})),chrome.tabs.get(e.tabId,(e=>{chrome.runtime.lastError||L(e)}))};function L(e){if(!r.IS_READER&&0!=t.version&&(1!=t.version||0!=t.NMHConnStatus)&&e.url){const t=new URL(e.url);chrome.runtime.id===t.host?(chrome.contextMenus.update("convertPageContextMenu",{visible:!1}),chrome.contextMenus.update("appendPageContextMenu",{visible:!1})):(chrome.contextMenus.update("convertPageContextMenu",{visible:!0}),chrome.contextMenus.update("appendPageContextMenu",{visible:!0}))}}function w(e){const t=new Date;t.setMinutes(t.getMinutes()+r.VIEWER_RECHECK_CDN_ACCESS_DELAY_MINS),s.setItem("netAccAdT",t.getTime()),s.setItem("netAcc",e)}const P=e=>{if(!e||null===e||"undefined"===e)return!1;let t=[/^chrome\:\/\//,/^chrome\-extension\:\/\//,/^https:\/\/([a-zA-Z\d-]+\.){0,}officeapps.live.com/,/^https\:\/\/*.*\/(saml|login)/,/^https:\/\/sharedcloud.([a-zA-Z\d-]+)+.(s3|s3-accelerate).amazonaws.com/,/^https\:\/\/*.*.\/(login|auth|okta|saml).*\/S*|\/(login|auth|okta|saml|IWA|owa)\//,/^https\:\/\/www.google.com\/search\?q/,/^https\:\/\/www.citibank.*/,/^https:\/\/[^/]*\.*\/([$-/:-?{-~!"^_`\[\]A-Za-z0-9]*)view-sdk/];let n=c.getFeatureMeta("dc-cv-invalid-urls");if(n){n=JSON.parse(n);for(let e in n)n[e]=new RegExp(n[e]);t=n}return!![/^http\:\/\/[^/]/,/^https\:\/\/[^/]/,/^file?:/].find((t=>t.test(e)))&&!t.find((t=>t.test(e)))};function C(){try{return"true"===s.getItem("pdfViewer")}catch(e){return!1}}function D(e,t){for(let n=0;n<e.length;++n){let r=e[n];if(r.name.toLowerCase()===t)return r}}function A(t){if(void 0!==t.responseHeaders){const n=D(t.responseHeaders,"content-type"),r=D(t.responseHeaders,"content-disposition"),s=t.url&&new URLSearchParams(t.url)?.has("acrobatPromotionSource");if(n){const o=n.value.toLowerCase().split(";",1)[0].trim();if(r&&/^\s*attachment[;]?/i.test(r.value)&&!s)return!1;if("application/pdf"===o)return!0;if("application/octet-stream"===o){if(r&&/\.pdf(["']|$)/i.test(r.value))return e.event(e.e.VIEWER_PDF_PROCESS_OS_CD),!0;t.url.toLowerCase().indexOf(".pdf")>0&&e.event(e.e.VIEWER_PDF_PROCESS_OS_URL)}}}else if("file"==function(e){let t=e.indexOf("/");return e.substr(0,t-1)}(t.url)&&"PDF"==function(e){if(e)try{let t=new URL(e).pathname;return t.substr(t.lastIndexOf(".")+1).toUpperCase()}catch(e){return""}return""}(t.url))return!0;return!1}const F=async(r,s,a)=>{"loading"===s.status&&s.url&&(n.consoleLog("onTabsUpdated",s.url),chrome.tabs.sendMessage(r,{main_op:"rerunGenAiWebpage"}));const c=a.incognito;if("complete"===a.status&&L(a),"loading"===s.status){t.loading({id:r});try{n.isViewerURL(a.url)&&(!C()&&!i.isTabURLFromExternalTouchPoint(a.url)||c)&&T(r,i.extractUrlValue("pdfurl",a.url))}catch(e){}}else if("complete"===s.status){o.newTab(a.url,r);const t=await chrome.extension.isAllowedFileSchemeAccess();a.url.toLowerCase().startsWith("file://")&&a.url.toLowerCase().endsWith(".pdf")&&!t&&(l(a),e.event(e.e.LOCAL_FILE_OPENED))}};function T(e,t){e&&t&&chrome.tabs.update(e,{url:t})}function O(e){try{const t=new URL(e.url),n=new URLSearchParams(t.search);let r=!1;const s=e.initiator;return s&&["https://classroom.google.com","https://mail.google.com","https://drive.google.com"].includes(s)&&n.forEach(((e,t)=>{t.startsWith("print")&&"true"===e&&(r=!0)})),r}catch(e){return!1}}async function S(t){try{if(s.getItem("retryOnNextPage")){await chrome.scripting.executeScript({target:{tabId:t},files:["content_scripts/injectCopyLSIframe.js"]}),await n.sleep(300),e.event(e.e.LOCAL_STORAGE_MIGRATION_RETRYING);const r=await chrome.runtime.sendMessage({main_op:"copy-ls"});chrome.tabs.sendMessage(t,{content_op:"remove-lsCopy"}).catch((()=>null)),"succeed"===r&&(e.event(e.e.LOCAL_STORAGE_MIGRATION_RETRYING_SUCCESS),s.removeItem("retryOnNextPage"),Object.assign(s.storage,await chrome.storage.local.get()))}}catch(e){}}const y=(N=chrome.runtime.getURL(""),N?.replace(/\/+$/,""));var N;function U(t){t.initiator!==y&&"main_frame"!==t.type&&function(t){if(void 0!==t.responseHeaders){const n=D(t.responseHeaders,"content-type"),r=D(t.responseHeaders,"content-disposition");if(n){const s=n.value.toLowerCase().split(";",1)[0].trim();if("application/pdf"===s)return e.event(e.e.VIEWER_PDF_DETECT_EMBED_PDF_TYPE_PDF),!0;if("application/octet-stream"===s){if(r&&/\.pdf(["']|$)/i.test(r.value))return e.event(e.e.VIEWER_PDF_DETECT_EMBED_PDF_OCTET_CD),!0;if(new URL(t.url).pathname.toLowerCase().endsWith(".pdf"))return e.event(e.e.VIEWER_PDF_DETECT_EMBED_PDF_OCTET_URL),!0}}}return!1}(t)&&e.event(e.e.VIEWER_PDF_DETECT_EMBED_PDF)}function W(t){a.isMimeHandlerAvailable().then((async o=>{const{tabId:a}=t;if(await S(a),s.getItem("sessionStarted")||(s.setItem("sessionId",n.uuid()),s.setItem("sessionStarted",!0)),!o){let s=n.parseExtensionURL(t.url);if(s){s=E+"?pdfurl="+s+"&tabId="+a+"&aw=true";let n=t.url.indexOf("#");n>0&&(s+=t.url.slice(n)),r.VIEWER_ENABLED&&C()&&!g&&e.event(e.e.VIEWER_EXTN_PDF_OPENED,{tabURL:s}),chrome.tabs.update(a,{url:s})}}}))}function x(t){try{c.hasFlag("dc-cv-enable-webpage-domain-tracking",m.NO_CALL).then((n=>{if(n&&"main_frame"===t.type){const n=new URL(t.url).hostname;(r=t.tabId,new Promise((e=>{_[r]=e}))).then((t=>{e.event(e.e.VIEWER_PDF_WEBPAGE_OPENED,{domain:n,hasPDFLink:t})}))}var r}))}catch(e){}}function M(o,c){a.isMimeHandlerAvailable().then((async a=>{s.getItem("sessionStarted")||(s.setItem("sessionId",n.uuid()),s.setItem("sessionStarted",!0));const{tabId:l}=o;if(a){const e=O(o),t=o.tabId;t>-1&&(I[t]={isGooglePrint:e})}else{if("main_frame"===o.type){if(await S(l),!function(){if(!navigator.onLine&&s.getItem("offlineSupportDisable"))return!1;const e=new Date,t=s.getItem("netAcc"),n=s.getItem("netAccAdT");return!(n&&n>e.getTime())||t}()||!function(t){if("GET"!==t.method||!r.VIEWER_ENABLED||!C())return!1;let n=t.url,o=`reloadurl-${t.tabId}`;const a=s.getItem(o);if(a&&a===n){try{s.removeItem(o)}catch(e){}return!1}if(!P(n))return!1;const i=A(t);return g?(i&&e.event(e.e.VIEWER_FALLBACK_TO_NATIVE_INCOGNITO),!1):i}(o))return;if(c&&!h)return void e.event(e.e.VIEWER_PDF_LOCAL_FILE_IGNORED);f=o.url,c&&e.event(e.e.VIEWER_PDF_LOCAL_FILE),i.updateVariables(t.version),i.fetchAndUpdateVersionConfig();let{viewerURL:n,acceptRanges:a}=function(e){let t=E,n=!1;if(t+="?pdfurl="+encodeURIComponent(e.url),t+="&tabId="+e.tabId,void 0!==e.responseHeaders){const r=b(e);r&&(t+="&clen="+r);const s=D(e.responseHeaders,"accept-ranges");s&&s.value&&"bytes"===s.value.toLowerCase()&&!O(e)&&(t+="&chunk=true",n=!0);const o=D(e.responseHeaders,"content-disposition");if(o&&o.value&&/\.pdf(["']|$)/i.test(o.value)){const e=/filename[^;=\n\*]?=((['"]).*?\2|[^;\n]*)/.exec(o.value);null!=e&&e.length>1&&(t+="&pdffilename="+e[1].replace(/['"]/g,""))}}return O(e)&&(t+="&googlePrint=true"),{viewerURL:t,acceptRanges:n}}(o);e.event(e.e.VIEWER_EXTN_PDF_OPENED,{tabURL:f});const m=b(o),d=void 0!==m?Number(m):m;O(o)||u.setupWorkerOffscreen({tabId:l,pdfURL:o.url,pdfSize:d,acceptRanges:a});for(let e=0;e<20;e++)try{await chrome.tabs.update(l,{url:n})}catch(e){}}"sub_frame"===o.type&&function(e){return/^https:\/\/[^/]*(acrobat|adobe)\.com\/proxy\/chrome-viewer/.test(e)}(o.url)&&(200!==o.statusCode?403===o.statusCode?(s.setItem("pdfViewer","false"),e.event(e.e.VIEWER_FALLBACK_TO_NATIVE_CDN_FORBIDDEN,{tabURL:o.url}),T(o.tabId,f)):(w(!1),e.event(e.e.VIEWER_FALLBACK_TO_NATIVE_CDN_OFFLINE,{tabURL:o.url}),T(o.tabId,f)):w(!0))}}))}function V(t){try{if(!s.getItem("ignorePrefetchRequests"))return!1;if(v.size>200)return v.clear(),!1;const n=`${t.tabId}_${t.requestId}`,r=v.get(n);if(r){if(r===t.url){v.delete(n);return A(t)&&e.event(e.e.PDF_PREFETCH_REQUEST_SKIPPED),!0}v.delete(n)}return!1}catch(e){return!1}}function G(e){try{if(!s.getItem("ignorePrefetchRequests"))return;const t=e.requestHeaders||[];if(t.some((e=>{const t=e.name.toLowerCase(),n=e.value.toLowerCase();return("purpose"===t||"sec-purpose"===t)&&n.includes("prefetch")}))){const t=`${e.tabId}_${e.requestId}`;v.set(t,e.url)}}catch(e){}}function H(e){try{if(!s.getItem("ignorePrefetchRequests"))return;const t=[];for(const[n,r]of v.entries())n.startsWith(`${e}_`)&&t.push(n);t.forEach((e=>v.delete(e)))}catch(e){}}t.registerHandlers({"check-is-google-print":function(e,t,n){return I&&I[e.tabId]?n({isGooglePrint:I[e.tabId].isGooglePrint}):n({isGooglePrint:!1})},"resolve-has-pdf-promise":({hasPDFLink:e,tabId:t})=>function(e,t){const n=_[t];n&&(n(e),delete _[t])}(e,t)});export{M as processRequest,W as honorRequest,R as onTabActivated,F as onTabsUpdated,U as detectEmbeddedPDF,x as logWebpageDomain,V as handlePrefetchValidation,G as detectAndStorePrefetch,H as cleanupPrefetchForTab};