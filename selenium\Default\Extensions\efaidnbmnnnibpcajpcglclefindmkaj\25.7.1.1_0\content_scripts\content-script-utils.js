/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
let initDcLocalStoragePromise,scrollY;const landingURL=document.URL;let currentURL=document.URL;function isGoogleQuery(e){if(!e)return!1;try{const t=new URL(e);if(t.host.startsWith("www.google.")||t.host.startsWith("www.bing."))return!0}catch(e){return!1}return!1}function isSupportedBrowserVersion(){const e=navigator.userAgent.match(/Chrome\/([0-9]+)/);return!(e&&e.length>=2)||+e[1]>=SETTINGS.SUPPORTED_VERSION}function checkForThirdPartyCookiesStatus(e){const t=document.createElement("iframe");t.id="third-party-cookies-checker",t.style.display="none",t.src=chrome.runtime.getURL("browser/js/check-cookies.html");const o=(n,r,a)=>{"thirdPartyCookiesChecked"===n.content_op&&(t.remove(),chrome.runtime.onMessage.removeListener(o),e&&e(n.status))};chrome.runtime.onMessage.addListener(o),document.documentElement.appendChild(t)}const functionThrottler=(e,t)=>{let o=0;return function(...n){const r=Date.now();r-o>=t&&(o=r,e.apply(this,n))}};let scrollTimeout;window.addEventListener("scroll",(()=>{clearTimeout(scrollTimeout),scrollTimeout=setTimeout((()=>{scrollY=window.scrollY}),100)}));const isPageScrolledByHashRoute=()=>{const e=new URL(document.URL),t=new URL(landingURL),o=e.origin+e.pathname,n=t.origin+t.pathname,r=e.hash.slice(1);return!!r&&(o===n&&(document.getElementById(r)||document.querySelector(`a[name="${r}"]`)))},renderGenAIWebpageFAB=()=>{$(document).ready((()=>{!async function(){const e=isPageScrolledByHashRoute(),t=!e&&window.scrollY>1&&window.scrollY!==scrollY&&document.URL!==landingURL&&document.URL!==currentURL;if(e&&(window.dcLocalStorage.getItem("enableURLHashScrollingLogging")&&chrome.runtime.sendMessage({main_op:"log-info",log:{message:"Page scrolled by hash route detected",hashURL:document.URL}}),window.dcLocalStorage.getItem("enableURLHashScrollingFeature")))return;const o=await GenAIWebpageEligibilityService.shouldShowTouchpoints();if(window.dcLocalStorage.getItem("genAiWebpageCoachmarkData")){const e=new FABManager;o?(await initDcLocalStoragePromise,e.renderFAB({enableHoveredState:!window.dcLocalStorage.getItem("fabHoveredStateShown")}),window.dcLocalStorage.setItem("fabHoveredStateShown",!0)):e.removeFAB()}window.dcLocalStorage.getItem("enableURLChangeOnScrollLogging")&&o&&(chrome.runtime.sendMessage({main_op:"log-info",log:{message:"Rendering Gen AI sidepanel FAB for the webpage"}}),t&&(chrome.runtime.sendMessage({main_op:"log-info",log:{message:"URL changed due to page scroll irrespective of sidepanel being opened or closed",nextURL:document.URL,currentURL:currentURL}}),currentURL=document.URL)),chrome.runtime.sendMessage({type:"send_to_sidepanel",main_op:"page_switched",qualified:o,isScrolled:t,url:document.URL}),scrollY=window.scrollY}()}))},throttledRenderGenAIWebpageFAB=functionThrottler(renderGenAIWebpageFAB,300),initDcLocalStorageHelper=async()=>{const e=chrome.runtime.getURL("./common/local-storage.js"),{dcLocalStorage:t}=await import(e);return await t.init(),t},initDcLocalStorage=async()=>(initDcLocalStoragePromise||(initDcLocalStoragePromise=initDcLocalStorageHelper()),window.dcLocalStorage||(window.dcLocalStorage=await initDcLocalStoragePromise),window.dcLocalStorage);function getCleanedHtml(){try{const e=document.cloneNode(!0),t=new Set(["script","style","iframe","head","meta","noscript","template","link","colgroup","col","track","param","source","slot","base","map","embed","area","applet"]),o=new Set(["href","title","src","id","alt"]);t.forEach((t=>{e.querySelectorAll(t).forEach((e=>e.remove()))}));const n=e.createTreeWalker(e.documentElement,NodeFilter.SHOW_ELEMENT,null);for(;n.nextNode();){const e=n.currentNode;[...e?.attributes||[]].forEach((t=>{o.has(t.name)||e.removeAttribute(t.name)}))}return e.documentElement.outerHTML}catch(e){return document.documentElement.outerHTML}}function PromiseTimeout(e,t={}){const o=new Promise((e=>{const o=setTimeout((()=>{clearTimeout(o),e(t?.defaultVal)}),t?.timeout)}));return Promise.race([e,o])}initDcLocalStorage(),chrome.runtime.onMessage.addListener(((e,t,o)=>{if("removeActionableCoachmark"===e.type){(new ActionableCoachmark).remove()}return(async()=>{switch(e.main_op){case"getHtmlContent":o({htmlContent:getCleanedHtml(),url:document.URL,initialQuestion:initialQuestion,disqualified:!await GenAIWebpageEligibilityService.isCurrentWebPageReadable(),textContent:document.documentElement.innerText});break;case"highlightText":{const t=new AttributionManager;o(await t.highlightText(e));break}case"canHighlightText":{const t=new AttributionManager;o(await t.canHighlightText(e));break}case"sanitiseSources":{const t=new AttributionManager;o(await t.sanitiseSources(e));break}case"removeHighlights":(new AttributionManager).removeHighlights();break;case"shouldDisableGenAIForWebPagesTPs":o(await GenAIWebpageEligibilityService.shouldDisableTouchpoints());break;case"shouldShowGenAIForWebPagesTPs":o(await GenAIWebpageEligibilityService.shouldShowTouchpoints());break;case"rerunGenAiWebpage":GenAIWebpageEligibilityService.reset(),await initDcLocalStorage(),throttledRenderGenAIWebpageFAB()}})(),!0}));