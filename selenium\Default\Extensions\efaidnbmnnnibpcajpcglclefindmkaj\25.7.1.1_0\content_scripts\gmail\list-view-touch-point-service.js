/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import state from"./state.js";import{getClosestElementBasedOnSelector,getElementBasedOnSelector,getArrayElementBasedOnSelector,createAcrobatIconElement,isOrphanContentScript,createURLForAttachment,LIST_VIEW,isDriveFileDirectDownloadLink,isDriveLinkAttachmentTouchPointEnabled}from"./util.js";import{isDefaultViewer,sendAnalyticsWithGMailDVFeatureStatus,openPdfInNewTab}from"./default-viewership-service.js";import{sendAnalytics}from"../gsuite/util.js";import{createFteTooltip,shouldShowFteTooltip,updateFteToolTipCoolDown,acrobatTouchPointClicked,getAcrobatTouchPointFteEligibility,removeGsuiteFteTooltip}from"../gsuite/fte-utils.js";import{sendAnalyticsIfNativeViewerLaunched,processForNativeViewer}from"./native-viewer-touch-point-service.js";const ACROBAT_PROCESSED_ATTRIBUTE="acrobat-icon-added",LIST_VIEW_TOUCH_POINT_CLASS="acrobat-attachmentlistview-hyperlink",LIST_VIEW_TOUCH_POINT_VISIBLE="acrobat-gmail-attachment-visible",GMAIL_FTE_TOOLTIP_STORAGE_KEY="acrobat-gmail-fte-config",GMAIL_FTE_TOOLTIP_CONTAINER_CLASS="acrobat-fte-tooltip-container",revertAttachmentParentDivStyle=t=>{t?.style?.removeProperty("padding-right")},createAcrobatTooltip=()=>{const t=document.createElement("div");return t.setAttribute("class","acrobat-attachmentlistview-hyperlink-tooltip"),t.innerText=state?.gmailConfig?.acrobatPromptText,t},handleAcrobatHyperLinkClickForFte=()=>{const t=document.getElementsByClassName("acrobat-fte-tooltip-container");t?.length>0&&(state.fteToolTip.eligibleFte.type="",state.fteToolTip.touchPointClicked=!0,removeFteTooltipFromAttachmentDiv(),sendAnalytics(["DCBrowserExt:GmailFTE:ListView:Dismissed"])),document.removeEventListener("click",handleFteClickOutside),acrobatTouchPointClicked("acrobat-gmail-fte-config")};function resetTitleFromOldTitle(t){const e=t?.getAttribute("oldTitle");e&&(t?.setAttribute("title",e),t?.setAttribute("oldTitle",""))}const createAcrobatHyperlinkForListViewAttachment=(t,e)=>{const i=t.url,o=t.name,n=document.createElement("a");n.setAttribute("class",LIST_VIEW_TOUCH_POINT_CLASS);const a=createURLForAttachment(i,"GmailListView",o);n.setAttribute("href",a),n.setAttribute("target","_blank");let s=createAcrobatIconElement();return s.setAttribute("src",state?.iconURLListView),n.appendChild(s),n.addEventListener("click",(i=>{i.stopPropagation(),resetTitleFromOldTitle(e);sendAnalytics([[t?.isDriveAsset?"DCBrowserExt:Gmail:ListViewPrompt_DriveLink:Clicked":"DCBrowserExt:Gmail:ListViewPrompt:Clicked",{gsuiteFte:getAcrobatTouchPointFteEligibility(state?.gmailConfig?.enableGmailFteToolTip)}]]),handleAcrobatHyperLinkClickForFte()})),n.addEventListener("mouseenter",(t=>{e?.setAttribute("oldTitle",e?.getAttribute("title")),t?.target?.parentElement?.setAttribute("title","")})),n.addEventListener("mouseleave",(()=>{resetTitleFromOldTitle(e)})),n},getListViewPDFAttachmentsWithoutAcrobatIcon=(t,e)=>{if(!getElementBasedOnSelector(t,"pdfAttachmentWithoutAcrobatIcon","listView"))return;const i=getArrayElementBasedOnSelector(t,"pdfAttachmentWithoutAcrobatIcon","listView");let o={};if(i&&i.length>0)for(let t=0;t<i.length;t++){const n=getClosestElementBasedOnSelector(i[t],"threadElement","listView"),a=n.querySelector("[data-thread-id]");if(!a)return;const s=a.getAttribute("data-thread-id"),r=s?.substring(s?.indexOf("#")+1);isDataPresentForThreadId(r,e)&&(o[r]?o[r].pdfAttachments.push(i[t]):(o[r]={},o[r].pdfAttachments=[i[t]],o[r].threadElement=n))}return o},getSortedMessageIds=t=>{let e=Object.keys(t).sort(((e,i)=>-t[e].timestamp+t[i].timestamp));const i=e.filter((e=>t[e]?.deleted));return e=e.filter((e=>!t[e]?.deleted)),e.push(...i),e},getAttachmentURLAgainstName=(t,e)=>{const i={};for(let o=0;o<t.length;o++){const n=e[t[o]];if(n.attachments?.length>0)for(let t=0;t<n.attachments.length;t++){const e=n.attachments[t];i[e.name]?i[e.name].push(e):i[e.name]=[e]}if(n.driveAttachments?.length>0)for(let t=0;t<n.driveAttachments.length;t++){const e=n.driveAttachments[t];i[e.name]?i[e.name].push(e):i[e.name]=[e]}}return i},isDataPresentForThreadId=(t,e)=>{let i=state.getMessagesForThreadId(t);return!i&&e&&e[t]&&(i=e[t].messages),!!i},getDataForThreadId=(t,e)=>{let i=state.getMessagesForThreadId(t);if(!i&&e&&e[t]&&(i=e[t].messages),i){let t=getSortedMessageIds(i);return getAttachmentURLAgainstName(t,i)}return null},isMultipleAttachmentWithSameName=(t,e)=>{const i=t[e];return i?.length>1},getAttachmentDetailsFromThreadData=(t,e,i)=>{const o=t[e];if(!o)return null;for(let t=0;t<o?.length;t++)if(!i.includes(o[t].url))return i.push(o[t].url),o[t]},addEventListenerToListViewPDFAttachment=t=>{t?.forEach((t=>{const e=getClosestElementBasedOnSelector(t,"attachmentDiv","listView");e&&(t.setAttribute("acrobat-icon-added","Y"),e.addEventListener("click",(()=>{sendAnalytics(["DCBrowserExt:Gmail:ListViewAttachment:Clicked"]),setTimeout((()=>{sendAnalyticsIfNativeViewerLaunched("Shown")}),500)}),{signal:state?.eventControllerSignal}))}))},makeTouchPointVisibleWhenAttachmentIsHovered=(t,e,i)=>{t.addEventListener("mouseenter",(()=>{isLargeLayout(i)&&!isOrphanContentScript()&&(e.style.display="flex",t.style.paddingRight="0")}),{signal:state.eventControllerSignal})},hideTouchPointWhenUserLeavesTheThread=(t,e,i)=>{i?.addEventListener("mouseleave",(()=>{isLargeLayout(t)&&e&&(e.style.display="none",e.classList.contains(LIST_VIEW_TOUCH_POINT_VISIBLE)||i.style.removeProperty("padding-right"))}),{signal:state.eventControllerSignal})},removeTouchPointFromAttachmentDiv=t=>{const e=t?.getElementsByClassName(LIST_VIEW_TOUCH_POINT_CLASS);e?.length>0&&t.removeChild(e[0])},removeFteTooltipFromAttachmentDiv=()=>{const t=document.getElementsByClassName(LIST_VIEW_TOUCH_POINT_VISIBLE);if(t&&t[0]){const e=getClosestElementBasedOnSelector(t[0],"threadElement","listView");e&&e?.style?.removeProperty("z-index"),t[0].style.display="none",t[0].appendChild(createAcrobatTooltip()),t[0].parentElement.style.removeProperty("padding-right"),t[0].setAttribute("class",LIST_VIEW_TOUCH_POINT_CLASS)}removeFteToolTip()},removeFteToolTip=()=>{removeGsuiteFteTooltip(),state.fteToolTip.eligibleFte={type:""}},handleFteClickOutside=(t,e)=>{document.getElementsByClassName("acrobat-fte-tooltip-container").length>0&&"listView"===state?.fteToolTip?.eligibleFte?.type&&(e.contains(t.target)||(sendAnalytics(["DCBrowserExt:GmailFTE:ListView:Dismissed"]),document.removeEventListener("click",handleFteClickOutside))),removeFteTooltipFromAttachmentDiv()},addFteTooltipButtonEventListener=(t,e,i,o)=>{o.querySelector(".acrobat-fte-tooltip-button").addEventListener("click",(()=>{removeFteTooltipFromAttachmentDiv(),sendAnalytics(["DCBrowserExt:GmailFTE:ListView:Clicked"]),document.removeEventListener("click",handleFteClickOutside)}))},handleAcrobatTouchPointClickForFte=()=>{removeFteTooltipFromAttachmentDiv(),document.removeEventListener("click",handleFteClickOutside)},addFteTooltipToAttachmentDiv=(t,e,i)=>{let o="listView";isLastThreadInMail(t)&&(o="listViewLastThread");const n=createFteTooltip(state?.gmailConfig?.gmailFteToolTipStrings,o);addFteTooltipButtonEventListener(0,0,0,n),i.style.zIndex="3",e.addEventListener("click",(()=>handleAcrobatTouchPointClickForFte()),{once:!0}),document.addEventListener("click",(t=>handleFteClickOutside(t,n)),{once:!0,signal:state?.eventControllerSignal});const a=state?.gmailConfig?.fteConfig?.tooltip;""===state?.fteToolTip?.eligibleFte?.type&&(state.fteToolTip.eligibleFte={type:"listView",href:window.location.href},sendAnalyticsWithGMailDVFeatureStatus(["DCBrowserExt:GmailFTE:ListView:Shown"]),updateFteToolTipCoolDown(a,"acrobat-gmail-fte-config").then((t=>{state.fteToolTip={...state?.fteToolTip,...t}}))),e.appendChild(n)},isLastThreadInMail=t=>{const e=t.getBoundingClientRect();return window.innerHeight-e.bottom<130},isThreadVisibleInViewPort=t=>{const e=t.getBoundingClientRect();return e.top>=0&&e.bottom<=window.innerHeight},shouldShowListViewFte=(t,e)=>{if(!isThreadVisibleInViewPort(e))return!1;if(state?.isAcrobatDefaultForGmailPDFs)return!1;if(document.getElementsByClassName("acrobat-fte-tooltip-container").length>0)return!1;if(!isLargeLayout(e))return!1;const i=window.location.href,o=state?.fteToolTip?.eligibleFte;return"listView"===o?.type?o?.href===i:(""===o?.type||"listView"===o?.type)&&t},handleGmailFteTooltip=(t,e,i,o)=>{shouldShowListViewFte(t,o)?(e.style.paddingRight="0",i.classList.add(LIST_VIEW_TOUCH_POINT_VISIBLE),addFteTooltipToAttachmentDiv(e,i,o)):i.appendChild(createAcrobatTooltip())},addTouchPointToAttachmentDiv=(t,e,i)=>{if(isDefaultViewer())return;const o=createAcrobatHyperlinkForListViewAttachment(e,t),n=state?.gmailConfig?.fteConfig?.tooltip;makeTouchPointVisibleWhenAttachmentIsHovered(t,o,i),hideTouchPointWhenUserLeavesTheThread(i,o,t),t.appendChild(o),shouldShowFteTooltip(n,state?.fteToolTip,state?.gmailConfig?.enableGmailFteToolTip).then((e=>{handleGmailFteTooltip(e,t,o,i)}))},handleAttachmentDivClickForFte=()=>{const t=document.getElementsByClassName("acrobat-fte-tooltip-container");t?.length>0&&(removeGsuiteFteTooltip(),sendAnalytics(["DCBrowserExt:GmailFTE:ListView:Dismissed"])),state.fteToolTip.eligibleFte.type="nativeView",document.removeEventListener("click",handleFteClickOutside)},handleClickAction=(t,e)=>{if("click"===t.type||"keydown"===t.type&&"Enter"===t.key){isDefaultViewer()&&e?.url?(t.preventDefault(),t.stopPropagation(),openPdfInNewTab({url:e.url,touchPoint:"GmailListView",attachmentName:e.name})):(handleAttachmentDivClickForFte(),setTimeout((()=>processForNativeViewer(e,LIST_VIEW)),500),setTimeout((()=>processForNativeViewer(e,LIST_VIEW)),1e3));sendAnalyticsWithGMailDVFeatureStatus([`DCBrowserExt:Gmail:ListViewPDFAttachment${e?.isDriveAsset?"_DriveLink":""}:Clicked`])}},addClickListenerToAttachmentDiv=(t,e)=>{t.addEventListener("click",(t=>handleClickAction(t,e)),{signal:state?.eventControllerSignal}),t.addEventListener("keydown",(t=>handleClickAction(t,e)),{signal:state?.eventControllerSignal})},addClickListenerToDriveAttachmentDiv=(t,e)=>{t.addEventListener("click",(()=>{sendAnalytics(["DCBrowserExt:Gmail:ListViewDrivePDFAttachment:Clicked"]),e&&!e?.isDriveAsset||setTimeout((()=>sendAnalyticsIfNativeViewerLaunched("DriveAttachment")),500)}),{signal:state?.eventControllerSignal})},shouldShowTouchPoint=t=>t?.isDriveAsset?isDriveLinkAttachmentTouchPointEnabled()&&isDriveFileDirectDownloadLink(t.url):!!t,processForAttachment=(t,e,i,o)=>{const n=getClosestElementBasedOnSelector(t,"attachmentDiv","listView");if(n){removeTouchPointFromAttachmentDiv(n),t.setAttribute("acrobat-icon-added","Y");const a=n.getAttribute("title"),s=isMultipleAttachmentWithSameName(e,a);let r=getAttachmentDetailsFromThreadData(e,a,i);if(s&&!r.url?.includes("showSameMailAttachmentToast")&&(r={...r,url:r.url+"&showSameMailAttachmentToast=true"}),shouldShowTouchPoint(r)){const t=o.threadElement;addClickListenerToAttachmentDiv(n,r),addTouchPointToAttachmentDiv(n,r,t)}else addClickListenerToDriveAttachmentDiv(n,r)}},processForThread=(t,e,i)=>{const o=i.pdfAttachments,n=getDataForThreadId(t,e);if(!n)return;const a=[];for(let t of o)processForAttachment(t,n,a,i)},processForAllThreads=(t,e)=>{Object.keys(t).forEach((i=>{const o=t[i].pdfAttachments;if(o?.length>0){const o=t[i];processForThread(i,e,o)}}))},removeAllTouchPoints=()=>{const t=document.querySelectorAll(`.${LIST_VIEW_TOUCH_POINT_CLASS}`);if(t?.length>0)for(let e=0;e<t.length;e++)revertAttachmentParentDivStyle(t[e]?.parentElement),t[e]?.parentElement?.removeChild(t[e]);removeFteToolTip()},addAcrobatTouchPointInTheListView=(t,e)=>{const i=getListViewPDFAttachmentsWithoutAcrobatIcon(t);i&&processForAllThreads(i,e)},getListViewPDFAttachmentsWithoutAcrobatIconForClickListener=()=>getElementBasedOnSelector(document,"threadElement","listView")?getArrayElementBasedOnSelector(document,"pdfAttachmentWithoutAcrobatIcon","listView"):null,addEventListenersToListViewAttachments=()=>{const t=getListViewPDFAttachmentsWithoutAcrobatIconForClickListener();t&&addEventListenerToListViewPDFAttachment(t)},isLargeLayout=t=>t?.getBoundingClientRect()?.width>900,getVisibleListView=()=>{const t=getArrayElementBasedOnSelector(document,"listView","listView");for(let e=0;e<t?.length;e++)if("none"!==t[e].style?.display)return t[e];return null},processForListView=t=>{try{if(chrome?.runtime?.id)if(state?.gmailConfig?.enableListViewPromptInGmail||window.location?.search?.includes("enableAcrobatPromptInGmail")){const e=getVisibleListView();e&&addAcrobatTouchPointInTheListView(e,t)}else addEventListenersToListViewAttachments()}catch(t){sendAnalytics([["DCBrowserExt:Gmail:ListView:ProcessingError"]])}};export{processForListView,removeAllTouchPoints};