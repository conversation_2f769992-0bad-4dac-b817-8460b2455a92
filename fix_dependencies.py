#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复一键发布功能依赖问题的脚本
"""

import subprocess
import sys
import os

def install_missing_dependencies():
    """安装缺失的依赖"""
    missing_deps = [
        'pyperclip',
        'beautifulsoup4',
        'lxml'  # beautifulsoup4的解析器
    ]
    
    print("🔧 开始安装缺失的依赖...")
    
    for dep in missing_deps:
        try:
            print(f"📦 安装 {dep}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
            print(f"✅ {dep} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {dep} 安装失败: {e}")
            return False
    
    print("🎉 所有依赖安装完成！")
    return True

def verify_imports():
    """验证导入是否正常"""
    print("\n🧪 验证导入...")
    
    try:
        # 测试基础依赖
        import pyperclip
        import bs4
        print("✅ 基础依赖导入成功")
        
        # 测试发布器导入
        from src.services.platform_publisher.selenium_douyin_publisher import SeleniumDouyinPublisher
        print("✅ Selenium发布器导入成功")
        
        # 测试发布器工厂
        from src.services.platform_publisher.publisher_factory import PublisherFactory
        platforms = PublisherFactory.get_supported_platforms()
        print(f"✅ 发布器工厂正常，支持 {len(platforms)} 个平台")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 一键发布功能依赖修复工具")
    print("=" * 50)
    
    # 1. 安装依赖
    if not install_missing_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return False
    
    # 2. 验证导入
    if not verify_imports():
        print("❌ 导入验证失败，可能还有其他问题")
        return False
    
    print("\n🎉 修复完成！现在可以正常使用一键发布功能了")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)