﻿(function(){function B(c,a){n.apply(this,arguments);try{if(t.test(a)){var b=this.upload.__idm_reqid__="xrq-"+u+"-"+ ++w;this.addEventListener("loadend",C.bind(this,b))}}catch(d){}}function D(c,a){try{this.upload.__idm_reqid__&&"accept"==c.toLowerCase()&&(this.upload.__idm_accept__=!0)}catch(b){}k.apply(this,arguments)}function E(c){try{var a=this.upload,b=a.__idm_reqid__;if(b){var d=a.__idm_accept__;delete a.__idm_accept__;delete a.__idm_reqid__;d||k.call(this,"accept","*/*");k.call(this,"accept",
"application/vnd.t1c."+b)}}catch(e){}p.apply(this,arguments)}function C(c){try{if(c){var a=this.responseText,b=v.exec(a);r([1229212979,c,b&&(b[1]||a)||null],"/")}}catch(d){}}function F(c,a){var b,d,e=x(c);try{if(b=t.test(e?c.url:c)){var l="frq-"+u+"-"+ ++w;e?d=c.headers:a?d=a.headers||(a.headers=new Headers):a=arguments[1]={headers:d=new Headers};d.has("accept")||d.append("accept","*/*");d.append("accept","application/vnd.t1c."+l)}}catch(f){}d=q.apply(this,arguments);try{if(b)return d.then(G.bind(this,
l))}catch(f){}return d}function G(c,a){try{if(c)if(a.ok){var b=a.text,d=a.json;a.text=function(){return b.call(this).then(y.bind(this,c,!1))};a.json=function(){return d.call(this).then(y.bind(this,c,!0))}}else r([1229212979,c],"/")}catch(e){}return a}function y(c,a,b){try{var d=a?JSON.stringify(b):b,e=v.exec(d);r([1229212979,c,e&&(e[1]||d)||null],"/")}catch(l){}return b}function z(c,a){var b=c;a=a.split(".");if("*"==a[0]){a.shift();for(var d of H(c))try{if(x(b=c[d])&&I(b,a[0])){for(var e of a)b=b[e];
return b}}catch(l){}}else try{for(e of a)b=b[e];return b}catch(l){}}function A(c,a){for(;c&&a--;c=c.parentElement);return c}const J=window.origin||document.origin||location.origin,x=Object.isExtensible,K=Array.isArray,h=XMLHttpRequest.prototype,r=window.postMessage.bind(window);var w=0,u=0,t,v,n,p,k,q;const I=Object.a||Function.call.bind(Object.prototype.hasOwnProperty),H=Object.getOwnPropertyNames,L=/ *\$([<>=]) */;window.addEventListener("message",function(c){var a=c.data;if(K(a)&&c.origin==J)switch(a[0]){case 1229212978:u=
a[1];var b=a[2],d=a[3],e=a[4];a=a[5];e?(t=RegExp(e),v=RegExp(a)):b=d=!1;try{b?(n||(n=h.open,h.open=B),p||(p=h.send,h.send=E),k||(k=h.setRequestHeader,h.setRequestHeader=D)):(n&&(h.open=n,n=null),p&&(h.send=p,p=null),k&&(h.setRequestHeader=k,k=null)),d?q||(q=fetch,fetch=F):q&&(fetch=q,q=null)}catch(M){}break;case 1229212980:c=a[1];var l=a[2];a=a[3];var f;try{if(l?f=document.querySelector('[__idm_id__="'+l+'"]'):f=window,f)a:if(d=a.split(L),1<d.length){for(d.shift();f&&(e=d.shift());){var g=d.shift().trim(),
m;for(m=1;"<"==g[g.length-m];m++);--m&&(g=g.slice(0,-m).trim());switch(e){case "<":f=A(g?f.closest(g):f,m);break;case ">":f=A(f.querySelector(g),m);break;case "=":if(g=z(f,g),void 0!==g){b=g;break a}}}b=void 0}else b=z(f,a)}catch(M){}r([1229212981,c,b],"/")}},!1);r([1229212977],"/")})();
