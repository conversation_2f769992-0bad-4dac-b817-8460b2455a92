<!-- FABViewSettings.html -->

<style>
    @font-face {
        font-family: 'AdobeClean-Regular';
        src: url('../../browser/css/fonts/AdobeClean-Regular.otf') format('opentype');
    }
    @font-face {
        font-family: 'AdobeClean-Bold';
        src: url('../../browser/css/fonts/AdobeClean-Bold.otf') format('opentype');
    }
    body {
        font-family: 'AdobeClean-Regular', adobe-clean, "Adobe Clean", sans-serif;
    }

    .dialog-container {
        width: 166px;
        background-color: #ffffff;
        border-radius: 4px;
        border: 1px solid #B1B1B1;
        box-shadow: 0px 1px 4px 0px #00000026;
        z-index: 1000;
        padding: 4px;
        margin-right: -5px;
        position: relative;
    }

    .menu {
        display: flex;
        flex-direction: column;
        gap: 4px;
        list-style-type: none;
        margin: 0;
        padding: 0;
    }

    .menu .menu_item {
        cursor: pointer;
        font-family: 'AdobeClean-Regular', adobe-clean, "Adobe Clean", sans-serif;
        align-content: center;
        font-size: 14px;
        font-weight: 400;
        line-height: 18.2px;
        text-align: left;
        color: #222222;
        padding: 0px 12px;
        height: 32px;
        width: 140px;
    }

    .menu .menu_item:hover {
        background-color: #eaeaea;
    }

    .preferences {
        font-size: 12px;
        font-weight: 400;
        line-height: 18.2px;
        text-align: left;
        color: #464646;
    }

    .divider {
        width: 180px;
        border: none;
        border-top: 1px solid #E6E6E6;
        border-radius: 0.5px;
        margin-left: -8px;
    }
</style>

<div class="dialog-container">
    <ul class="menu">
        <li id="hideForNow" class="menu_item translate"></li>
        <li id="hideFabForDomain" class="menu_item translate"></li>
        <li id="neverDisplayFab" class="menu_item translate"></li>
        <li class="divider"></li>
        <li id="preferences" class="menu_item translate"></li>
    </ul>
</div>
