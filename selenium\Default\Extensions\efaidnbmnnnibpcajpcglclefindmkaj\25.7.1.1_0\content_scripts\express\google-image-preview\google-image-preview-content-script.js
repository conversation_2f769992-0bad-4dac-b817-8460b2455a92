/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
class GoogleImagePreviewExpressIntegration{previewEntryPointId="google-image-preview-adobe-express-entry-point";previewContainerClassname="BIB1wf EIehLd fHE6De Emjfjd";previewedImageClassname="sFlh5c FyHeAf";previewHeaderClassname="HJRshd";touchpoint="googleImagePreview";launchExpress=(e,t)=>{chrome.runtime.sendMessage({main_op:"launch-express",imgUrl:e,intent:t,touchpoint:this.touchpoint})};sendAnalyticsEvent=e=>{try{chrome.runtime.sendMessage({main_op:"analytics",analytics:e})}catch(e){}};sendErrorLog=(e,t)=>{chrome.runtime.sendMessage({main_op:"log-error",log:{message:e,error:t}})};async loadContentScripts(){const e=this.config.singleCTAEnabled?chrome.runtime.getURL("content_scripts/express/single-click-cta.js"):chrome.runtime.getURL("content_scripts/express/dropdown-menu.js"),t=await import(e);this.expressCTA=this.config.singleCTAEnabled?new t.default:t.default}addThumbnailAndImagePreviewerObserver=()=>{new MutationObserver((()=>{this.imagePreviewerObserverHandler()})).observe(document.body,{childList:!0,subtree:!0})};previewEntryPointClickHandler=e=>{if(!chrome?.runtime?.id)return void this.removePreviewEntryPoint(this.previewEntryPointId);if(!this.currentPreviewContainer)return void this.sendErrorLog("Error executing express in google image preview","preview container not found");if(!this.currentPreviewImage||"IMG"!==this.currentPreviewImage.tagName)return void this.sendErrorLog("Error executing express in google image preview","image element not found");const t=this.currentPreviewImage.src;t?this.launchExpress(t,e):this.sendErrorLog("Error executing express in google image preview","image URL not found")};imagePreviewerObserverHandler=()=>{const e=document.getElementsByClassName(this.previewContainerClassname)[0];if(!e)return;this.currentPreviewContainer=e;const t=this.currentPreviewContainer.getElementsByClassName(this.previewedImageClassname)[0];if(!t||"IMG"!==t.tagName||this.currentPreviewImage===t)return;this.currentPreviewImage=t;const i=this.currentPreviewContainer.getElementsByClassName(this.previewHeaderClassname)[0];if(!i)return;const n=document.getElementById(this.previewEntryPointId);n&&this.currentPreviewContainer.contains(n)||(chrome?.runtime?.id?(this.removePreviewEntryPoint(this.previewEntryPointId),this.addPreviewEntryPoint(i,this.previewEntryPointId,this.previewEntryPointClickHandler),this.sendAnalyticsEvent([["DCBrowserExt:Express:GoogleImagePreview:PreviewEntryPoint:Shown"]])):this.removePreviewEntryPoint(this.previewEntryPointId))};toggleButtonSize=e=>{const t=document.getElementById("expressEditImageParentContextMenu");t&&(t.style.display=e?"none":"block");const i=document.getElementsByClassName("cc440d50ba-express-entrypoint-button-icon")[0];if(!i)return;let n="40px",s="7px 8px 7px 14px";e&&(n="32px",s="7px"),i.style.width=n,i.style.padding=s};createTooltip=()=>{const e=document.createElement("div");e.className="cc440d50ba-tooltiptext";const t=util.getTranslation("expressDropdownMenuTooltip");return e.innerHTML=t,e};showHideTooltipOnMouseHover=(e,t)=>{let i=!1;e.addEventListener("mouseenter",(()=>{const s=e.getBoundingClientRect();t.style.top=s.top+window.scrollY+"px",t.style.left=s.left+e.offsetWidth/2+window.scrollX+"px",t.style.transform="translate(-50%, -120%)",i=!0,setTimeout((()=>{i&&(t.style.visibility="visible",window.addEventListener("scroll",n))}),400)}));const n=()=>{t.style.visibility="hidden",window.removeEventListener("scroll",n)};e.addEventListener("mouseleave",(()=>{i=!1,setTimeout((()=>{n()}),100)}))};addPreviewEntryPoint=async(e,t,i)=>{if(document.getElementById(t))return;const n=await this.expressCTA.renderMenuButton(i,this.touchpoint);n.id=t,n.style.display="flex",n.style.alignItems="center",n.style.visibility="hidden",e.insertBefore(n,e.firstChild);if(new ResizeObserver((e=>{n.style.visibility="hidden",this.toggleButtonSize(!1),e[0].contentRect.width-285<n.offsetWidth?(this.toggleButtonSize(!0),n.style.visibility="visible"):(this.toggleButtonSize(!1),n.style.visibility="visible")})).observe(e.parentElement),this.config.singleCTAEnabled)this.expressCTA.attachTooltip("top");else{const e=this.createTooltip();document.body.appendChild(e);const t=n.getElementsByClassName("cc440d50ba-express-entrypoint-button")[0];this.showHideTooltipOnMouseHover(t,e)}};removePreviewEntryPoint=e=>{const t=document.getElementById(e);t&&t.remove();const i=document.getElementsByClassName("cc440d50ba-tooltiptext");for(const e of i)e.remove()};runOnceOnStartup=()=>{this.imagePreviewerObserverHandler()};init=async()=>{this.config=await chrome.runtime.sendMessage({main_op:"google-image-preview-express-init"}),this.config.enableGoogleImagePreviewExpressMenu&&(this.previewContainerClassname=this.config.selectors?.previewContainerClassname||this.previewContainerClassname,this.previewedImageClassname=this.config.selectors?.previewedImageClassname||this.previewedImageClassname,this.previewHeaderClassname=this.config.selectors?.previewHeaderClassname||this.previewHeaderClassname,await this.loadContentScripts(),this.addThumbnailAndImagePreviewerObserver(),this.runOnceOnStartup())}}const googleImagePreviewExpressIntegration=new GoogleImagePreviewExpressIntegration;googleImagePreviewExpressIntegration.init();