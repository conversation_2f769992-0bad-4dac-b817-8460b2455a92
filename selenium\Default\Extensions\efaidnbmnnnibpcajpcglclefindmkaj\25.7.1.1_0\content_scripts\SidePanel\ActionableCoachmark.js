/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
let initialQuestion;class ActionableCoachmark{constructor(){if(ActionableCoachmark.instance)return ActionableCoachmark.instance;ActionableCoachmark.instance=this,this.shadowRoot=null,this.visibilityTimeout=void 0}id="GenAIWebpageFte";sendAnalyticsEvent=e=>{try{chrome.runtime.sendMessage({main_op:"analytics",analytics:e})}catch(e){}};async clickHandler(e){this.remove(),await initDcLocalStorage();const t=window.dcLocalStorage.getItem("genAiWebpageCoachmarkData")||{};window.dcLocalStorage.setItem("genAiWebpageCoachmarkData",{...t,hasUsedActionableCoachMark:!0}),chrome.runtime.sendMessage({type:"open_side_panel",touchpoint:"actionableCoachmark",initialQuestion:e}),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:ActionableCoachmark:Clicked"]]),initialQuestion=e}isRendered(){return Boolean(this.shadowRoot)}remove(e=!0){if(this.isRendered()&&(this.shadowRoot?.host?.remove(),this.shadowRoot=null,this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:ActionableCoachmark:Removed"]]),e)){(new FABManager).renderFAB()}}now=()=>(new Date).getTime();render(){const e=new FABManager;if(e.isFABRendered()&&e.removeFAB(),this.isRendered())return;const t=document.createElement("div");t.id="aiAcShadowRoot",t.style.display="block",this.shadowRoot=t.attachShadow({mode:"open"}),fetch(chrome.runtime.getURL("resources/SidePanel/ActionableCoachmark.html")).then((e=>e.text())).then((e=>{const i=document.createElement("template");i.innerHTML=e;const a=i.content;this.shadowRoot.appendChild(a.cloneNode(!0)),this.shadowRoot.querySelector(".close-btn").addEventListener("click",(()=>{this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:ActionableCoachmark:Closed"]]),this.remove()}));const o=this.shadowRoot.querySelector(".nudgeInput");AnimateCannedQuestions.start(o),o.addEventListener("focus",(()=>{this.visibilityTimeout&&(clearTimeout(this.visibilityTimeout),this.visibilityTimeout=void 0)})),o.addEventListener("keydown",(e=>{"Enter"===e.key&&this.clickHandler(AnimateCannedQuestions.getQuestion())}));this.shadowRoot.querySelector(".submitBtn").addEventListener("click",(async()=>{this.clickHandler(AnimateCannedQuestions.getQuestion())})),util.translateElements(".translate",this.shadowRoot),document.documentElement.appendChild(t),this.updateShowCount(),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:ActionableCoachmark:Displayed"]])})),this.visibilityTimeout=setTimeout((()=>{this.remove(),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:ActionableCoachmark:TimedOut"]])}),3e6)}async isEligible(){return!!await GenAIWebpageEligibilityService.shouldShowTouchpoints()&&(!await GenAIWebpageEligibilityService.shouldDisableTouchpoints()&&this.shouldShow())}async shouldShow(){if(await initDcLocalStorage(),"true"!==window.dcLocalStorage.getItem("enableGenAIFab"))return!1;const{hasUsedActionableCoachMark:e,lastShownTimestamp:t,showCount:i=0}=window.dcLocalStorage.getItem("genAiWebpageCoachmarkData")||{};return!e&&(!t||i<3&&this.now()-t>=864e6)}updateShowCount=async()=>{await initDcLocalStorage();const{showCount:e=0}=window.dcLocalStorage.getItem("genAiWebpageCoachmarkData")||{};window.dcLocalStorage.setItem("genAiWebpageCoachmarkData",{lastShownTimestamp:this.now(),showCount:e+1})}}