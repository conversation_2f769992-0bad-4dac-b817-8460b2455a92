/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
const GSUITE_FTE_TOOLTIP_CONTAINER_CLASS="acrobat-fte-tooltip-container",GSUITE_FTE_TOOLTIP_ELIGIBLE_EVAR="FteEligible",GSUITE_FTE_TOOLTIP_NOT_ELIGIBLE_EVAR="FteNotEligible",EXTENSION_ENVIRONMENT_KEY="env",createFteToolTipDivObject=(t,e)=>{const o=document.createElement("div");return o.setAttribute("class",t),e&&e.length>0&&(o.textContent=e),o},createFteTooltipObject=(t,e,o)=>{const a=createFteToolTipDivObject(e,"");a.setAttribute("class",e),a.id="acrobat-fte-tooltip-container";const n=createFteToolTipDivObject("acrobat-fte-tooltip",""),i=createFteToolTipDivObject(o,"");if(n.appendChild(i),t?.title?.length>0){const e=createFteToolTipDivObject("acrobat-fte-tooltip-title",t.title);n.appendChild(e)}if(t?.description?.length>0){const e=createFteToolTipDivObject("acrobat-fte-tooltip-description",t.description);n.appendChild(e)}if(t?.button?.length>0){const e=document.createElement("button");e.setAttribute("class","acrobat-fte-tooltip-button"),e.textContent=t.button,n.appendChild(e)}return a.appendChild(n),a},createFteTooltip=(t,e)=>{const o=createFteTooltipObject(t,`acrobat-fte-tooltip-container-${e} acrobat-fte-tooltip-container`,`acrobat-fte-tooltip-arrow-${e} acrobat-fte-tooltip-arrow`);return o.addEventListener("click",(t=>{t.preventDefault(),t.stopPropagation()})),o},getFteCustomCoolDownTime=async()=>{const{env:t}=await getLocalStorageValue("env");if("prod"===t)return 0;const e=new URLSearchParams(window.location.search).get("acrobatTouchPointFteDay");return e?parseInt(e):0},shouldShowFteTooltip=async(t,e,o)=>{const a=(new Date).getTime();if(e?.touchPointClicked||!o)return!1;return!(document.getElementsByClassName("acrobat-fte-tooltip-container").length>0)&&(a>e?.nextDate&&(e?.count<t?.maxFteCount||-1===t?.maxFteCount))},applyCoolDown=(t,e,o)=>{o?t.setSeconds(t.getSeconds()+e):t.setDate(t.getDate()+e)},getCoolDown=(t,e,o)=>{let a,n;return e>0?(a=t%3>0?o.shortCoolDown*e:o.longCoolDown*e,n=!0):(a=t%3>0?o.shortCoolDown:o.longCoolDown,n=!1),{coolDown:a,isSeconds:n}},updateFteToolTipCoolDown=async(t,e)=>{const o=await getLocalStorageValue(e),a=new Date,n=o?.[e]||{count:0,nextDate:a.toISOString()},i=n.count+1,c=await getFteCustomCoolDownTime(),{coolDown:l,isSeconds:r}=getCoolDown(i,c,t);return applyCoolDown(a,l,r),n.nextDate=a.getTime(),n.count=i,await setLocalStorageValue(e,n),n},removeGsuiteFteTooltip=()=>{const t=document.getElementsByClassName("acrobat-fte-tooltip-container");t.length>0&&t[0].remove()},acrobatTouchPointClicked=async t=>{const e=await getLocalStorageValue(t),o=e?.[t];o?.touchPointClicked||(removeGsuiteFteTooltip(),o.touchPointClicked=!0,setLocalStorageValue(t,o))},getAcrobatTouchPointFteEligibility=t=>t?"FteEligible":"FteNotEligible",getLocalStorageValue=async t=>await chrome.storage.local.get(t),setLocalStorageValue=async(t,e)=>await chrome.storage.local.set({[t]:e},(()=>{chrome.runtime?.sendMessage({main_op:t,fteState:e})}));export{createFteTooltip,shouldShowFteTooltip,updateFteToolTipCoolDown,acrobatTouchPointClicked,getAcrobatTouchPointFteEligibility,removeGsuiteFteTooltip};