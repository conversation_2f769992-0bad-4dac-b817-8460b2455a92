# -*- coding: utf-8 -*-
"""
内容生成工作流
协调项目分析、内容生成和平台优化的完整流程
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass

from src.services.project_content_analyzer import (
    ProjectContent, get_project_content_analyzer
)
from src.services.ai_content_generator import (
    GeneratedContent, get_ai_content_generator
)
from src.services.platform_content_optimizer import (
    OptimizedContent, get_platform_content_optimizer
)
from src.utils.logger import logger


@dataclass
class WorkflowProgress:
    """工作流进度"""
    stage: str
    progress: float
    message: str
    platform: str = ""


class ContentGenerationWorkflow:
    """内容生成工作流"""
    
    def __init__(self):
        self.analyzer = get_project_content_analyzer()
        self.generator = get_ai_content_generator()
        self.optimizer = get_platform_content_optimizer()
        
        logger.info("内容生成工作流初始化完成")
    
    async def execute(self, 
                     platforms: List[str], 
                     progress_callback: Optional[callable] = None) -> Dict[str, OptimizedContent]:
        """执行完整的内容生成流程"""
        try:
            results = {}
            
            # 1. 分析项目内容
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="analyze",
                    progress=0.0,
                    message="正在分析项目内容..."
                ))
            
            project_content = self.analyzer.analyze_current_project()
            
            if project_content.is_empty():
                logger.warning("项目内容为空，可能影响生成质量")
                if progress_callback:
                    progress_callback(WorkflowProgress(
                        stage="analyze",
                        progress=1.0,
                        message="项目内容分析完成，但内容为空"
                    ))
            else:
                if progress_callback:
                    progress_callback(WorkflowProgress(
                        stage="analyze",
                        progress=1.0,
                        message=f"项目内容分析完成: {project_content.title}"
                    ))
            
            # 2. 为每个平台生成内容
            total_platforms = len(platforms)
            
            for i, platform in enumerate(platforms):
                platform_progress_base = (i / total_platforms) * 0.8 + 0.1
                
                if progress_callback:
                    progress_callback(WorkflowProgress(
                        stage="generate",
                        progress=platform_progress_base,
                        message=f"正在为{platform}平台生成内容...",
                        platform=platform
                    ))
                
                # 生成内容
                content = await self.generator.generate_content(
                    project_context=project_content,
                    platform=platform
                )
                
                if progress_callback:
                    progress_callback(WorkflowProgress(
                        stage="optimize",
                        progress=platform_progress_base + 0.1,
                        message=f"正在优化{platform}平台内容...",
                        platform=platform
                    ))
                
                # 优化内容
                optimized = self.optimizer.optimize_for_platform(content, platform)
                results[platform] = optimized
                
                if progress_callback:
                    progress_callback(WorkflowProgress(
                        stage="complete",
                        progress=platform_progress_base + 0.2,
                        message=f"{platform}平台内容生成完成",
                        platform=platform
                    ))
            
            # 3. 完成
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="complete",
                    progress=1.0,
                    message=f"所有平台内容生成完成 ({len(results)}/{total_platforms})"
                ))
            
            logger.info(f"内容生成工作流执行完成，生成了 {len(results)} 个平台的内容")
            return results
            
        except Exception as e:
            logger.error(f"内容生成工作流执行失败: {e}")
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="error",
                    progress=1.0,
                    message=f"内容生成失败: {str(e)}"
                ))
            return {}
    
    async def generate_for_platform(self, 
                                  platform: str, 
                                  progress_callback: Optional[callable] = None) -> OptimizedContent:
        """为单个平台生成内容"""
        try:
            # 1. 分析项目内容
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="analyze",
                    progress=0.0,
                    message="正在分析项目内容...",
                    platform=platform
                ))
            
            project_content = self.analyzer.analyze_current_project()
            
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="analyze",
                    progress=0.3,
                    message="项目内容分析完成",
                    platform=platform
                ))
            
            # 2. 生成内容
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="generate",
                    progress=0.4,
                    message=f"正在为{platform}平台生成内容...",
                    platform=platform
                ))
            
            content = await self.generator.generate_content(
                project_context=project_content,
                platform=platform
            )
            
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="generate",
                    progress=0.7,
                    message=f"{platform}平台内容生成完成",
                    platform=platform
                ))
            
            # 3. 优化内容
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="optimize",
                    progress=0.8,
                    message=f"正在优化{platform}平台内容...",
                    platform=platform
                ))
            
            optimized = self.optimizer.optimize_for_platform(content, platform)
            
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="complete",
                    progress=1.0,
                    message=f"{platform}平台内容生成和优化完成",
                    platform=platform
                ))
            
            logger.info(f"{platform}平台内容生成完成")
            return optimized
            
        except Exception as e:
            logger.error(f"{platform}平台内容生成失败: {e}")
            if progress_callback:
                progress_callback(WorkflowProgress(
                    stage="error",
                    progress=1.0,
                    message=f"内容生成失败: {str(e)}",
                    platform=platform
                ))
            
            # 返回空内容
            return OptimizedContent(
                titles=["生成失败，请重试"],
                description="内容生成失败，请重试或手动编辑",
                tags=["自动生成失败"],
                platform=platform
            )


# 全局实例
_workflow = None

def get_content_generation_workflow() -> ContentGenerationWorkflow:
    """获取内容生成工作流实例"""
    global _workflow
    if _workflow is None:
        _workflow = ContentGenerationWorkflow()
    return _workflow


# 便捷函数
async def generate_content_for_platforms(platforms: List[str], 
                                       progress_callback: Optional[callable] = None) -> Dict[str, OptimizedContent]:
    """便捷的多平台内容生成函数"""
    workflow = get_content_generation_workflow()
    return await workflow.execute(platforms, progress_callback)


async def generate_content_for_platform(platform: str,
                                      progress_callback: Optional[callable] = None) -> OptimizedContent:
    """便捷的单平台内容生成函数"""
    workflow = get_content_generation_workflow()
    return await workflow.generate_for_platform(platform, progress_callback)