/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
const e={setViewerState:async function(e){},getStreamInfo:function(){return new Promise((e=>{e(void 0)}))},isMimeHandlerAvailable:function(){return new Promise((e=>{e(!1)}))},caretModeStatus:function(){return new Promise((e=>{e(!1)}))},addCaretModeListener:function(e){},reloadWithNativeViewer:function(e,n){},isInstalledViaUpsell:function(){return new Promise((e=>{e(!1)}))},validateCertificate:async()=>null};export{e as privateApi};