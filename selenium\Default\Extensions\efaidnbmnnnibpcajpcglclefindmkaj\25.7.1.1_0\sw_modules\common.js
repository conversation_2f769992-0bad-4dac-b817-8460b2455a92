/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{util as e}from"./util.js";import{Proxy as t}from"./proxy.js";import{analytics as i}from"../common/analytics.js";import{SETTINGS as s}from"./settings.js";import{dcLocalStorage as r}from"../common/local-storage.js";let o=null,n={prod:{cloud_host:"https://cloud.acrobat.com/",redirect_uri:"https://createpdf.acrobat.com/static/js/aicuc/cpdf-template/sign_in_complete.html",cpdf_host:"https://createpdf.acrobat.com/",frictionlessUrl:"https://acrobat.adobe.com/dc-hosted-extension/iframe-index.html",env:"prod",viewer_ims_client_id:"dc-prod-chrome-viewer",acrobatViewerUrl:"https://acrobat.adobe.com/dc-chrome-extension/index.html",signInUrl:"https://acrobat.adobe.com/dc-chrome-extension/sign-in.html",imsURL:"https://ims-na1.adobelogin.com",floodgateUri:"https://p13n.adobe.io/fg/api",imsLibUrl:"https://auth.services.adobe.com/imslib/imslib.min.js",dcApiUri:"https://dc-api.adobe.io",viewer_ims_client_id_social:"dc-prod-chrome-viewer-social",uninstallUrl:"https://acrobat.adobe.com/dc-chrome-extension/index.html?la=true#/uninstall",welcomePdfUrlHost:"https://acrobat.adobe.com",ims_context_id:"v:2,s,9122f250-90cf-11ed-9fe5-b3719c660a78",loggingUri:"https://dc-api.adobe.io",viewerUrl:{latest_uri:"https://acrobat.adobe.com/dc-chrome-extension/index.html",internal_uri:"https://acrobat.adobe.com/dc-chrome-extension/index.html",external_uri:"https://acrobat.adobe.com/dc-chrome-extension/1.0.4/index.html",version_template:"https://acrobat.adobe.com/dc-chrome-extension/{VERSION}/index.html"},version_config_uri:"https://acrobat.adobe.com/dc-chrome-extension/version-config.json",ote_config_uri:"https://acrobat.adobe.com/dc-chrome-extension/ote-config.json",floodgateUrl:"https://acrobat.adobe.com/dc-chrome-extension/floodgate.html",popup_cdn_uri:"https://acrobat.adobe.com/dc-hosted-extension/react-index.html",sidepanelUrl:"https://acrobat.adobe.com/dc-hosted-extension/react-index.html?#/side-panel",genAIPricingUri:" https://acrobat.adobe.com/proxy/pricing/us/en/acrobat-chrome-gen-ai.html",acrobat_ipm_uri:"https://acroipm2.adobe.com/acrobat-web",express:{pluginUrl:"https://cc-embed.adobe.com/plugin/v4/acrobat",webAppUrl:"https://new.express.adobe.com/new"}},stage:{cloud_host:"https://cloud.stage.acrobat.com/",redirect_uri:"https://createpdf.stage.acrobat.com/static/js/aicuc/cpdf-template/sign_in_complete.html",cpdf_host:"https://createpdf.stage.acrobat.com/",frictionlessUrl:"https://stage.acrobat.adobe.com/dc-hosted-extension/iframe-index.html",env:"stage",viewer_ims_client_id:"dc-stage-chrome-viewer",acrobatViewerUrl:"https://stage.acrobat.adobe.com/dc-chrome-extension/index.html",signInUrl:"https://stage.acrobat.adobe.com/dc-chrome-extension/sign-in.html",imsURL:"https://ims-na1-stg1.adobelogin.com",floodgateUri:"https://p13n-stage.adobe.io/fg/api",imsLibUrl:"https://auth-stg1.services.adobe.com/imslib/imslib.js",dcApiUri:"https://dc-api-stage.adobe.io",viewer_ims_client_id_social:"dc-stage-chrome-viewer-social",uninstallUrl:"https://stage.acrobat.adobe.com/dc-chrome-extension/index.html?la=true#/uninstall",welcomePdfUrlHost:"https://stage.acrobat.adobe.com",ims_context_id:"v:2,s,03c19460-80fe-11ed-a731-c51304049b0d",loggingUri:"https://dc-api-stage.adobe.io",viewerUrl:{latest_uri:"https://stage.acrobat.adobe.com/dc-chrome-extension/index.html",internal_uri:"https://stage.acrobat.adobe.com/dc-chrome-extension/index.html",external_uri:"https://stage.acrobat.adobe.com/dc-chrome-extension/1.0.4/index.html",version_template:"https://stage.acrobat.adobe.com/dc-chrome-extension/{VERSION}/index.html"},version_config_uri:"https://stage.acrobat.adobe.com/dc-chrome-extension/version-config.json",ote_config_uri:"https://stage.acrobat.adobe.com/dc-chrome-extension/ote-config.json",floodgateUrl:"https://stage.acrobat.adobe.com/dc-chrome-extension/floodgate.html",popup_cdn_uri:"https://stage.acrobat.adobe.com/dc-hosted-extension/react-index.html",sidepanelUrl:"https://stage.acrobat.adobe.com/dc-hosted-extension/react-index.html?#/side-panel",genAIPricingUri:"https://stage.acrobat.adobe.com/proxy/pricing/acrobat-chrome-gen-ai.html",acrobat_ipm_uri:"https://acroipm2.stage.adobe.com/acrobat-web",express:{pluginUrl:"https://stage.cc-embed.adobe.com/plugin/v4/acrobat?env=stage",webAppUrl:"https://stage.projectx.corp.adobe.com/new"}}};export function deriveCDNURL(e="prod"){if(!r.getItem("enableCDNVersioning")){const t="acrobatViewerUrl";return n[e]?.[t]}const t="viewerUrl";let i;const s="prod"===e?"prod":"non_prod";let o=n[e]?.[t].external_uri;try{const a=r.getItem("adobeInternal");"true"===a&&(o=n[e]?.[t].internal_uri);const c=r.getItem("version-config");c&&(i="true"===a?c[`iv_${s}`]:c[`ev_${s}`],i&&""!==i&&null!==i&&(o="latest"===i?n[e]?.[t].latest_uri:n[e]?.[t].version_template.replace(new RegExp("{VERSION}","g"),i)))}catch{}return o}export function onMessageListener(e){const t=e.env;switch(e.requestType){case"update_env":{const e="uninstallUrl",i=n[t],s=deriveCDNURL(t),o=i.viewer_ims_client_id,a=i.viewer_ims_client_id_social,c=i.ims_context_id,h=i.imsURL,l=i.imsLibUrl,d=i.dcApiUri,p=i.genAIPricingUri,g=new URL(i[e]);g.searchParams.append("callingApp",chrome.runtime.id),g.searchParams.append("theme",r.getItem("theme")||"auto"),common.settings={...common.settings,...i};try{r.setItem("cdnUrl",s),r.setItem("signInUrl",common.getSignInUrl()),r.setItem("viewerImsClientId",o),r.setItem("viewerImsClientIdSocial",a),r.setItem("imsContextId",c),r.setItem("imsURL",h),r.setItem("imsLibUrl",l),r.setItem("dcApiUri",d),r.setItem("genAIPricingUri",p),r.setItem("sidepanelUrl",common.getSidepanelUrl()),chrome.runtime.setUninstallURL(g.href)}catch(e){}break}}}class a{constructor(){this.$GET_headers={Accept:"application/vnd.adobe.dex+json;version=1",Authorization:null,"x-api-client-id":"api_browser_ext"},this.$POST_headers={Accept:"application/vnd.adobe.dex+json;version=1","Content-Type":"application/vnd.adobe.dex+json;version=1;charset=utf-8",Authorization:null,"x-api-client-id":"api_browser_ext"},this.reset()}proxy(...e){return t.proxy.bind(this)(...e)}uuid(){return"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".replace(/x/g,(function(){return"0123456789abcdef"[Math.floor(16*Math.random())]}))}deriveCDNURL(e="prod"){return deriveCDNURL(e)}reset(t="prod"){this.settings={cpdf_api:null,files_host:null,files_api:null,files_upload:null,files_root:null,fillsign_api:null,auth_token:null,ims_host:null},this.server=n[t]||n[Object.keys(n)[0]];const i=this.deriveCDNURL(t);this.settings=e.extend(this.settings,this.server);const s="acrobatViewerUrl";this.settings={...this.settings,[s]:i}}connected(){return!!this.settings.auth_token}setGlobals(e){this.globals=e}getFrictionlessUri(){const e="frictionlessUrl";return this.settings[e]||n.prod[e]}getPopupCDNUrl(){return this.settings.popup_cdn_uri||n.prod.popup_cdn_uri}getUninstallUrl(){const e="uninstallUrl";return this.settings[e]||n.prod[e]}getVersionConfigUrl(){return this.settings.version_config_uri?this.settings.version_config_uri:n.prod.version_config_uri}getOteConfigUrl(){return this.settings.ote_config_uri?this.settings.ote_config_uri:n.prod.ote_config_uri}getEnv(){return this.settings.env||"prod"}getSignInUrl(){return n[this.getEnv()]?.signInUrl}getFloodgateUrl(){return n[this.getEnv()]?.floodgateUrl}getAcroIpmURL(){return n[this.getEnv()]?.acrobat_ipm_uri}getSidepanelUrl(){return n[this.getEnv()]?.sidepanelUrl}getViewerIMSClientId(){return this.settings.viewer_ims_client_id?this.settings.viewer_ims_client_id:n.prod.viewer_ims_client_id}getViewerIMSClientIdSocial(){return this.settings.viewer_ims_client_id_social?this.settings.viewer_ims_client_id_social:n.prod.viewer_ims_client_id_social}getImsContextId(){return this.settings.ims_context_id?this.settings.ims_context_id:n.prod.ims_context_id}getIMSurl(){return this.settings.imsURL?this.settings.imsURL:n.prod.imsURL}getImsLibUrl(){return this.settings.imsLibUrl?this.settings.imsLibUrl:n.prod.imsLibUrl}getDcApiUri(){return this.settings.dcApiUri?this.settings.dcApiUri:n.prod.dcApiUri}getGenAIPricingUri(){return this.settings.genAIPricingUri?this.settings.genAIPricingUri:n.prod.genAIPricingUri}getExpressURLs(){return n[this.getEnv()]?.express||n.prod.express}getFloodgateuri(){return this.settings.floodgateUri?this.settings.floodgateUri:n.prod.floodgateUri}getWelcomePdfUrlHost(){return this.settings.welcomePdfUrlHost?this.settings.welcomePdfUrlHost:n.prod.welcomePdfUrlHost}getLoggingUri(){return"development"===r.getItem("installSource")?"https://dc-api-stage.adobe.io":this.settings.loggingUri?this.settings.loggingUri:n.prod.loggingUri}isAdobeInternalUser(){return"true"===r.getItem("adobeInternal")}createAnonUserUUID(){const t=`${this.getEnv()}_${this.getViewerIMSClientId()}_${e.uuid()}`;try{r.setItem("anonUserUUID",t)}catch(e){}return t}GET_headers(){return this.$GET_headers["x-request-id"]=this.uuid(),this.$GET_headers.Authorization=this.settings.auth_token,this.$GET_headers}POST_headers(){return this.$POST_headers["x-request-id"]=this.uuid(),this.$POST_headers.Authorization=this.settings.auth_token,this.$POST_headers}noToken(e){e&&e.reject()}filesBaseUris(){var t=e.Deferred();return this.settings.files_api?t.resolve():e.ajax({url:this.settings.files_host+"api/base_uris",headers:{Accept:this.GET_headers().Accept,"x-api-client-id":this.GET_headers()["x-api-client-id"]}}).then(this.proxy((function(e){this.settings.files_api=e.api,this.settings.files_upload=e.upload,t.resolve()})),(function(){t.reject()})),t.promise()}cloudBaseUris(){var t=e.Deferred();return this.settings.cloud_api?t.resolve():e.ajax({url:this.settings.cloud_host+"api/base_uris",headers:{Accept:this.GET_headers().Accept,"x-api-client-id":this.GET_headers()["x-api-client-id"]}}).then(this.proxy((function(e){this.settings.cloud_api=e.api,this.settings.files_host=e.files,this.settings.fillsign_api=e.fss,this.settings.ims_host=e.ims,this.settings.cpdf_api=e.cpdf,t.resolve()})),(function(){t.reject()})),t.promise()}baseUris(){var t=e.Deferred();return this.cloudBaseUris().done(this.proxy((function(){this.filesBaseUris().done(this.proxy((function(){t.resolve()})))}))),t.promise()}connect(){var t=e.Deferred();return this.settings.auth_code?(this.baseUris().then(this.proxy((function(){if(this.settings.auth_code){var s={grant_type:"authorization_code",code:this.settings.auth_code,client_id:this.settings.ims_client_id,client_secret:this.settings.ims_client_secret};e.ajax({url:this.settings.ims_host+"ims/token/v1",type:"POST",data:s,contentType:"application/x-www-form-urlencoded;charset=UTF-8"}).then(this.proxy((function(s){var r=(new Date).getTime(),o=/@adobe(test)?\.com$/i.test(s.email);this.settings.auth_code=null,o?(this.settings.auth_token="Bearer "+s.access_token,this.settings.refresh_token=s.refresh_token,this.settings.token_expiry=r+s.expires_in,this.settings.refresh_time=r+s.expires_in/2,this.settings.displayName=s.displayName,t.resolve(),e.consoleLog("got auth token")):(alert("PDF Helper Extension is available to Adobe Employees only"),i.event(i.e.EXTENSION_FORCE_UNINSTALL),chrome.management.uninstallSelf())})),this.proxy((function(){this.noToken(t)})))}else this.noToken(t)})),this.proxy((function(){this.noToken(t)}))),t.promise()):(t.reject(),t.promise())}refreshToken(t){var i,s=(new Date).getTime(),r=e.Deferred();return this.settings.refresh_token?!t&&s<this.settings.refresh_time?r.resolve():s>this.settings.token_expiry?r.reject():(i={grant_type:"refresh_token",refresh_token:this.settings.refresh_token,client_id:this.settings.ims_client_id,client_secret:this.settings.ims_client_secret},e.ajax({url:this.settings.ims_host+"ims/token/v1",type:"POST",data:i,contentType:"application/x-www-form-urlencoded; charset=UTF-8"}).then(this.proxy((function(t){var i=(new Date).getTime();this.settings.auth_token="Bearer "+t.access_token,this.settings.refresh_token=t.refresh_token,this.settings.token_expiry=i+t.expires_in,this.settings.refresh_time=i+t.expires_in/2,r.resolve(),e.consoleLog("refresh token result"),e.consoleLogDir(t)})),this.proxy((function(){this.noToken(r)})))):r.reject(),r.promise()}ajaxReady(t){let i=e.Deferred();return this.settings.auth_token?this.refreshToken(t).then(this.proxy((function(){i.resolve()})),this.proxy((function(){this.noToken(i)}))):this.connect().then(this.proxy((function(){this.settings.files_root?i.resolve():e.ajax({url:this.settings.files_api+"root",type:"GET",headers:this.GET_headers()}).then(this.proxy((function(e){i.resolve(),this.settings.files_root=e.id})),this.proxy((function(){this.noToken(i)})))})),this.proxy((function(){this.noToken(i)}))),i.promise()}sso_url(t){return e.ajax({url:this.settings.cloud_api+"session/sso_uri?path="+t,type:"GET",headers:this.GET_headers()})}authorize(e){return this.settings.auth_code=e,this.settings.auth_token=null,delete this.settings.refresh_token,delete this.settings.token_expiry,delete this.settings.refresh_time,this.ajaxReady()}clearAuth(){this.settings.auth_token&&(e.ajax({url:this.settings.cloud_api+"session",type:"DELETE",headers:this.$POST_headers}),delete this.settings.auth_token,delete this.settings.refresh_token,delete this.settings.token_expiry,delete this.settings.refresh_time,delete this.settings.displayName)}LOG(t,i){return e.Deferred().resolve()}}o||(o=new a,e.ajaxError((function(e,t,i,s){401===t.status&&o.clearAuth()})));export const common=o;