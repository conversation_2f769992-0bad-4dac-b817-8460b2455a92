#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理程序中的无用文件
删除重复、过时、临时和测试文件
"""

import os
import shutil
import time
from pathlib import Path
from typing import List, Dict, Set

def get_files_to_cleanup() -> Dict[str, List[str]]:
    """获取需要清理的文件列表"""
    return {
        # 1. 重复的测试文件
        "duplicate_test_files": [
            "debug_douyin_xpath.py",
            "quick_test_douyin.py", 
            "quick_test_douyin_fixed.py",
            "test_description_fix.py",
            "test_douyin_login_persistence.py",
            "test_douyin_optimized.py",
            "test_douyin_publish.py",
            "test_douyin_upload_fix.py",
            "test_enhanced_features.py",
            "test_optimized_douyin_publish.py"
        ],
        
        # 2. 过时的文档文件
        "obsolete_docs": [
            "DOUYIN_FIREFOX_SOLUTION.md",
            "DOUYIN_PUBLISH_FIX.md", 
            "DOUYIN_USAGE_GUIDE.md",
            "FIREFOX_DOUYIN_GUIDE.md",
            "OBSOLETE_CLEANUP_REPORT.md",
            "ONE_CLICK_PUBLISH_STATUS_REPORT.md"
        ],
        
        # 3. 重复的演示文件
        "duplicate_demo_files": [
            "demo_enhanced_ai_optimizer.py",
            "demo_enhanced_video_converter.py",
            "demo_one_click_publish.py"
        ],
        
        # 4. 临时和缓存目录
        "temp_cache_dirs": [
            "selenium",
            "selenium_chrome_data", 
            "cache",
            "temp/image_cache",
            "temp/test_videos",
            "temp/uploaded_images",
            "temp/vheer_videos",
            "temp/video_cache",
            "temp/video_conversion"
        ],
        
        # 5. 过时的脚本文件
        "obsolete_scripts": [
            "scripts/auto_cleanup.py",
            "scripts/check_chrome_compatibility.py",
            "scripts/check_chrome_debug.py",
            "scripts/code_cleanup_analyzer.py",
            "scripts/comprehensive_code_check.py",
            "scripts/critical_bug_checker.py",
            "scripts/dead_code_detector.py",
            "scripts/demo_multi_platform_publish.py",
            "scripts/demo_simulation_publish.py",
            "scripts/diagnose_chrome_debug.py",
            "scripts/diagnose_douyin_publish.py",
            "scripts/download_chromedriver_137.py",
            "scripts/download_matching_chromedriver.py",
            "scripts/fix_chrome_issues.py",
            "scripts/fresh_chrome_setup.py",
            "scripts/patch_selenium_version_check.py",
            "scripts/safe_code_cleanup.py",
            "scripts/start_chrome_debug.bat",
            "scripts/start_chrome_debug.py",
            "scripts/test_douyin_login_debug.py",
            "scripts/test_douyin_login_fix.py",
            "scripts/test_kuaishou_crawler_assist.py",
            "scripts/test_moneyprinter_publish.py",
            "scripts/test_moneyprinter_style_login.py",
            "scripts/test_one_click_publish.py",
            "scripts/test_platform_list.py",
            "scripts/test_publish_basic.py",
            "scripts/test_simple_ui_platforms.py",
            "scripts/test_ui_platforms.py",
            "scripts/update_chromedriver.py"
        ],
        
        # 6. 过时的文档目录内容
        "obsolete_doc_files": [
            "doc/AUTO_LOGIN_DETECTION_GUIDE.md",
            "doc/AUTO_SAVE_MEMORY_OPTIMIZATION_GUIDE.md",
            "doc/BROWSER_CONFIG_PERSISTENCE_FIX.md",
            "doc/BROWSER_DETECTION_UNRESPONSIVE_FIX.md",
            "doc/CLEANUP_REPORT.md",
            "doc/CODE_QUALITY_REPORT.md",
            "doc/CURRENT_PAGE_LOGIN_DETECTION_GUIDE.md",
            "doc/CogVideoX_504_Error_Solution_Guide.md",
            "doc/DEAD_CODE_ANALYSIS_REPORT.md",
            "doc/DOUYIN_LOGIN_FIX.md",
            "doc/DOUYIN_PUBLISH_GUIDE.md",
            "doc/ENHANCED_ONE_CLICK_PUBLISH_SOLUTION.md",
            "doc/FINAL_CLEANUP_SUMMARY.md",
            "doc/IMPLEMENTATION_SUMMARY.md",
            "doc/INTEGRATED_ONE_CLICK_PUBLISH_SOLUTION.md",
            "doc/LOG_CONTROL_GUIDE.md",
            "doc/ONE_CLICK_PUBLISH.md",
            "doc/ONE_CLICK_PUBLISH_CLEANUP_AND_TEST.md",
            "doc/ONE_CLICK_PUBLISH_COMPLETE_REPORT.md",
            "doc/ONE_CLICK_PUBLISH_FINAL_REPORT.md",
            "doc/ONE_CLICK_PUBLISH_IMPLEMENTATION.md",
            "doc/ONE_CLICK_PUBLISH_PLAN.md",
            "doc/OPTIMIZATION_PLAN.md",
            "doc/PLATFORM_UI_OPTIMIZATION_COMPLETE.md",
            "doc/PROJECT_CLEANUP_SUMMARY.md",
            "doc/PROJECT_STATUS.md",
            "doc/SECURE_CHROME_LAUNCH_PARAMS.md",
            "doc/SHOT_TEXT_LENGTH_CONTROL_ANALYSIS.md",
            "doc/UI_OPTIMIZATION_GUIDE.md",
            "doc/UI_OPTIMIZATION_SUMMARY.md",
            "doc/UI_PLATFORM_OPTIMIZATION.md",
            "doc/UNIFIED_DATA_MANAGEMENT_GUIDE.md",
            "doc/VIDEO_PROMPT_OPTIMIZATION_GUIDE.md"
        ],
        
        # 7. 日志和临时文件
        "log_temp_files": [
            "chromedriver.log",
            "startup_error.log",
            "logs/error.log",
            "logs/system.log"
        ],
        
        # 8. 备份文件
        "backup_files": [
            "project_backup_20250717.zip",
            "cleanup_backup_dirs.py",
            "fix_dependencies.py",
            "install_ffmpeg.py",
            "install_vheer_dependencies.py"
        ]
    }

def backup_important_files():
    """备份重要文件"""
    backup_dir = Path("cleanup_backup")
    backup_dir.mkdir(exist_ok=True)
    
    important_files = [
        "main.py",
        "requirements.txt",
        "config/app_settings.json",
        "config/llm_config.json",
        "config/tts_config.json",
        "config/video_generation_config.py"
    ]
    
    print("📦 备份重要文件...")
    for file_path in important_files:
        if os.path.exists(file_path):
            dest = backup_dir / Path(file_path).name
            shutil.copy2(file_path, dest)
            print(f"   ✅ 备份: {file_path}")

def cleanup_files(files_to_cleanup: Dict[str, List[str]], dry_run: bool = True):
    """清理文件"""
    total_deleted = 0
    total_size_saved = 0
    
    for category, file_list in files_to_cleanup.items():
        print(f"\n🗂️ 清理类别: {category}")
        print("-" * 50)
        
        category_deleted = 0
        category_size = 0
        
        for file_path in file_list:
            path = Path(file_path)
            
            try:
                if path.exists():
                    # 计算文件/目录大小
                    if path.is_file():
                        size = path.stat().st_size
                    else:
                        size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                    
                    size_mb = size / (1024 * 1024)
                    
                    if dry_run:
                        print(f"   🔍 将删除: {file_path} ({size_mb:.2f} MB)")
                    else:
                        if path.is_file():
                            path.unlink()
                        else:
                            shutil.rmtree(path)
                        print(f"   ✅ 已删除: {file_path} ({size_mb:.2f} MB)")
                        
                    category_deleted += 1
                    category_size += size
                else:
                    print(f"   ⚠️ 不存在: {file_path}")
                    
            except Exception as e:
                print(f"   ❌ 删除失败: {file_path} - {e}")
        
        total_deleted += category_deleted
        total_size_saved += category_size
        
        print(f"   📊 {category}: {category_deleted} 个文件/目录, {category_size/(1024*1024):.2f} MB")
    
    print(f"\n📊 清理总结:")
    print(f"   - 总计: {total_deleted} 个文件/目录")
    print(f"   - 节省空间: {total_size_saved/(1024*1024):.2f} MB")
    
    return total_deleted, total_size_saved

def cleanup_empty_directories():
    """清理空目录"""
    print("\n🗂️ 清理空目录...")
    
    empty_dirs = []
    for root, dirs, files in os.walk(".", topdown=False):
        for dir_name in dirs:
            dir_path = Path(root) / dir_name
            try:
                if dir_path.is_dir() and not any(dir_path.iterdir()):
                    empty_dirs.append(dir_path)
            except:
                continue
    
    for empty_dir in empty_dirs:
        try:
            empty_dir.rmdir()
            print(f"   ✅ 删除空目录: {empty_dir}")
        except Exception as e:
            print(f"   ❌ 删除空目录失败: {empty_dir} - {e}")
    
    return len(empty_dirs)

def create_cleanup_report(deleted_count: int, size_saved: int, empty_dirs: int):
    """创建清理报告"""
    report_content = f"""# 项目清理报告

## 清理时间
{time.strftime('%Y-%m-%d %H:%M:%S')}

## 清理统计
- 删除文件/目录: {deleted_count} 个
- 节省磁盘空间: {size_saved/(1024*1024):.2f} MB
- 删除空目录: {empty_dirs} 个

## 清理类别
1. 重复的测试文件
2. 过时的文档文件
3. 重复的演示文件
4. 临时和缓存目录
5. 过时的脚本文件
6. 过时的文档目录内容
7. 日志和临时文件
8. 备份文件

## 保留的重要文件
- 主程序文件 (main.py)
- 配置文件 (config/)
- 源代码 (src/)
- 需求文件 (requirements.txt)
- 项目文档 (.kiro/specs/)

## 建议
- 定期运行此清理脚本
- 保持项目结构整洁
- 及时删除临时文件
"""
    
    with open("CLEANUP_REPORT.md", "w", encoding="utf-8") as f:
        f.write(report_content)
    
    print(f"📄 清理报告已保存: CLEANUP_REPORT.md")

def main():
    """主函数"""
    print("🧹 项目文件清理工具")
    print("=" * 50)
    
    # 获取要清理的文件
    files_to_cleanup = get_files_to_cleanup()
    
    # 显示清理预览
    print("📋 清理预览:")
    total_files = sum(len(files) for files in files_to_cleanup.values())
    print(f"   - 将清理 {len(files_to_cleanup)} 个类别")
    print(f"   - 涉及约 {total_files} 个文件/目录")
    
    # 询问用户确认
    print("\n⚠️ 这将永久删除文件，请确认:")
    print("   1. 预览模式 (不实际删除)")
    print("   2. 执行清理 (实际删除)")
    print("   3. 取消")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        print("\n🔍 预览模式 - 不会实际删除文件")
        backup_important_files()
        deleted_count, size_saved = cleanup_files(files_to_cleanup, dry_run=True)
        empty_dirs = 0
        
    elif choice == "2":
        print("\n⚠️ 执行清理模式 - 将实际删除文件")
        confirm = input("确认执行清理？(yes/no): ").strip().lower()
        
        if confirm == "yes":
            backup_important_files()
            deleted_count, size_saved = cleanup_files(files_to_cleanup, dry_run=False)
            empty_dirs = cleanup_empty_directories()
            create_cleanup_report(deleted_count, size_saved, empty_dirs)
            
            print("\n🎉 清理完成！")
            print("💡 建议重启程序以确保所有更改生效")
        else:
            print("❌ 清理已取消")
            return
            
    else:
        print("❌ 清理已取消")
        return
    
    print(f"\n📊 最终统计:")
    print(f"   - 处理文件/目录: {deleted_count} 个")
    print(f"   - 节省空间: {size_saved/(1024*1024):.2f} MB")
    if choice == "2":
        print(f"   - 删除空目录: {empty_dirs} 个")

if __name__ == "__main__":
    main()
