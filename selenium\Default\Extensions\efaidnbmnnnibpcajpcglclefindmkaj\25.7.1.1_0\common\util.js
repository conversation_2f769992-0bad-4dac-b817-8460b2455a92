/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{common as e}from"../sw_modules/common.js";import{util as t}from"../sw_modules/util.js";import{dcLocalStorage as n}from"./local-storage.js";import{ADOBE_URL as a,demoFileURL as r,ONBOARDING_DEMO as o}from"./constant.js";import{getActiveExperimentAnalyticsArray as s}from"./experimentUtils.js";import{EXPERIMENT_VARIANTS_STORAGE_KEY as i}from"../sw_modules/constant.js";export function isNewUser(){if(!n.getItem("extUserState")){const e=n.getItem("viewerStorage");if(e){return+(0===(parseInt(e.usage)||0))}return-1}return 0}export async function updateExtUserState(){if(!n.getItem("extUserState")){n.setItem("extUserState","ru");const e=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});chrome.runtime.sendMessage({main_op:"reRegisterUninstallUrl",tab:e[0]})}}const c={MACHINE:"machine"};export function setCookie(e,t,n={}){const a={name:e,value:t,secure:!0,sameSite:"no_restriction"};n.url&&(a.url=n.url),n.path&&(a.path=n.path),n.expiryDate&&(a.expirationDate=n.expiryDate),n.sameSite&&(a.sameSite=n.sameSite),n.secure&&(a.secure=n.secure),n.domain&&(a.domain=n.domain),chrome.cookies.set(a)}export function setDVExperimentCodeForAnalytics(e){if("gdrive"===e){const e=s(),t=function(){let e="";"true"===n.getItem("gdrive-pdf-dv-feature-enablement-status")?e+="T":"true"===n.getItem("gdrive-pdf-dv-feature-control-enablement-status")&&(e+="C");e&&(e="GD"+e+("true"===n.getItem("gdrive-pdf-default-viewership")?"T":"F"));return e}();if(!e.includes(t)){const a=e.filter((e=>!["GDTT","GDTF","GDCF"].includes(e)));a.push(t),n.setItem(i,a)}}}export function registerUninstallUrl({locale:a}={}){const r=n.getItem("appLocale"),o=n.getItem("installSource");if(["development","normal","sideload"].includes(o)){const o=new URL(e.getUninstallUrl());if(r)o.searchParams.append("locale",r);else if(a)o.searchParams.append("locale",a);else{let e=n.getItem("viewer-locale")||t.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale"));o.searchParams.append("locale",e)}o.searchParams.append("theme",n.getItem("theme")||"auto"),o.searchParams.append("callingApp",chrome.runtime.id),o.searchParams.append("nu",isNewUser());const i=s();i.length>0&&o.searchParams.append("acEx",JSON.stringify(i.sort())),chrome.runtime.setUninstallURL(o.href)}}async function m(e,t=void 0){const n=new Date,r=n.getMonth(),o=n.getFullYear();let s="mmac";e===c.MACHINE&&(s+="_machine"),t&&(s+=`_${t}`);let i=null;try{i=await chrome.cookies.get({url:a,name:s});const e=!i||function(e,t,n){const a=new Date(e),r=a.getMonth(),o=a.getFullYear();return n>o||n===o&&t>r}(i.value,r,o);return!e}catch(e){return!1}}function p(t){const n=e.getAcroIpmURL(),a=new URL(n);return t.pingType===c.MACHINE&&(a.pathname+="/machine"),t.appPath?a.pathname+=`/${encodeURIComponent(t.appPath)}`:a.pathname+="/overall",Object.keys(t.schema).forEach((e=>{const n=t.schema[e];a.pathname+=n?`/${encodeURIComponent(n)}`:"/unspecified"})),a.pathname+="/mmac.html",a.toString()}async function l(e,t,n=void 0){try{const r=await fetch(e);if(200===r?.status){let e="mmac";t===c.MACHINE&&(e+="_machine"),n&&(e+=`_${n}`);const r=new Date,o=Math.floor(Date.now()/1e3)+2678400;setCookie(e,r.toISOString(),{url:a,expiryDate:o,path:"/",domain:".adobe.com"})}}catch(e){console.error(e)}}async function u(e){if(async function(e){e&&!await m(c.MACHINE)&&l(p({...e,appPath:void 0,pingType:c.MACHINE}),c.MACHINE)}(e),function(e){return!!e&&!(!e.appPath||"string"!=typeof e.appPath||""===e.appPath.trim())}(e)&&!await m(c.MACHINE,e.appPath)){l(p({...e,pingType:c.MACHINE}),c.MACHINE,e.appPath)}}export function sendPingEventHandler(){let e="chrome-extension";t.isEdge()&&(e="edge-extension");const a=n.getItem("appLocale");let r=n.getItem("viewer-locale");r||(r=n.getItem("locale")),u({appPath:e,schema:{appIdentifier:"dc-acrobat-extension",appVersion:chrome.runtime.getManifest().version,appReferrer:n.getItem("installSource"),userType:"unknown",subscriptionType:"unknown",locale:a||r}})}export const isEdgeBrowser=()=>t.isEdge();export const isChromeViewerOpened=e=>{if(!e)return!1;const t=`chrome-extension://${chrome.runtime.id}`;return e.startsWith(`${t}/http`)||e.startsWith(`${t}/https`)||e.startsWith(`${t}/viewer.html`)||e.startsWith(`${t}/file`)};export const isDemoMode=(e,t)=>{if(!e||!t)return!1;const a=new URL(t),s=a.origin+a.pathname,i=n.getItem("od"),c="en"===(n.getItem("appLocale")||n.getItem("viewer-locale")||n.getItem("locale")).toLowerCase().split("-")[0],{name:m,source:p}=((e="")=>{try{if(!e)return{};const t=e.length%4;return JSON.parse(decodeURIComponent(atob(e.replace(/-/g,"+").replace(/_/g,"/").padEnd(e.length+(0===t?0:4-t),"="))))}catch(e){log("decodeBlobUrl failed",e)}})(e);return!(m!==o||"cdn"!==p||s!==r||!i||!c)};export const getGenAIServiceVariant=()=>{const t=n.getItem("cluster");return t||(e.isAdobeInternalUser()?"beta":"release")};