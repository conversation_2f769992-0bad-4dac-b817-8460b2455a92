#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键发布功能演示脚本
展示核心功能的使用方法
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.services.simple_publisher_service import SimplePublisherService
from src.services.platform_publisher.base_publisher import VideoMetadata
from src.utils.logger import logger

async def demo_publisher_service():
    """演示发布服务功能"""
    print("🚀 一键发布功能演示")
    print("=" * 50)
    
    try:
        # 1. 初始化发布服务
        print("📦 初始化发布服务...")
        publisher = SimplePublisherService()
        
        # 2. 获取支持的平台
        platforms = publisher.get_supported_platforms()
        print(f"✅ 支持 {len(platforms)} 个平台:")
        
        # 显示主要平台
        main_platforms = ['douyin', 'bilibili', 'kuaishou', 'xiaohongshu', 'wechat', 'youtube']
        platform_names = {
            'douyin': '🎵 抖音',
            'bilibili': '📺 B站', 
            'kuaishou': '⚡ 快手',
            'xiaohongshu': '📖 小红书',
            'wechat': '💬 微信视频号',
            'youtube': '🎬 YouTube'
        }
        
        for platform in main_platforms:
            if platform in platforms:
                name = platform_names.get(platform, platform)
                print(f"   {name}")
        
        # 3. 创建测试视频元数据
        print("\n📝 创建视频元数据...")
        metadata = VideoMetadata(
            title="AI生成的精彩视频 - 演示版",
            description="这是一个由AI生成的精彩视频内容，展示了最新的技术成果。\n\n#AI #科技 #创新 #视频生成",
            tags=["AI", "科技", "创新", "视频生成", "自动化"],
            category="科技",
            privacy="public"
        )
        
        print(f"   标题: {metadata.title}")
        print(f"   描述长度: {len(metadata.description)} 字符")
        print(f"   标签: {', '.join(metadata.tags)}")
        print(f"   分类: {metadata.category}")
        
        # 4. 模拟发布流程（不实际发布）
        print("\n🎯 模拟发布流程...")
        target_platforms = ['bilibili', 'douyin']
        
        print(f"   目标平台: {', '.join(target_platforms)}")
        print("   模式: 演示模式（不实际发布）")
        
        # 模拟进度回调
        def progress_callback(progress, message):
            bar_length = 30
            filled_length = int(bar_length * progress)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f"   [{bar}] {progress*100:.1f}% - {message}")
        
        # 模拟发布过程
        print("\n📤 开始模拟发布...")
        progress_callback(0.0, "初始化发布任务")
        await asyncio.sleep(0.1)
        
        progress_callback(0.2, "验证视频文件")
        await asyncio.sleep(0.1)
        
        progress_callback(0.4, "检查平台认证")
        await asyncio.sleep(0.1)
        
        progress_callback(0.6, "准备发布内容")
        await asyncio.sleep(0.1)
        
        progress_callback(0.8, "提交到目标平台")
        await asyncio.sleep(0.1)
        
        progress_callback(1.0, "发布完成")
        
        # 5. 显示模拟结果
        print("\n📊 模拟发布结果:")
        mock_results = {
            'bilibili': {
                'success': True,
                'video_id': 'BV1234567890',
                'video_url': 'https://bilibili.com/video/BV1234567890',
                'message': '发布成功'
            },
            'douyin': {
                'success': True,
                'video_id': 'DY9876543210',
                'video_url': 'https://douyin.com/video/9876543210',
                'message': '发布成功，等待审核'
            }
        }
        
        for platform, result in mock_results.items():
            status = "✅" if result['success'] else "❌"
            platform_name = platform_names.get(platform, platform)
            print(f"   {status} {platform_name}: {result['message']}")
            if result.get('video_id'):
                print(f"      视频ID: {result['video_id']}")
        
        # 6. 显示统计信息
        print("\n📈 功能统计:")
        stats = publisher.get_statistics()
        print(f"   总任务数: {stats.get('total_tasks', 0)}")
        print(f"   总发布记录: {stats.get('total_records', 0)}")
        
        print("\n🎉 演示完成！一键发布功能运行正常")
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_platform_factory():
    """演示平台工厂功能"""
    print("\n🏭 平台工厂演示")
    print("-" * 30)
    
    try:
        from src.services.platform_publisher.publisher_factory import PublisherFactory
        
        # 获取支持的平台
        platforms = PublisherFactory.get_supported_platforms()
        print(f"📋 工厂支持 {len(platforms)} 个平台")
        
        # 测试创建发布器
        test_platforms = ['douyin', 'bilibili', 'kuaishou']
        print(f"\n🧪 测试创建发布器:")
        
        for platform in test_platforms:
            try:
                publisher = PublisherFactory.create_publisher(platform, {'simulation_mode': True})
                if publisher:
                    print(f"   ✅ {platform}: 创建成功")
                else:
                    print(f"   ❌ {platform}: 创建失败")
            except Exception as e:
                print(f"   ❌ {platform}: 创建异常 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 平台工厂演示失败: {e}")
        return False

async def main():
    """主演示函数"""
    print("🎬 一键发布功能完整演示")
    print("=" * 60)
    
    # 演示发布服务
    service_ok = await demo_publisher_service()
    
    # 演示平台工厂
    factory_ok = demo_platform_factory()
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 演示总结:")
    print(f"   发布服务演示: {'✅ 成功' if service_ok else '❌ 失败'}")
    print(f"   平台工厂演示: {'✅ 成功' if factory_ok else '❌ 失败'}")
    
    if service_ok and factory_ok:
        print("\n🎉 所有演示成功！一键发布功能完全正常")
        print("\n💡 使用提示:")
        print("   1. 运行 python main.py 启动主程序")
        print("   2. 选择'增强版一键发布'标签页")
        print("   3. 按照界面提示设置浏览器和登录平台")
        print("   4. 选择视频文件并配置发布参数")
        print("   5. 点击'开始发布'即可发布到多个平台")
        return True
    else:
        print("\n❌ 部分演示失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)