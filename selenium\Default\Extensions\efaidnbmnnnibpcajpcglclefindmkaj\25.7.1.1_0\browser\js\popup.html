<!DOCTYPE html>
<!--
/*************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2020 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and may be covered by U.S. and Foreign Patents,
* patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="../css/new-popup.css" type="text/css" rel="stylesheet"/>
    <script src="../../libs/jquery-3.1.1.min.js"></script>
    <script type="module" src="popup.js"></script>
</head>
<body>
    <iframe id="cdn-iframe" loading="eager" class="iframe loader"></iframe>

    <div id="offline-mode" class="offline-mode hidden">
        <div class="offline-mode-header">
            <div id="branding" class="header-pane">
                <div class="header-icon"></div>
                <div class="header-text" id="web2pdfTitle">Adobe Acrobat</div>
            </div>
            <div class="header-pane">
                <div class="settings">
                    <input type="button" class="settings-icon" />
                    <span id="web2pdfOptionsTitle"></span>
                </div>
            </div>
        </div>

        <div class="offline-mode-body">
            <div class="offline-mode-icon"></div>
            <h2 id="offlineModeTitle" class="offline-mode-title"></h2>
            <div id="offlineModeMessage" class="offline-mode-message"></div>
        </div>

        <div id="footer" class="offline-mode-footer hidden">
            <label class="toggle-container">
                <input id="toggle-input" type="checkbox" />
                <span class="slider"></span>
            </label>
            <label class="footer-text" id="pdfOwnershipExploreAcrobat" for="always-show"></label>
        </div>
    </div>
</body>
</html>
