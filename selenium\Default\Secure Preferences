{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "efaidnbmnnnibpcajpcglclefindmkaj": {"account_extension_type": 0, "ack_prompt_count": 1, "active_permissions": {"api": ["alarms", "contextMenus", "cookies", "downloads", "nativeMessaging", "storage", "tabs", "webNavigation", "webRequest", "scripting", "offscreen", "sidePanel"], "explicit_host": ["<all_urls>"], "manifest_permissions": [], "scriptable_host": ["<all_urls>", "file:///*", "http://*/*", "https://*/*", "https://acrobat.adobe.com/*", "https://documentcloud.adobe.com/*", "https://drive.google.com/*", "https://drive.usercontent.google.com/download*", "https://mail.google.com/*", "https://outlook.live.com/*", "https://outlook.office.com/*", "https://outlook.office365.com/*", "https://web.whatsapp.com/*", "https://www.google.com/*"]}, "commands": {}, "content_settings": [], "creation_flags": 9, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": true, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 6, "manifest": {"action": {"default_icon": "browser/images/acrobat_dc_appicon_24.png", "default_popup": "browser/js/popup.html", "default_title": "Acrobat 扩展程序 - 所有 PDF 工具的快捷方式"}, "background": {"service_worker": "service-worker.js", "type": "module"}, "content_scripts": [{"js": ["libs/jquery-3.1.1.min.js", "browser/js/ch-settings.js", "content_scripts/content-util.js", "content_scripts/SidePanel/GenAIWebpageBlocklist.js", "content_scripts/SidePanel/ActionableCoachmark.js", "content_scripts/embeddedpdfs/EmbeddedPDFTouchPointCoachMark.js", "content_scripts/express/express_whatsapp/whatsapp-express-fte.js", "content_scripts/outlook/outlook-fte.js", "content_scripts/SidePanel/FABManager.js", "content_scripts/SidePanel/GenAIWebpageEligibilityService.js", "content_scripts/SidePanel/AttributionManager.js", "content_scripts/SidePanel/AnimateCannedQuestions.js", "content_scripts/content-script-utils.js", "content_scripts/ch-content-script.js", "content_scripts/injectBannerIframe.js", "content_scripts/prompts/ShowOneChild.js"], "matches": ["file://*/*", "http://*/*", "https://*/*"], "run_at": "document_start"}, {"css": ["browser/css/outlook-content-script.css", "browser/css/gsuite-fte.css"], "js": ["content_scripts/outlook/outlook-content-script.js"], "matches": ["https://outlook.office365.com/*", "https://outlook.office.com/*", "https://outlook.live.com/*"], "run_at": "document_end"}, {"css": ["browser/css/express-tooltip.css"], "js": ["content_scripts/content-script-idle.js"], "matches": ["http://*/*", "https://*/*"], "run_at": "document_idle"}, {"js": ["content_scripts/ch-content-script-dend.js", "content_scripts/injectAIMarker.js"], "matches": ["file://*/*", "http://*/*", "https://*/*"], "run_at": "document_end"}, {"all_frames": true, "css": ["browser/css/embedded-pdf-touch-point.css", "browser/css/gsuite-fte.css"], "js": ["content_scripts/embeddedpdfs/embedded-pdf-touch-point.js"], "matches": ["<all_urls>"], "run_at": "document_end"}, {"css": ["browser/css/gdrive-content-script.css", "browser/css/gdrive-touchpoint-service.css"], "js": ["content_scripts/gdrive-content-script.js"], "matches": ["https://drive.google.com/*"], "run_at": "document_start"}, {"js": ["content_scripts/express/express_whatsapp/whatsapp-content-script.js"], "matches": ["https://web.whatsapp.com/*"], "run_at": "document_idle"}, {"js": ["content_scripts/express/google-image-preview/google-image-preview-content-script.js"], "matches": ["https://www.google.com/*"], "run_at": "document_idle"}, {"js": ["content_scripts/gdrive-download-page-content-script.js"], "matches": ["https://drive.usercontent.google.com/download*"], "run_at": "document_end"}, {"css": ["browser/css/gmail-content-script.css"], "js": ["content_scripts/gmail-content-script.js"], "matches": ["https://mail.google.com/*"], "run_at": "document_start"}, {"js": ["content_scripts/acrobat-content-script.js"], "matches": ["https://acrobat.adobe.com/*", "https://documentcloud.adobe.com/*"], "run_at": "document_end"}], "content_security_policy": {"extension_pages": "script-src 'self'; child-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline'; frame-src https://use.typekit.net https://assets.adobedtm.com https://*.adobecontent.io https://*.adobelogin.com https://local-test.acrobat.com:* https://local-test.acrobat.adobe.com:* https://*.acrobat.com https://*.adobe.com https://*.adobe.io"}, "current_locale": "zh_CN", "default_locale": "en", "description": "利用 Adobe Acrobat PDF 工具在 Google Chrome 中完成更多任务：查看、填充、注释、签名，您还可以试试转换和压缩工具。", "externally_connectable": {"ids": ["bngnhmnppadfcmpggglniifohlkmddfc"], "matches": ["https://*.adobe.com/*", "https://*.acrobat.com/*", "https://adobe.com/*", "https://www.adobe.com/*"]}, "host_permissions": ["<all_urls>"], "icons": {"128": "browser/images/acrobat_dc_appicon_128.png", "16": "browser/images/acrobat_dc_appicon_16.png", "48": "browser/images/acrobat_dc_appicon_48.png"}, "incognito": "not_allowed", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZBtDwJUnqCebbFbTP819W+x0Tk9Com3yrxaCi/D0t/1FPWBuOvyyiQtFr/FmloZu/2ml8LFYNGNE6gQSSIgGXZceAFCRxzj+EXSI37kNIPISrdKznCTdkA6oSKUpFEVOcoD959DQFqeNbliB9MV+ZrZST+DOVxApdMSJzsCczIwIDAQAB", "manifest_version": 3, "minimum_chrome_version": "107.0", "name": "Adobe Acrobat：PDF 编辑、转化、签名工具", "optional_permissions": ["history", "bookmarks"], "options_page": "browser/js/options.html", "permissions": ["contextMenus", "tabs", "downloads", "nativeMessaging", "webRequest", "webNavigation", "storage", "scripting", "alarms", "offscreen", "cookies", "sidePanel"], "storage": {"managed_schema": "schema.json"}, "update_url": "https://clients2.google.com/service/update2/crx", "version": "********", "web_accessible_resources": [{"matches": ["<all_urls>"], "resources": ["viewer.html", "signInHandler.html", "signInAdobeYolo.html", "browser/js/check-cookies.html", "browser/js/express.html", "browser/js/frame.html", "browser/js/frameUI.html", "browser/js/local-fte.html", "browser/js/local-file/local-file-blocking-page.html", "browser/js/local-file/local-file-prompt.html", "browser/js/lsCopy.html", "browser/js/gdrive-inject.js", "browser/js/viewer/localFileSignInRedirection.html", "browser/js/viewer/localFileSignInRedirection.html", "browser/js/download-banner.html", "browser/js/successToast.html", "browser/js/failToast.html", "browser/js/assistantPopup.html", "browser/images/SDC_GenAIGradientTrefoil_24_N.svg", "browser/css/fonts/AdobeClean-Regular.otf", "browser/css/fonts/AdobeClean-Bold.otf", "browser/images/dc_trefoil_red_32_n.svg", "browser/images/acrobat_dc_appicon_128.png", "browser/images/acrobat_dc_trefoil_24_white.svg", "browser/images/cross_10_n.svg", "browser/images/ExpressRemoveBackground.png", "browser/images/ExpressApplyEffects.png", "browser/images/ExpressCropImage.png", "browser/images/SDC_ShowMenu_18_N_24_D.svg", "browser/images/SDC_ShowMenu_18_N_24_D_ActiveState.svg", "content_scripts/express/express-fte.js", "content_scripts/express/express-fte-utils.js", "content_scripts/gsuite/fte-utils.js", "resources/SidePanel/sidePanelUpsellSuccess.html", "resources/SidePanel/sidePanelButton.html", "resources/SidePanel/index.html", "resources/SidePanel/ActionableCoachmark.html", "resources/SidePanel/FABViewSettings.html", "resources/SidePanel/signInSuccess.html", "resources/SidePanel/sidePanelUtil.js", "common/local-storage.js", "libs/*"]}, {"matches": ["https://mail.google.com/*"], "resources": ["content_scripts/gmail/gmail-inject.js", "content_scripts/gmail/state.js", "content_scripts/gmail/util.js", "content_scripts/gsuite/util.js", "content_scripts/gmail/gmail-response-service.js", "content_scripts/gmail/native-viewer-touch-point-service.js", "content_scripts/gmail/message-view-touch-point-service.js", "content_scripts/gmail/list-view-touch-point-service.js", "content_scripts/gmail/default-viewership-service.js", "content_scripts/express/gmail/express-gmail-touchpoint-service.js", "content_scripts/express/gmail/express-gmail-message-view-touchpoint-service.js"]}, {"matches": ["https://drive.google.com/*"], "resources": ["content_scripts/gdrive/gdrive-inject.js", "content_scripts/gdrive/get-auth-user.js", "content_scripts/gdrive/state.js", "content_scripts/gdrive/util.js", "content_scripts/gsuite/util.js", "content_scripts/gdrive/touchpoint-service.js", "content_scripts/gdrive/search-handler.js", "content_scripts/gdrive/default-viewership-service.js", "content_scripts/gdrive/api-parsing-util.js"]}, {"matches": ["https://web.whatsapp.com/*", "https://mail.google.com/*", "https://www.google.com/*"], "resources": ["browser/images/express_edit.svg", "browser/images/express_remove_background.svg", "browser/images/express_apply_effects.svg", "browser/images/express_crop_image.svg", "browser/images/express_insert_objects.svg", "browser/images/express_remove_objects.svg", "browser/images/acrobat_prodc_appicon_24.svg", "resources/express/expressDropdownMenu.html", "content_scripts/express/dropdown-menu.js", "resources/express/expressSingleClickCTA.html", "content_scripts/express/single-click-cta.js", "content_scripts/express/express-cta-tooltip.js", "resources/express/expressContextualFTE.html", "content_scripts/express/fte/express-contextual-fte.js"]}, {"matches": ["https://outlook.office365.com/*", "https://outlook.office.com/*", "https://outlook.live.com/*"], "resources": ["content_scripts/outlook/outlook-inject.js", "content_scripts/gsuite/util.js", "content_scripts/outlook/state.js", "browser/images/outlook-error-toast-icon.svg", "browser/images/outlook-error-toast-close.svg", "resources/outlook/error-toast.html", "content_scripts/outlook/outlook-error-toast-service.js", "content_scripts/outlook/outlook-fte-service.js"]}]}, "path": "efaidnbmnnnibpcajpcglclefindmkaj\\********_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "ngpampappnmepgilojfohadhhmbhlaek": {"account_extension_type": 0, "ack_prompt_count": 1, "active_permissions": {"api": ["contextMenus", "cookies", "downloads", "downloads.shelf", "management", "nativeMessaging", "proxy", "storage", "tabs", "webNavigation", "webRequest", "webRequestBlocking"], "explicit_host": ["<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["file:///*", "ftp://*/*", "http://*/*", "https://*/*"]}, "commands": {}, "content_settings": [], "creation_flags": 4097, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"author": "Tonec FZE", "background": {"persistent": true, "scripts": ["background.js"]}, "browser_action": {"default_icon": {"16": "images/logo16.png", "32": "images/logo32.png"}}, "content_scripts": [{"all_frames": true, "js": ["content.js"], "matches": ["http://*/*", "https://*/*", "ftp://*/*", "file:///*"], "run_at": "document_start"}], "content_security_policy": "connect-src *; script-src 'self' 'sha256-3A6Y6ygbQdayC7L3d1LSwz60wQiRVT9GBErQTn6TwTo='; style-src 'unsafe-inline'; default-src 'self'", "current_locale": "zh_CN", "default_locale": "en", "description": "Download files with Internet Download Manager", "externally_connectable": {"matches": ["*://*.internetdownloadmanager.com/*", "*://*.tonec.com/*"]}, "homepage_url": "http://www.internetdownloadmanager.com/", "icons": {"128": "images/logo128.png", "16": "images/logo16.png", "48": "images/logo48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAutN8/aVCnWWo01YitMu1kREcX+50UY//YxrPd0XXSLTK0mEJqPRYbcgxjxAfUc1+eHtFbWvR5BDQvip0+3chrMpGfG+Fwn/aQkaZj7T6gEqbHQElqoncCtXaDTRvo0M7Pj0RWjc1dTV+COlhHZI4RIw4TmL1SAvwO/1+VUgg4ohTjFNKhcSsz89kUbaPqfMfFk1UbrI3pkCHTUNCu0+doN7KQDbH1QBlYxt3ajsd15hzhca/dT4eDfew1nHRADOF5A4JNEuDHoyR/ETvn/CZgNeSo2lWNyYp/xpxRR6Fygjhjf4rNoRm/CtdCCF6mPNjbOkvIU8nQJQvvj08QZ3yFQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "47.0", "name": "IDM Integration Module", "offline_enabled": false, "optional_permissions": ["notifications", "system.display"], "permissions": ["<all_urls>", "tabs", "cookies", "contextMenus", "webNavigation", "webRequest", "webRequestBlocking", "downloads", "downloads.shelf", "management", "storage", "proxy", "nativeMessaging"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "6.42.32", "web_accessible_resources": ["captured.html"]}, "path": "ngpampappnmepgilojfohadhhmbhlaek\\6.42.32_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"account_extension_type": 0, "ack_external": true, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 137, "disable_reasons": [], "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "lastpingday": "*****************", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "zh_CN", "default_locale": "en", "description": "Chrome 网上应用店付款系统", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome 网上应用店付款系统", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda\\1.0.0.6_0", "preferences": {}, "regular_only_preferences": {}, "running": false, "was_installed_by_default": true, "was_installed_by_oem": false}}}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "AE186411699E1AE9160898794A3D9CF4CE2C86EE2C1B3EA59F59029C6C9859EF"}, "extensions": {"ui": {"developer_mode": "C211D1971F4029044C1EFA3F142FE896787FB3CC9CEC6BA98257B2559DE81F5A"}}, "homepage": "E0CE003D90D3D3CB2552C1B5089CC298E48ADE0323E16A203CE439CCB6A5087B", "homepage_is_newtabpage": "A0F3AA09B63298E9DACDF6E9D3EC19186E98819C94B1FB665847318F3F7B27A8", "session": {"restore_on_startup": "9B8B1A7349680ED7048E8126586FD5B85E3DB6604AEFD65E08E56012D4547C23", "startup_urls": "AEDF4BDAFF40EF94AA0377F00C3A2C2DE8D712009B75DE61FB087A7393FDD95A"}}, "browser": {"show_home_button": "3D2E1E708D0E05DD63B5E4A33E6584BED85825F0C37E9560D3EA4A974069A5DA"}, "default_search_provider_data": {"template_url_data": "35B8AB17F6981E0F81DA5911E997D3D278DFFB7C89176A54FA47053013093B1A"}, "enterprise_signin": {"policy_recovery_token": "18114D04A3EE98A4081F7C834BDF75AF87DA28AE185A2511EFE4B41687D07CC2"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "A16B2D2D9AE5C946AB27A8A67AC3A1C4808E87CC98EFBE333BD05455663EE0B1", "efaidnbmnnnibpcajpcglclefindmkaj": "419D1D766411576248400E853CFF3C67F71FD6FA9AC7C167CC2A7E23547AE0B6", "fignfifoniblkonapihmkfakmlgkbkcf": "D171EF419CF7D229359FC524A1A24AA81F17810B20B8075E12C1632AABD49CC6", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "E0205333816C55E0F56E9086D72B56AFFC1469C22B48E9DAF0AED813DBF31C25", "ngpampappnmepgilojfohadhhmbhlaek": "19E561DED373E41F49E68C4BD7D7D2D4019CDC5879791623B4CED275D9EA9ADC", "nkeimhogjdpnpccoofpliimaahmaaome": "3862CE495271BBCF3618D22CD66C0B601EBFB213229F71E4EE4236B77C07DD22", "nmmhkkegccagdldgiimedpiccmgmieda": "AB50163BDB07CD7E34FD120E1E1B5220CE6B9127634AC8DFBBD457BD9A684CFD"}, "ui": {"developer_mode": "EE27525AE2AB83DF5197BE85C9C176BC22686A632362870B63180D72A3E92964"}}, "google": {"services": {"account_id": "774CD9F5AC531687C430F8CF65D7D665C249995E7BDCE0FC85BBE8E7C68E5020", "last_signed_in_username": "0AA042D27E524887ABBC6670BBAC6C09C2DB413115324F2CBDB867F3678D6620", "last_username": "757DD82516A2DA46D275563273600C8DE9678DD8DF5FC1265F5FFB744AC34350"}}, "homepage": "A3E98857F9103CF99C7D821479D88BEA18169D331AE3ED667AB2FE6D5C865125", "homepage_is_newtabpage": "2E92577C67C3EDDA5CB9FB4FB1A7867E0C703E745C425E3DA49BE93EEF5AFC29", "media": {"cdm": {"origin_data": "99E33D2C392933689180DCD7B2ED803B69921AB9C26729317EF7907AA2705BAD"}, "storage_id_salt": "E73A2C36416F8056DD5F557CBC0FF82945E680520A3E4673EFA95C7FD46DFEBA"}, "module_blocklist_cache_md5_digest": "4B66E8291A355D269895193026019471893D2CC9650C1E36CE0535CB5C800792", "pinned_tabs": "B8EF4B54A4DB8CF582B65194DCF1F1393874C1B18CEB429DBFCAE30B118488F8", "prefs": {"preference_reset_time": "F65634CFC3AAAF9BF53659857E1D285949AAB0D13680AA0D8D4CA23D95A4248C"}, "safebrowsing": {"incidents_sent": "1623332B952AECB40F1CB30D8E62F607C4F105FD234DCE05F502D123F58EBDE7"}, "search_provider_overrides": "2C09A12C261693ED22B576C548D8538CC5A4774230C629CEB282C7E976002227", "session": {"restore_on_startup": "4F5368C97A1E51500C122864A909D61FD4F379049913F24BFAC688B0656599F1", "startup_urls": "F3F36E97B7503D8987A0FE3678021B8E5FD52CD2D896E97BEBE92CAC93922881"}}, "super_mac": "542EE6E8C51C31EA683D082020E6F5617C4763EB99D5D6469A6666110EB4A105"}}