# -*- coding: utf-8 -*-
"""
AI内容生成器
基于项目内容生成视频标题、描述和标签
"""

import json
import asyncio
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict, field
import logging

from src.services.llm_service_interface import (
    get_llm_service_manager, LLMRequest, LLMResponse
)
from src.services.project_content_analyzer import (
    ProjectContent, get_project_content_analyzer
)
from src.utils.logger import logger


@dataclass
class GeneratedContent:
    """AI生成的内容模型"""
    titles: List[str] = field(default_factory=list)  # 多个标题选项
    description: str = ""
    tags: List[str] = field(default_factory=list)
    platform: str = ""
    confidence_score: float = 0.0
    generation_time: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
    
    def get_best_title(self) -> str:
        """获取最佳标题"""
        if not self.titles:
            return ""
        return self.titles[0]


@dataclass
class PlatformConfig:
    """平台配置模型"""
    name: str
    title_max_length: int
    description_max_length: int
    max_tags: int
    required_fields: List[str]
    content_guidelines: Dict[str, Any]
    language: str = "zh-CN"


class AIContentGenerator:
    """AI内容生成器"""
    
    def __init__(self):
        self.llm_service = get_llm_service_manager()
        self.analyzer = get_project_content_analyzer()
        self.platform_configs = self._load_platform_configs()
        
        logger.info("AI内容生成器初始化完成")
    
    def _load_platform_configs(self) -> Dict[str, PlatformConfig]:
        """加载平台配置"""
        configs = {}
        
        # 抖音平台配置
        configs['douyin'] = PlatformConfig(
            name="抖音",
            title_max_length=60,
            description_max_length=500,
            max_tags=20,
            required_fields=["title", "description"],
            content_guidelines={
                "title_style": "简短吸引人，带有情感或好奇心触发点",
                "description_style": "详细描述内容，包含关键词和话题",
                "tag_style": "热门标签，与内容相关的流行话题"
            }
        )
        
        # B站平台配置
        configs['bilibili'] = PlatformConfig(
            name="B站",
            title_max_length=80,
            description_max_length=2000,
            max_tags=12,
            required_fields=["title", "description"],
            content_guidelines={
                "title_style": "详细准确，可以包含动漫、游戏等二次元文化元素",
                "description_style": "详细的内容介绍，可以包含分P说明",
                "tag_style": "分区相关标签，ACG文化相关"
            }
        )
        
        # 小红书平台配置
        configs['xiaohongshu'] = PlatformConfig(
            name="小红书",
            title_max_length=50,
            description_max_length=1000,
            max_tags=20,
            required_fields=["title", "description", "tags"],
            content_guidelines={
                "title_style": "生活化、个人化，使用第一人称",
                "description_style": "详细的体验分享，包含个人感受",
                "tag_style": "生活方式、美妆、旅行等相关话题"
            }
        )
        
        # 快手平台配置
        configs['kuaishou'] = PlatformConfig(
            name="快手",
            title_max_length=30,
            description_max_length=200,
            max_tags=10,
            required_fields=["title"],
            content_guidelines={
                "title_style": "接地气、简短直接",
                "description_style": "简洁明了，可以包含表情符号",
                "tag_style": "地域性标签，生活相关"
            }
        )
        
        # YouTube平台配置
        configs['youtube'] = PlatformConfig(
            name="YouTube",
            title_max_length=100,
            description_max_length=5000,
            max_tags=500,
            required_fields=["title", "description"],
            content_guidelines={
                "title_style": "英文标题，包含关键词，吸引点击",
                "description_style": "详细的内容介绍，包含时间戳和链接",
                "tag_style": "英文标签，与内容相关的关键词"
            },
            language="en-US"
        )
        
        return configs
    
    async def generate_content(self, 
                              project_context: Optional[ProjectContent] = None, 
                              platform: str = "douyin") -> GeneratedContent:
        """基于项目内容生成平台特定内容"""
        try:
            # 获取项目内容
            if project_context is None:
                project_context = self.analyzer.analyze_current_project()
            
            # 获取平台配置
            platform_config = self.platform_configs.get(
                platform, 
                self.platform_configs['douyin']  # 默认使用抖音配置
            )
            
            # 构建提示词
            prompt = self._build_prompt(project_context, platform)
            
            # 定义输出结构
            schema = {
                "titles": ["标题1", "标题2", "标题3"],
                "description": "视频描述内容",
                "tags": ["标签1", "标签2", "标签3"]
            }
            
            # 调用LLM服务
            import time
            start_time = time.time()
            
            response = await self.llm_service.generate_structured_content(
                prompt=prompt,
                schema=schema,
                max_tokens=1000,
                temperature=0.7
            )
            
            generation_time = time.time() - start_time
            
            # 解析结果
            content = self._parse_llm_response(response, platform)
            content.platform = platform
            content.generation_time = generation_time
            
            # 优化内容
            content = self._optimize_for_platform(content, platform_config)
            
            logger.info(f"为平台 {platform} 生成内容成功")
            return content
            
        except Exception as e:
            logger.error(f"生成内容失败: {e}")
            # 返回空内容
            return GeneratedContent(
                titles=["生成失败，请重试"],
                description="内容生成失败，请重试或手动编辑",
                tags=["自动生成失败"],
                platform=platform
            )
    
    def _build_prompt(self, context: ProjectContent, platform: str) -> str:
        """构建平台特定的提示词"""
        platform_config = self.platform_configs.get(platform, self.platform_configs['douyin'])
        platform_name = platform_config.name
        
        # 基础提示词
        prompt = f"""请基于以下项目内容，为{platform_name}平台生成视频标题、描述和标签。

项目信息:
标题: {context.title or '未知标题'}
摘要: {context.summary or '无摘要'}
类型: {context.content_type or '创作内容'}
"""

        # 添加世界观信息
        if context.world_building:
            prompt += f"\n世界观设定:\n{context.world_building[:300]}...\n"
        
        # 添加角色信息
        if context.characters:
            prompt += f"\n角色: {', '.join(context.characters)}\n"
        
        # 添加主题信息
        if context.themes:
            prompt += f"\n主题: {', '.join(context.themes)}\n"
        
        # 添加关键元素
        if context.key_elements:
            prompt += f"\n关键元素: {', '.join(context.key_elements[:5])}\n"
        
        # 添加平台特定要求
        prompt += f"""
请为{platform_name}平台生成以下内容:

1. 生成3个吸引人的视频标题选项，每个标题不超过{platform_config.title_max_length}字符
2. 生成一段详细的视频描述，不超过{platform_config.description_max_length}字符
3. 生成{min(platform_config.max_tags, 10)}个相关标签

平台内容风格要求:
- 标题风格: {platform_config.content_guidelines.get('title_style', '吸引人的标题')}
- 描述风格: {platform_config.content_guidelines.get('description_style', '详细的描述')}
- 标签风格: {platform_config.content_guidelines.get('tag_style', '相关标签')}

请以JSON格式返回结果，包含titles(标题列表)、description(描述)和tags(标签列表)字段。
"""

        # 如果是YouTube，要求英文内容
        if platform == 'youtube':
            prompt += "\nPlease generate all content in English."
        
        return prompt
    
    def _parse_llm_response(self, response: LLMResponse, platform: str) -> GeneratedContent:
        """解析LLM响应"""
        content = GeneratedContent(platform=platform)
        
        if not response.success:
            logger.warning(f"LLM响应失败: {response.error}")
            return content
        
        try:
            # 尝试解析JSON
            data = json.loads(response.content)
            
            # 提取标题
            if 'titles' in data and isinstance(data['titles'], list):
                content.titles = [title for title in data['titles'] if title]
            
            # 提取描述
            if 'description' in data and isinstance(data['description'], str):
                content.description = data['description']
            
            # 提取标签
            if 'tags' in data and isinstance(data['tags'], list):
                content.tags = [tag for tag in data['tags'] if tag]
            
            # 设置置信度
            content.confidence_score = 0.8
            
        except json.JSONDecodeError:
            logger.warning("LLM响应不是有效的JSON格式，尝试直接解析")
            
            # 尝试直接从文本中提取内容
            text = response.content
            
            # 提取标题
            import re
            title_matches = re.findall(r'标题\d*[:：]?\s*(.+)', text)
            if title_matches:
                content.titles = title_matches[:3]
            
            # 提取描述
            desc_match = re.search(r'描述[:：]?\s*(.+?)(?=标签|$)', text, re.DOTALL)
            if desc_match:
                content.description = desc_match.group(1).strip()
            
            # 提取标签
            tag_matches = re.findall(r'[#＃](\w+)', text)
            if tag_matches:
                content.tags = tag_matches
            else:
                tag_section = re.search(r'标签[:：]?\s*(.+?)(?=$)', text, re.DOTALL)
                if tag_section:
                    tag_text = tag_section.group(1)
                    content.tags = [t.strip() for t in re.split(r'[,，、\s]+', tag_text) if t.strip()]
            
            # 设置较低的置信度
            content.confidence_score = 0.5
        
        return content
    
    def _optimize_for_platform(self, content: GeneratedContent, platform_config: PlatformConfig) -> GeneratedContent:
        """为特定平台优化内容"""
        # 优化标题
        optimized_titles = []
        for title in content.titles:
            # 截断标题
            if len(title) > platform_config.title_max_length:
                title = title[:platform_config.title_max_length - 3] + "..."
            optimized_titles.append(title)
        content.titles = optimized_titles or ["自动生成的标题"]
        
        # 优化描述
        if len(content.description) > platform_config.description_max_length:
            content.description = content.description[:platform_config.description_max_length - 3] + "..."
        
        # 优化标签
        if len(content.tags) > platform_config.max_tags:
            content.tags = content.tags[:platform_config.max_tags]
        
        # 确保所有必填字段都有内容
        if "title" in platform_config.required_fields and not content.titles:
            content.titles = ["自动生成的标题"]
        
        if "description" in platform_config.required_fields and not content.description:
            content.description = "自动生成的描述"
        
        if "tags" in platform_config.required_fields and not content.tags:
            content.tags = ["自动生成"]
        
        return content


# 全局实例
_content_generator = None

def get_ai_content_generator() -> AIContentGenerator:
    """获取AI内容生成器实例"""
    global _content_generator
    if _content_generator is None:
        _content_generator = AIContentGenerator()
    return _content_generator


# 便捷函数
async def generate_video_content(platform: str = "douyin") -> GeneratedContent:
    """便捷的视频内容生成函数"""
    generator = get_ai_content_generator()
    return await generator.generate_content(platform=platform)