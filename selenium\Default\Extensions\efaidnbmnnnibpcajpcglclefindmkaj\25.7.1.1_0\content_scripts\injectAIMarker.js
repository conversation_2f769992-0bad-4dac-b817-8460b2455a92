/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
const isGoogleSearchLink=checkGoogleSearchLink(),isDarkMode=window.matchMedia("(prefers-color-scheme: dark)").matches;let hoverTimeout,showAssistantMarker=!0,isCursorOnPopup=null,hoveredLink=null,currentPopupTimeouts=new Map;function showPopup(e,t,o=!1){const n=`__assistantPopup__${t}`;if(document.getElementById(n))o||(hoverTimeout=setTimeout((()=>showPopup(e,t,!0)),300));else{let o=e.getBoundingClientRect(),s=document.createElement("iframe");s.id=n,s.style.cssText=`\n            border: 0;\n            z-index: 127;\n            position: absolute;\n            margin: auto;\n            background: transparent;\n            color-scheme: auto;\n            left: ${o.right+(scrollX||0)}px;\n            top: ${o.top+(scrollY||0)-8}px;\n            width: 250px;\n            height: 50px;\n            min-height: unset;\n            overflow: hidden;\n        `,s.src=chrome.runtime.getURL("browser/js/assistantPopup.html?pdfMarkerLink="+t+"&theme="+(isDarkMode?"dark":"light")),document.body.appendChild(s),s.addEventListener("mouseenter",(()=>{isCursorOnPopup=t})),s.addEventListener("mouseleave",(()=>{isCursorOnPopup=null,setTimeout((()=>hidePopup(t)),400)}))}}function hidePopup(e,t=!1){if(!t&&(hoveredLink===e||isCursorOnPopup===e))return;const o=`__assistantPopup__${e}`,n=document.getElementById(o);if(n){currentPopupTimeouts.has(e)&&clearTimeout(currentPopupTimeouts.get(e)),n.contentWindow.postMessage({action:"exit"},`chrome-extension://${chrome.runtime.id}`);const t=setTimeout((()=>{n.remove(),currentPopupTimeouts.delete(e)}),400);currentPopupTimeouts.set(e,t)}}function checkGoogleSearchLink(){const e=window.location.href;return/https:\/\/www\.google\.(com|[a-z]{2}|com\.[a-z]{2}|co\.[a-z]{2}|cat)\/search\?/.test(e)&&e.includes("q=")}function isPDFLink(e,t){return e&&e.match(/\.pdf(?=\?|#|$)/i)&&t.trim().length>0}let pdfLinksCache=null;function getPDFLinks(){if(null===pdfLinksCache){const e=Array.from(document.querySelectorAll("a[href]"));pdfLinksCache=e.filter((e=>isPDFLink(e.getAttribute("href"),e.textContent)))}return pdfLinksCache}function addPdfIconToLinks(){getPDFLinks().forEach((e=>{const t=e.getAttribute("href"),o=isGoogleSearchLink?e.getElementsByTagName("h3")[0]:e;e.addEventListener("mouseenter",(()=>{showAssistantMarker&&(hoveredLink=t,hoverTimeout=setTimeout((()=>{showPopup(o,t),chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:"dc-cv-genai-markers-impression-analytics",cachePurge:"NO_CALL"},(e=>{e&&chrome.runtime.sendMessage({main_op:"analytics",analytics:[["DCBrowserExt:PdfLink:Hovered"]]})}))}),200),o.style.backgroundColor="rgb(88, 76, 204, 0.1)",o.style.borderRadius="8px")})),e.addEventListener("mouseleave",(()=>{o.style.backgroundColor="",o.style.borderRadius="",setTimeout((()=>hidePopup(t)),400),hoveredLink=null,clearTimeout(hoverTimeout)}))}))}!async function(){const e=getPDFLinks().length>0;chrome.runtime.sendMessage({main_op:"resolve-has-pdf-promise",hasPDFLink:e});const t=await chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:"dc-cv-genai-markers",cachePurge:"NO_CALL"});if(!e||!t)return;const{appLocale:o,pdfViewer:n,egaf:s,aiMarkers:r,genAIEligible:i,locale:a}=await chrome.storage.local.get(["appLocale","pdfViewer","egaf","aiMarkers","genAIEligible","locale"]);if("en"!==o?.split("-")[0]||"en"!==a?.split("-")[0]||"false"===n||"false"===s||"false"===r||"true"!==i)return;const c="true"===(await chrome.storage.managed.get("DisableGenAI"))?.DisableGenAI,u=await chrome.runtime.sendMessage({main_op:"getFloodgateMeta",flag:"dc-cv-genai-markers-allowlist"});let l;try{l=JSON.parse(u||"[]").some((e=>window.location.href.match(e)))}catch(e){l=!1}!c&&l&&(chrome.runtime.sendMessage({main_op:"addExperimentCodeForAnalytics",experimentCode:"EGMS"}),addPdfIconToLinks())}(),chrome.runtime.onMessage.addListener(((e,t,o)=>{if("hideAIMarkerPopup"===e.content_op&&(hidePopup(e.href,!0),showAssistantMarker=!1),"updateIframeHeight"===e.content_op){const t=`__assistantPopup__${e.href}`,o=document.getElementById(t);o&&(o.style.height=(e.menuOpen?125:50)+"px",o.style.width=(e.menuOpen?350:250)+"px")}}));