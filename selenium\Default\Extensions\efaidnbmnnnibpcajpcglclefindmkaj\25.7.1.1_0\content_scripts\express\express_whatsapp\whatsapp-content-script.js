/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
class WhatsappExpressIntegration{previewEntryPointId="whatsapp-preview-adobe-express-entry-point";previewedImageClassname="x6s0dn4 x78zum5 x5yr21d xl56j7k x6ikm8r x10wlt62 x1n2onr6 xh8yej3 xhtitgo _ao3e";previewHeaderClassname="x78zum5 x6s0dn4 x1afcbsf x1totv3y";imageInMessageClassName="x15kfjtz x1c4vz4f x2lah0s xdl72j9 x127lhb5 x4afe7t xa3vuyk x10e4vud";gridImageClassName=".xh8yej3.xt7dq6l";touchpoint="whatsappPreview";hoverTouchpoint="whatsappHover";whatsappExpressStateKey="whatsappExpressState";launchExpress=(e,t,s)=>{chrome.runtime.sendMessage({main_op:"launch-express",imgUrl:e,intent:s,touchpoint:t})};sendAnalyticsEvent=e=>{try{chrome.runtime.sendMessage({main_op:"analytics",analytics:e})}catch(e){}};sendErrorLog=(e,t)=>{chrome.runtime.sendMessage({main_op:"log-error",log:{message:e,error:t}})};async loadContentScripts(){const e=chrome.runtime.getURL("content_scripts/express/single-click-cta.js"),t=await import(e);this.ExpressCTAClass=t.default,this.expressCTA=new this.ExpressCTAClass}addThumbnailAndImagePreviewerObserver=()=>{new MutationObserver((e=>{this.config.enableWhatsappPreviewExpressMenu&&this.imagePreviewerObserverHandler(),this.config.enableWhatsappHoverExpressMenu&&this.imageHoverObserverHandler(e)})).observe(document.body,{childList:!0,subtree:!0})};previewEntryPointClickHandler=e=>{if(!chrome?.runtime?.id)return void this.removePreviewEntryPoint(this.previewEntryPointId);const t=document.getElementsByClassName(this.previewedImageClassname)[0];if(!t||"IMG"!==t.tagName)return void this.sendErrorLog("Error executing express in whatsapp","image element not found");const s=t.src;s?(this.setTouchpointClickedForFteState(this.touchpoint),this.launchExpress(s,this.touchpoint,e)):this.sendErrorLog("Error executing express in whatsapp","image URL not found")};floatingButtonClickHandler=(e,t)=>{chrome?.runtime?.id?t?(this.setTouchpointClickedForFteState(this.hoverTouchpoint),this.launchExpress(t,this.hoverTouchpoint,e)):this.sendErrorLog("Error executing express in whatsapp","image URL not found"):this.removeAllFloatingButtons()};setTouchpointClickedForFteState=async e=>{let t=await chrome.storage.local.get(this.whatsappExpressStateKey);switch(t=t?.whatsappExpressState?t.whatsappExpressState:{},e){case this.touchpoint:t.previewTouchpointUsed=!0;break;case this.hoverTouchpoint:t.hoverTouchpointUsed=!0}chrome.storage.local.set({[this.whatsappExpressStateKey]:t})};createHoverButton=async e=>{const t=new this.ExpressCTAClass,s=await t.renderMenuButton((t=>this.floatingButtonClickHandler(t,e)),this.hoverTouchpoint);return s.style.position="absolute",s.style.top="10px",s.style.left="10px",s.style.opacity="0",s.style.transition="opacity 0.3s ease-in-out",t.attachTooltip("bottom"),s};createAndAttachHoverButton=async e=>{if(!chrome?.runtime?.id)return void this.removeAllFloatingButtons();if(0!==e.parentElement.getElementsByClassName(this.hoverTouchpoint).length)return;const t=await this.createHoverButton(e.src);e.parentElement.appendChild(t),e.parentElement.addEventListener("mouseenter",(()=>{this.showFloatingButtonToImageElement(t)})),e.parentElement.addEventListener("mouseleave",(()=>{this.hideFloatingButtonFromImageElement(t)}))};showFloatingButtonToImageElement=async e=>{"Y"!==e.getAttribute("impression-analytics-logged")&&(this.sendAnalyticsEvent([["DCBrowserExt:Express:Whatsapp:HoverEntryPoint:Shown"]]),e.setAttribute("impression-analytics-logged","Y")),e.style.opacity="1"};hideFloatingButtonFromImageElement=e=>{e&&(e.style.opacity="0")};removeAllFloatingButtons=()=>{const e=document.getElementsByClassName(this.hoverTouchpoint);for(const t of e)t.remove()};imageHoverObserverHandler=e=>{for(const t of e)if("childList"===t.type)for(const e of t.addedNodes){const t=e.getElementsByClassName?.(this.imageInMessageClassName);if(t)for(const e of t)e.closest(this.gridImageClassName)||this.createAndAttachHoverButton(e)}};imagePreviewerObserverHandler=()=>{const e=document.getElementsByClassName(this.previewHeaderClassname)[0];if(!e)return void this.cleanup();const t=document.getElementById(this.previewEntryPointId),s=document.getElementsByClassName(this.previewedImageClassname)[0];s&&"IMG"===s.tagName?t||(chrome?.runtime?.id?(this.addPreviewEntryPoint(e,this.previewEntryPointId,this.previewEntryPointClickHandler),this.sendAnalyticsEvent([["DCBrowserExt:Express:Whatsapp:PreviewEntryPoint:Shown"]])):this.cleanup()):this.cleanup()};addPreviewEntryPoint=async(e,t,s)=>{if(document.getElementById(t))return;const i=await this.expressCTA.renderMenuButton(s,this.touchpoint);i.id=t,e.insertBefore(i,e.firstChild),this.tooltip=this.expressCTA.attachTooltip("bottom"),chrome.runtime.sendMessage({main_op:"whatsapp-express-touch-point-added"})};removePreviewEntryPoint=e=>{const t=document.getElementById(e);t&&t.remove()};cleanup=()=>{this.removePreviewEntryPoint(this.previewEntryPointId),this.tooltip?.tooltip?.remove(),this.tooltip=null;const e=document.getElementById("express-contextual-fte");e&&e.remove()};init=async()=>{const e=await chrome.runtime.sendMessage({main_op:"whatsapp-express-init"});this.config=e,(this.config.enableWhatsappPreviewExpressMenu||this.config.enableWhatsappHoverExpressMenu)&&(this.previewedImageClassname=this.config.selectors?.previewedImageClassname||this.previewedImageClassname,this.previewHeaderClassname=this.config.selectors?.previewHeaderClassname||this.previewHeaderClassname,this.imageInMessageClassName=this.config.selectors?.imageInMessageClassName||this.imageInMessageClassName,this.gridImageClassName=this.config.selectors?.gridImageClassName||this.gridImageClassName,await this.loadContentScripts(),this.addThumbnailAndImagePreviewerObserver())}}const whatsappExpressIntegration=new WhatsappExpressIntegration;whatsappExpressIntegration.init();