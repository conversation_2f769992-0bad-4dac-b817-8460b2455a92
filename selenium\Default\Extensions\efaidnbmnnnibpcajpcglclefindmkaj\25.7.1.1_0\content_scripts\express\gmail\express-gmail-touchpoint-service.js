/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{getParentElementForNativeViewerPrompt}from"../../gmail/util.js";import state from"../../gmail/state.js";import expressDropdownMenu from"../dropdown-menu.js";import SingleClickCTA from"../single-click-cta.js";import{sendAnalytics}from"../../gsuite/util.js";const previewEntryPointId="express-gmail-native-viewer-entry-point",touchpoint="gmailNativeViewer",_getVisibleViewer=()=>{const e=state?.expressConfig?.selectors?.activeViewElementId??"drive-active-item-info",t=document.getElementById(e);return t?t.parentElement:null},_isImagePreviewed=()=>{const e=(state?.expressConfig?.selectors??{}).imgPrev??"aLF-aPX-J1-J3",t=_getVisibleViewer();if(!t)return null;const s=t.getElementsByClassName(e);return s&&s.length>0?s[0].getAttribute("src"):null},_clickCallback=e=>{chrome.runtime.sendMessage({main_op:"launch-express",imgUrl:state.expressState?.imageURL,intent:e,touchpoint:touchpoint})},toggleButtonSize=(e,t)=>{e.style.width=t?"32px":"";const s=e.querySelector(".cc440d50ba-express-entrypoint-button-text"),i=e.querySelector(".cc440d50ba-express-entrypoint-button-icon");s&&(s.style.display=t?"none":""),i&&(i.style.padding=t?"7px 9px 7px 5px":"",i.style.width=t?"32px":"")},updateButtonStyles=e=>{if(!e)return;e.style.visibility="hidden",toggleButtonSize(e,!1);const t=e.offsetWidth,s=window.innerWidth-t<1e3;toggleButtonSize(e,s),e.style.visibility="visible"},_handleResponsiveButton=e=>{const t=e.querySelector(".cc440d50ba-express-entrypoint-button");updateButtonStyles(t),state.expressState.resizeHandler=()=>updateButtonStyles(t),window.addEventListener("resize",state.expressState.resizeHandler)},_addExpressTouchpoint=async()=>{let e;const t=state?.expressConfig?.singleCTAEnabled;e=t?new SingleClickCTA:expressDropdownMenu,state.expressState.nativeViewerTouchpointVisible=!0;const s=await e.renderMenuButton(_clickCallback,touchpoint);s.id=previewEntryPointId;const i=getParentElementForNativeViewerPrompt();i&&i?.childNodes?.length>0&&(i.insertBefore(s,i.childNodes[0]),_handleResponsiveButton(s),t&&(state.expressState.tooltip=e.attachTooltip("bottom")),sendAnalytics([["DCBrowserExt:Express:Gmail:NativeViewerEntryPoint:Shown"]]))},addExpressNativeViewerTouchpoint=()=>{const e=_isImagePreviewed();if(!e)return void removeExpressTouchpoints();const{nativeViewerTouchpointVisible:t,imageURL:s}=state?.expressState;t||_addExpressTouchpoint(),s!==e&&(state.expressState.imageURL=e)},removeExpressTouchpoints=()=>{const e=document.getElementById(previewEntryPointId);e&&(state.expressState?.resizeHandler&&(window.removeEventListener("resize",state.expressState.resizeHandler),state.expressState.resizeHandler=null),state.expressState.imageURL=null,state.expressState.nativeViewerTouchpointVisible=!1,e.parentElement?.removeChild(e)),state.expressState.tooltip?.tooltip?.remove(),state.expressState.tooltip=null};export{addExpressNativeViewerTouchpoint,removeExpressTouchpoints};