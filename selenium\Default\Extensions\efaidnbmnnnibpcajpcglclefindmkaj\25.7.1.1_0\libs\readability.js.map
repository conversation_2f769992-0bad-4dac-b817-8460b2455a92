{"version": 3, "file": "readability.js", "sources": ["../../../node_modules/@mozilla/readability/Readability.js", "../../../node_modules/@mozilla/readability/Readability-readerable.js", "../../../node_modules/@mozilla/readability/index.js"], "sourcesContent": ["/*\n * Copyright (c) 2010 Arc90 Inc\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This code is heavily based on Arc90's readability.js (1.7.1) script\n * available at: http://code.google.com/p/arc90labs-readability\n */\n\n/**\n * Public constructor.\n * @param {HTMLDocument} doc     The document to parse.\n * @param {Object}       options The options object.\n */\nfunction Readability(doc, options) {\n  // In some older versions, people passed a URI as the first argument. Cope:\n  if (options && options.documentElement) {\n    doc = options;\n    options = arguments[2];\n  } else if (!doc || !doc.documentElement) {\n    throw new Error(\"First argument to Readability constructor should be a document object.\");\n  }\n  options = options || {};\n\n  this._doc = doc;\n  this._docJSDOMParser = this._doc.firstChild.__JSDOMParser__;\n  this._articleTitle = null;\n  this._articleByline = null;\n  this._articleDir = null;\n  this._articleSiteName = null;\n  this._attempts = [];\n\n  // Configurable options\n  this._debug = !!options.debug;\n  this._maxElemsToParse = options.maxElemsToParse || this.DEFAULT_MAX_ELEMS_TO_PARSE;\n  this._nbTopCandidates = options.nbTopCandidates || this.DEFAULT_N_TOP_CANDIDATES;\n  this._charThreshold = options.charThreshold || this.DEFAULT_CHAR_THRESHOLD;\n  this._classesToPreserve = this.CLASSES_TO_PRESERVE.concat(options.classesToPreserve || []);\n  this._keepClasses = !!options.keepClasses;\n  this._serializer = options.serializer || function(el) {\n    return el.innerHTML;\n  };\n  this._disableJSONLD = !!options.disableJSONLD;\n  this._allowedVideoRegex = options.allowedVideoRegex || this.REGEXPS.videos;\n\n  // Start with all flags set\n  this._flags = this.FLAG_STRIP_UNLIKELYS |\n                this.FLAG_WEIGHT_CLASSES |\n                this.FLAG_CLEAN_CONDITIONALLY;\n\n\n  // Control whether log messages are sent to the console\n  if (this._debug) {\n    let logNode = function(node) {\n      if (node.nodeType == node.TEXT_NODE) {\n        return `${node.nodeName} (\"${node.textContent}\")`;\n      }\n      let attrPairs = Array.from(node.attributes || [], function(attr) {\n        return `${attr.name}=\"${attr.value}\"`;\n      }).join(\" \");\n      return `<${node.localName} ${attrPairs}>`;\n    };\n    this.log = function () {\n      if (typeof console !== \"undefined\") {\n        let args = Array.from(arguments, arg => {\n          if (arg && arg.nodeType == this.ELEMENT_NODE) {\n            return logNode(arg);\n          }\n          return arg;\n        });\n        args.unshift(\"Reader: (Readability)\");\n        console.log.apply(console, args);\n      } else if (typeof dump !== \"undefined\") {\n        /* global dump */\n        var msg = Array.prototype.map.call(arguments, function(x) {\n          return (x && x.nodeName) ? logNode(x) : x;\n        }).join(\" \");\n        dump(\"Reader: (Readability) \" + msg + \"\\n\");\n      }\n    };\n  } else {\n    this.log = function () {};\n  }\n}\n\nReadability.prototype = {\n  FLAG_STRIP_UNLIKELYS: 0x1,\n  FLAG_WEIGHT_CLASSES: 0x2,\n  FLAG_CLEAN_CONDITIONALLY: 0x4,\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n  ELEMENT_NODE: 1,\n  TEXT_NODE: 3,\n\n  // Max number of nodes supported by this parser. Default: 0 (no limit)\n  DEFAULT_MAX_ELEMS_TO_PARSE: 0,\n\n  // The number of top candidates to consider when analysing how\n  // tight the competition is among candidates.\n  DEFAULT_N_TOP_CANDIDATES: 5,\n\n  // Element tags to score by default.\n  DEFAULT_TAGS_TO_SCORE: \"section,h2,h3,h4,h5,h6,p,td,pre\".toUpperCase().split(\",\"),\n\n  // The default number of chars an article must have in order to return a result\n  DEFAULT_CHAR_THRESHOLD: 500,\n\n  // All of the regular expressions in use within readability.\n  // Defined up here so we don't instantiate them repeatedly in loops.\n  REGEXPS: {\n    // NOTE: These two regular expressions are duplicated in\n    // Readability-readerable.js. Please keep both copies in sync.\n    unlikelyCandidates: /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,\n    okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,\n\n    positive: /article|body|content|entry|hentry|h-entry|main|page|pagination|post|text|blog|story/i,\n    negative: /-ad-|hidden|^hid$| hid$| hid |^hid |banner|combx|comment|com-|contact|foot|footer|footnote|gdpr|masthead|media|meta|outbrain|promo|related|scroll|share|shoutbox|sidebar|skyscraper|sponsor|shopping|tags|tool|widget/i,\n    extraneous: /print|archive|comment|discuss|e[\\-]?mail|share|reply|all|login|sign|single|utility/i,\n    byline: /byline|author|dateline|writtenby|p-author/i,\n    replaceFonts: /<(\\/?)font[^>]*>/gi,\n    normalize: /\\s{2,}/g,\n    videos: /\\/\\/(www\\.)?((dailymotion|youtube|youtube-nocookie|player\\.vimeo|v\\.qq)\\.com|(archive|upload\\.wikimedia)\\.org|player\\.twitch\\.tv)/i,\n    shareElements: /(\\b|_)(share|sharedaddy)(\\b|_)/i,\n    nextLink: /(next|weiter|continue|>([^\\|]|$)|»([^\\|]|$))/i,\n    prevLink: /(prev|earl|old|new|<|«)/i,\n    tokenize: /\\W+/g,\n    whitespace: /^\\s*$/,\n    hasContent: /\\S$/,\n    hashUrl: /^#.+/,\n    srcsetUrl: /(\\S+)(\\s+[\\d.]+[xw])?(\\s*(?:,|$))/g,\n    b64DataUrl: /^data:\\s*([^\\s;,]+)\\s*;\\s*base64\\s*,/i,\n    // Commas as used in Latin, Sindhi, Chinese and various other scripts.\n    // see: https://en.wikipedia.org/wiki/Comma#Comma_variants\n    commas: /\\u002C|\\u060C|\\uFE50|\\uFE10|\\uFE11|\\u2E41|\\u2E34|\\u2E32|\\uFF0C/g,\n    // See: https://schema.org/Article\n    jsonLdArticleTypes: /^Article|AdvertiserContentArticle|NewsArticle|AnalysisNewsArticle|AskPublicNewsArticle|BackgroundNewsArticle|OpinionNewsArticle|ReportageNewsArticle|ReviewNewsArticle|Report|SatiricalArticle|ScholarlyArticle|MedicalScholarlyArticle|SocialMediaPosting|BlogPosting|LiveBlogPosting|DiscussionForumPosting|TechArticle|APIReference$/\n  },\n\n  UNLIKELY_ROLES: [ \"menu\", \"menubar\", \"complementary\", \"navigation\", \"alert\", \"alertdialog\", \"dialog\" ],\n\n  DIV_TO_P_ELEMS: new Set([ \"BLOCKQUOTE\", \"DL\", \"DIV\", \"IMG\", \"OL\", \"P\", \"PRE\", \"TABLE\", \"UL\" ]),\n\n  ALTER_TO_DIV_EXCEPTIONS: [\"DIV\", \"ARTICLE\", \"SECTION\", \"P\"],\n\n  PRESENTATIONAL_ATTRIBUTES: [ \"align\", \"background\", \"bgcolor\", \"border\", \"cellpadding\", \"cellspacing\", \"frame\", \"hspace\", \"rules\", \"style\", \"valign\", \"vspace\" ],\n\n  DEPRECATED_SIZE_ATTRIBUTE_ELEMS: [ \"TABLE\", \"TH\", \"TD\", \"HR\", \"PRE\" ],\n\n  // The commented out elements qualify as phrasing content but tend to be\n  // removed by readability when put into paragraphs, so we ignore them here.\n  PHRASING_ELEMS: [\n    // \"CANVAS\", \"IFRAME\", \"SVG\", \"VIDEO\",\n    \"ABBR\", \"AUDIO\", \"B\", \"BDO\", \"BR\", \"BUTTON\", \"CITE\", \"CODE\", \"DATA\",\n    \"DATALIST\", \"DFN\", \"EM\", \"EMBED\", \"I\", \"IMG\", \"INPUT\", \"KBD\", \"LABEL\",\n    \"MARK\", \"MATH\", \"METER\", \"NOSCRIPT\", \"OBJECT\", \"OUTPUT\", \"PROGRESS\", \"Q\",\n    \"RUBY\", \"SAMP\", \"SCRIPT\", \"SELECT\", \"SMALL\", \"SPAN\", \"STRONG\", \"SUB\",\n    \"SUP\", \"TEXTAREA\", \"TIME\", \"VAR\", \"WBR\"\n  ],\n\n  // These are the classes that readability sets itself.\n  CLASSES_TO_PRESERVE: [ \"page\" ],\n\n  // These are the list of HTML entities that need to be escaped.\n  HTML_ESCAPE_MAP: {\n    \"lt\": \"<\",\n    \"gt\": \">\",\n    \"amp\": \"&\",\n    \"quot\": '\"',\n    \"apos\": \"'\",\n  },\n\n  /**\n   * Run any post-process modifications to article content as necessary.\n   *\n   * @param Element\n   * @return void\n  **/\n  _postProcessContent: function(articleContent) {\n    // Readability cannot open relative uris so we convert them to absolute uris.\n    this._fixRelativeUris(articleContent);\n\n    this._simplifyNestedElements(articleContent);\n\n    if (!this._keepClasses) {\n      // Remove classes.\n      this._cleanClasses(articleContent);\n    }\n  },\n\n  /**\n   * Iterates over a NodeList, calls `filterFn` for each node and removes node\n   * if function returned `true`.\n   *\n   * If function is not passed, removes all the nodes in node list.\n   *\n   * @param NodeList nodeList The nodes to operate on\n   * @param Function filterFn the function to use as a filter\n   * @return void\n   */\n  _removeNodes: function(nodeList, filterFn) {\n    // Avoid ever operating on live node lists.\n    if (this._docJSDOMParser && nodeList._isLiveNodeList) {\n      throw new Error(\"Do not pass live node lists to _removeNodes\");\n    }\n    for (var i = nodeList.length - 1; i >= 0; i--) {\n      var node = nodeList[i];\n      var parentNode = node.parentNode;\n      if (parentNode) {\n        if (!filterFn || filterFn.call(this, node, i, nodeList)) {\n          parentNode.removeChild(node);\n        }\n      }\n    }\n  },\n\n  /**\n   * Iterates over a NodeList, and calls _setNodeTag for each node.\n   *\n   * @param NodeList nodeList The nodes to operate on\n   * @param String newTagName the new tag name to use\n   * @return void\n   */\n  _replaceNodeTags: function(nodeList, newTagName) {\n    // Avoid ever operating on live node lists.\n    if (this._docJSDOMParser && nodeList._isLiveNodeList) {\n      throw new Error(\"Do not pass live node lists to _replaceNodeTags\");\n    }\n    for (const node of nodeList) {\n      this._setNodeTag(node, newTagName);\n    }\n  },\n\n  /**\n   * Iterate over a NodeList, which doesn't natively fully implement the Array\n   * interface.\n   *\n   * For convenience, the current object context is applied to the provided\n   * iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return void\n   */\n  _forEachNode: function(nodeList, fn) {\n    Array.prototype.forEach.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, and return the first node that passes\n   * the supplied test function\n   *\n   * For convenience, the current object context is applied to the provided\n   * test function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The test function.\n   * @return void\n   */\n  _findNode: function(nodeList, fn) {\n    return Array.prototype.find.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, return true if any of the provided iterate\n   * function calls returns true, false otherwise.\n   *\n   * For convenience, the current object context is applied to the\n   * provided iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return Boolean\n   */\n  _someNode: function(nodeList, fn) {\n    return Array.prototype.some.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, return true if all of the provided iterate\n   * function calls return true, false otherwise.\n   *\n   * For convenience, the current object context is applied to the\n   * provided iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return Boolean\n   */\n  _everyNode: function(nodeList, fn) {\n    return Array.prototype.every.call(nodeList, fn, this);\n  },\n\n  /**\n   * Concat all nodelists passed as arguments.\n   *\n   * @return ...NodeList\n   * @return Array\n   */\n  _concatNodeLists: function() {\n    var slice = Array.prototype.slice;\n    var args = slice.call(arguments);\n    var nodeLists = args.map(function(list) {\n      return slice.call(list);\n    });\n    return Array.prototype.concat.apply([], nodeLists);\n  },\n\n  _getAllNodesWithTag: function(node, tagNames) {\n    if (node.querySelectorAll) {\n      return node.querySelectorAll(tagNames.join(\",\"));\n    }\n    return [].concat.apply([], tagNames.map(function(tag) {\n      var collection = node.getElementsByTagName(tag);\n      return Array.isArray(collection) ? collection : Array.from(collection);\n    }));\n  },\n\n  /**\n   * Removes the class=\"\" attribute from every element in the given\n   * subtree, except those that match CLASSES_TO_PRESERVE and\n   * the classesToPreserve array from the options object.\n   *\n   * @param Element\n   * @return void\n   */\n  _cleanClasses: function(node) {\n    var classesToPreserve = this._classesToPreserve;\n    var className = (node.getAttribute(\"class\") || \"\")\n      .split(/\\s+/)\n      .filter(function(cls) {\n        return classesToPreserve.indexOf(cls) != -1;\n      })\n      .join(\" \");\n\n    if (className) {\n      node.setAttribute(\"class\", className);\n    } else {\n      node.removeAttribute(\"class\");\n    }\n\n    for (node = node.firstElementChild; node; node = node.nextElementSibling) {\n      this._cleanClasses(node);\n    }\n  },\n\n  /**\n   * Converts each <a> and <img> uri in the given element to an absolute URI,\n   * ignoring #ref URIs.\n   *\n   * @param Element\n   * @return void\n   */\n  _fixRelativeUris: function(articleContent) {\n    var baseURI = this._doc.baseURI;\n    var documentURI = this._doc.documentURI;\n    function toAbsoluteURI(uri) {\n      // Leave hash links alone if the base URI matches the document URI:\n      if (baseURI == documentURI && uri.charAt(0) == \"#\") {\n        return uri;\n      }\n\n      // Otherwise, resolve against base URI:\n      try {\n        return new URL(uri, baseURI).href;\n      } catch (ex) {\n        // Something went wrong, just return the original:\n      }\n      return uri;\n    }\n\n    var links = this._getAllNodesWithTag(articleContent, [\"a\"]);\n    this._forEachNode(links, function(link) {\n      var href = link.getAttribute(\"href\");\n      if (href) {\n        // Remove links with javascript: URIs, since\n        // they won't work after scripts have been removed from the page.\n        if (href.indexOf(\"javascript:\") === 0) {\n          // if the link only contains simple text content, it can be converted to a text node\n          if (link.childNodes.length === 1 && link.childNodes[0].nodeType === this.TEXT_NODE) {\n            var text = this._doc.createTextNode(link.textContent);\n            link.parentNode.replaceChild(text, link);\n          } else {\n            // if the link has multiple children, they should all be preserved\n            var container = this._doc.createElement(\"span\");\n            while (link.firstChild) {\n              container.appendChild(link.firstChild);\n            }\n            link.parentNode.replaceChild(container, link);\n          }\n        } else {\n          link.setAttribute(\"href\", toAbsoluteURI(href));\n        }\n      }\n    });\n\n    var medias = this._getAllNodesWithTag(articleContent, [\n      \"img\", \"picture\", \"figure\", \"video\", \"audio\", \"source\"\n    ]);\n\n    this._forEachNode(medias, function(media) {\n      var src = media.getAttribute(\"src\");\n      var poster = media.getAttribute(\"poster\");\n      var srcset = media.getAttribute(\"srcset\");\n\n      if (src) {\n        media.setAttribute(\"src\", toAbsoluteURI(src));\n      }\n\n      if (poster) {\n        media.setAttribute(\"poster\", toAbsoluteURI(poster));\n      }\n\n      if (srcset) {\n        var newSrcset = srcset.replace(this.REGEXPS.srcsetUrl, function(_, p1, p2, p3) {\n          return toAbsoluteURI(p1) + (p2 || \"\") + p3;\n        });\n\n        media.setAttribute(\"srcset\", newSrcset);\n      }\n    });\n  },\n\n  _simplifyNestedElements: function(articleContent) {\n    var node = articleContent;\n\n    while (node) {\n      if (node.parentNode && [\"DIV\", \"SECTION\"].includes(node.tagName) && !(node.id && node.id.startsWith(\"readability\"))) {\n        if (this._isElementWithoutContent(node)) {\n          node = this._removeAndGetNext(node);\n          continue;\n        } else if (this._hasSingleTagInsideElement(node, \"DIV\") || this._hasSingleTagInsideElement(node, \"SECTION\")) {\n          var child = node.children[0];\n          for (var i = 0; i < node.attributes.length; i++) {\n            child.setAttribute(node.attributes[i].name, node.attributes[i].value);\n          }\n          node.parentNode.replaceChild(child, node);\n          node = child;\n          continue;\n        }\n      }\n\n      node = this._getNextNode(node);\n    }\n  },\n\n  /**\n   * Get the article title as an H1.\n   *\n   * @return string\n   **/\n  _getArticleTitle: function() {\n    var doc = this._doc;\n    var curTitle = \"\";\n    var origTitle = \"\";\n\n    try {\n      curTitle = origTitle = doc.title.trim();\n\n      // If they had an element with id \"title\" in their HTML\n      if (typeof curTitle !== \"string\")\n        curTitle = origTitle = this._getInnerText(doc.getElementsByTagName(\"title\")[0]);\n    } catch (e) {/* ignore exceptions setting the title. */}\n\n    var titleHadHierarchicalSeparators = false;\n    function wordCount(str) {\n      return str.split(/\\s+/).length;\n    }\n\n    // If there's a separator in the title, first remove the final part\n    if ((/ [\\|\\-\\\\\\/>»] /).test(curTitle)) {\n      titleHadHierarchicalSeparators = / [\\\\\\/>»] /.test(curTitle);\n      curTitle = origTitle.replace(/(.*)[\\|\\-\\\\\\/>»] .*/gi, \"$1\");\n\n      // If the resulting title is too short (3 words or fewer), remove\n      // the first part instead:\n      if (wordCount(curTitle) < 3)\n        curTitle = origTitle.replace(/[^\\|\\-\\\\\\/>»]*[\\|\\-\\\\\\/>»](.*)/gi, \"$1\");\n    } else if (curTitle.indexOf(\": \") !== -1) {\n      // Check if we have an heading containing this exact string, so we\n      // could assume it's the full title.\n      var headings = this._concatNodeLists(\n        doc.getElementsByTagName(\"h1\"),\n        doc.getElementsByTagName(\"h2\")\n      );\n      var trimmedTitle = curTitle.trim();\n      var match = this._someNode(headings, function(heading) {\n        return heading.textContent.trim() === trimmedTitle;\n      });\n\n      // If we don't, let's extract the title out of the original title string.\n      if (!match) {\n        curTitle = origTitle.substring(origTitle.lastIndexOf(\":\") + 1);\n\n        // If the title is now too short, try the first colon instead:\n        if (wordCount(curTitle) < 3) {\n          curTitle = origTitle.substring(origTitle.indexOf(\":\") + 1);\n          // But if we have too many words before the colon there's something weird\n          // with the titles and the H tags so let's just use the original title instead\n        } else if (wordCount(origTitle.substr(0, origTitle.indexOf(\":\"))) > 5) {\n          curTitle = origTitle;\n        }\n      }\n    } else if (curTitle.length > 150 || curTitle.length < 15) {\n      var hOnes = doc.getElementsByTagName(\"h1\");\n\n      if (hOnes.length === 1)\n        curTitle = this._getInnerText(hOnes[0]);\n    }\n\n    curTitle = curTitle.trim().replace(this.REGEXPS.normalize, \" \");\n    // If we now have 4 words or fewer as our title, and either no\n    // 'hierarchical' separators (\\, /, > or ») were found in the original\n    // title or we decreased the number of words by more than 1 word, use\n    // the original title.\n    var curTitleWordCount = wordCount(curTitle);\n    if (curTitleWordCount <= 4 &&\n        (!titleHadHierarchicalSeparators ||\n         curTitleWordCount != wordCount(origTitle.replace(/[\\|\\-\\\\\\/>»]+/g, \"\")) - 1)) {\n      curTitle = origTitle;\n    }\n\n    return curTitle;\n  },\n\n  /**\n   * Prepare the HTML document for readability to scrape it.\n   * This includes things like stripping javascript, CSS, and handling terrible markup.\n   *\n   * @return void\n   **/\n  _prepDocument: function() {\n    var doc = this._doc;\n\n    // Remove all style tags in head\n    this._removeNodes(this._getAllNodesWithTag(doc, [\"style\"]));\n\n    if (doc.body) {\n      this._replaceBrs(doc.body);\n    }\n\n    this._replaceNodeTags(this._getAllNodesWithTag(doc, [\"font\"]), \"SPAN\");\n  },\n\n  /**\n   * Finds the next node, starting from the given node, and ignoring\n   * whitespace in between. If the given node is an element, the same node is\n   * returned.\n   */\n  _nextNode: function (node) {\n    var next = node;\n    while (next\n        && (next.nodeType != this.ELEMENT_NODE)\n        && this.REGEXPS.whitespace.test(next.textContent)) {\n      next = next.nextSibling;\n    }\n    return next;\n  },\n\n  /**\n   * Replaces 2 or more successive <br> elements with a single <p>.\n   * Whitespace between <br> elements are ignored. For example:\n   *   <div>foo<br>bar<br> <br><br>abc</div>\n   * will become:\n   *   <div>foo<br>bar<p>abc</p></div>\n   */\n  _replaceBrs: function (elem) {\n    this._forEachNode(this._getAllNodesWithTag(elem, [\"br\"]), function(br) {\n      var next = br.nextSibling;\n\n      // Whether 2 or more <br> elements have been found and replaced with a\n      // <p> block.\n      var replaced = false;\n\n      // If we find a <br> chain, remove the <br>s until we hit another node\n      // or non-whitespace. This leaves behind the first <br> in the chain\n      // (which will be replaced with a <p> later).\n      while ((next = this._nextNode(next)) && (next.tagName == \"BR\")) {\n        replaced = true;\n        var brSibling = next.nextSibling;\n        next.parentNode.removeChild(next);\n        next = brSibling;\n      }\n\n      // If we removed a <br> chain, replace the remaining <br> with a <p>. Add\n      // all sibling nodes as children of the <p> until we hit another <br>\n      // chain.\n      if (replaced) {\n        var p = this._doc.createElement(\"p\");\n        br.parentNode.replaceChild(p, br);\n\n        next = p.nextSibling;\n        while (next) {\n          // If we've hit another <br><br>, we're done adding children to this <p>.\n          if (next.tagName == \"BR\") {\n            var nextElem = this._nextNode(next.nextSibling);\n            if (nextElem && nextElem.tagName == \"BR\")\n              break;\n          }\n\n          if (!this._isPhrasingContent(next))\n            break;\n\n          // Otherwise, make this node a child of the new <p>.\n          var sibling = next.nextSibling;\n          p.appendChild(next);\n          next = sibling;\n        }\n\n        while (p.lastChild && this._isWhitespace(p.lastChild)) {\n          p.removeChild(p.lastChild);\n        }\n\n        if (p.parentNode.tagName === \"P\")\n          this._setNodeTag(p.parentNode, \"DIV\");\n      }\n    });\n  },\n\n  _setNodeTag: function (node, tag) {\n    this.log(\"_setNodeTag\", node, tag);\n    if (this._docJSDOMParser) {\n      node.localName = tag.toLowerCase();\n      node.tagName = tag.toUpperCase();\n      return node;\n    }\n\n    var replacement = node.ownerDocument.createElement(tag);\n    while (node.firstChild) {\n      replacement.appendChild(node.firstChild);\n    }\n    node.parentNode.replaceChild(replacement, node);\n    if (node.readability)\n      replacement.readability = node.readability;\n\n    for (var i = 0; i < node.attributes.length; i++) {\n      try {\n        replacement.setAttribute(node.attributes[i].name, node.attributes[i].value);\n      } catch (ex) {\n        /* it's possible for setAttribute() to throw if the attribute name\n         * isn't a valid XML Name. Such attributes can however be parsed from\n         * source in HTML docs, see https://github.com/whatwg/html/issues/4275,\n         * so we can hit them here and then throw. We don't care about such\n         * attributes so we ignore them.\n         */\n      }\n    }\n    return replacement;\n  },\n\n  /**\n   * Prepare the article node for display. Clean out any inline styles,\n   * iframes, forms, strip extraneous <p> tags, etc.\n   *\n   * @param Element\n   * @return void\n   **/\n  _prepArticle: function(articleContent) {\n    this._cleanStyles(articleContent);\n\n    // Check for data tables before we continue, to avoid removing items in\n    // those tables, which will often be isolated even though they're\n    // visually linked to other content-ful elements (text, images, etc.).\n    this._markDataTables(articleContent);\n\n    this._fixLazyImages(articleContent);\n\n    // Clean out junk from the article content\n    this._cleanConditionally(articleContent, \"form\");\n    this._cleanConditionally(articleContent, \"fieldset\");\n    this._clean(articleContent, \"object\");\n    this._clean(articleContent, \"embed\");\n    this._clean(articleContent, \"footer\");\n    this._clean(articleContent, \"link\");\n    this._clean(articleContent, \"aside\");\n\n    // Clean out elements with little content that have \"share\" in their id/class combinations from final top candidates,\n    // which means we don't remove the top candidates even they have \"share\".\n\n    var shareElementThreshold = this.DEFAULT_CHAR_THRESHOLD;\n\n    this._forEachNode(articleContent.children, function (topCandidate) {\n      this._cleanMatchedNodes(topCandidate, function (node, matchString) {\n        return this.REGEXPS.shareElements.test(matchString) && node.textContent.length < shareElementThreshold;\n      });\n    });\n\n    this._clean(articleContent, \"iframe\");\n    this._clean(articleContent, \"input\");\n    this._clean(articleContent, \"textarea\");\n    this._clean(articleContent, \"select\");\n    this._clean(articleContent, \"button\");\n    this._cleanHeaders(articleContent);\n\n    // Do these last as the previous stuff may have removed junk\n    // that will affect these\n    this._cleanConditionally(articleContent, \"table\");\n    this._cleanConditionally(articleContent, \"ul\");\n    this._cleanConditionally(articleContent, \"div\");\n\n    // replace H1 with H2 as H1 should be only title that is displayed separately\n    this._replaceNodeTags(this._getAllNodesWithTag(articleContent, [\"h1\"]), \"h2\");\n\n    // Remove extra paragraphs\n    this._removeNodes(this._getAllNodesWithTag(articleContent, [\"p\"]), function (paragraph) {\n      var imgCount = paragraph.getElementsByTagName(\"img\").length;\n      var embedCount = paragraph.getElementsByTagName(\"embed\").length;\n      var objectCount = paragraph.getElementsByTagName(\"object\").length;\n      // At this point, nasty iframes have been removed, only remain embedded video ones.\n      var iframeCount = paragraph.getElementsByTagName(\"iframe\").length;\n      var totalCount = imgCount + embedCount + objectCount + iframeCount;\n\n      return totalCount === 0 && !this._getInnerText(paragraph, false);\n    });\n\n    this._forEachNode(this._getAllNodesWithTag(articleContent, [\"br\"]), function(br) {\n      var next = this._nextNode(br.nextSibling);\n      if (next && next.tagName == \"P\")\n        br.parentNode.removeChild(br);\n    });\n\n    // Remove single-cell tables\n    this._forEachNode(this._getAllNodesWithTag(articleContent, [\"table\"]), function(table) {\n      var tbody = this._hasSingleTagInsideElement(table, \"TBODY\") ? table.firstElementChild : table;\n      if (this._hasSingleTagInsideElement(tbody, \"TR\")) {\n        var row = tbody.firstElementChild;\n        if (this._hasSingleTagInsideElement(row, \"TD\")) {\n          var cell = row.firstElementChild;\n          cell = this._setNodeTag(cell, this._everyNode(cell.childNodes, this._isPhrasingContent) ? \"P\" : \"DIV\");\n          table.parentNode.replaceChild(cell, table);\n        }\n      }\n    });\n  },\n\n  /**\n   * Initialize a node with the readability object. Also checks the\n   * className/id for special names to add to its score.\n   *\n   * @param Element\n   * @return void\n  **/\n  _initializeNode: function(node) {\n    node.readability = {\"contentScore\": 0};\n\n    switch (node.tagName) {\n      case \"DIV\":\n        node.readability.contentScore += 5;\n        break;\n\n      case \"PRE\":\n      case \"TD\":\n      case \"BLOCKQUOTE\":\n        node.readability.contentScore += 3;\n        break;\n\n      case \"ADDRESS\":\n      case \"OL\":\n      case \"UL\":\n      case \"DL\":\n      case \"DD\":\n      case \"DT\":\n      case \"LI\":\n      case \"FORM\":\n        node.readability.contentScore -= 3;\n        break;\n\n      case \"H1\":\n      case \"H2\":\n      case \"H3\":\n      case \"H4\":\n      case \"H5\":\n      case \"H6\":\n      case \"TH\":\n        node.readability.contentScore -= 5;\n        break;\n    }\n\n    node.readability.contentScore += this._getClassWeight(node);\n  },\n\n  _removeAndGetNext: function(node) {\n    var nextNode = this._getNextNode(node, true);\n    node.parentNode.removeChild(node);\n    return nextNode;\n  },\n\n  /**\n   * Traverse the DOM from node to node, starting at the node passed in.\n   * Pass true for the second parameter to indicate this node itself\n   * (and its kids) are going away, and we want the next node over.\n   *\n   * Calling this in a loop will traverse the DOM depth-first.\n   */\n  _getNextNode: function(node, ignoreSelfAndKids) {\n    // First check for kids if those aren't being ignored\n    if (!ignoreSelfAndKids && node.firstElementChild) {\n      return node.firstElementChild;\n    }\n    // Then for siblings...\n    if (node.nextElementSibling) {\n      return node.nextElementSibling;\n    }\n    // And finally, move up the parent chain *and* find a sibling\n    // (because this is depth-first traversal, we will have already\n    // seen the parent nodes themselves).\n    do {\n      node = node.parentNode;\n    } while (node && !node.nextElementSibling);\n    return node && node.nextElementSibling;\n  },\n\n  // compares second text to first one\n  // 1 = same text, 0 = completely different text\n  // works the way that it splits both texts into words and then finds words that are unique in second text\n  // the result is given by the lower length of unique parts\n  _textSimilarity: function(textA, textB) {\n    var tokensA = textA.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean);\n    var tokensB = textB.toLowerCase().split(this.REGEXPS.tokenize).filter(Boolean);\n    if (!tokensA.length || !tokensB.length) {\n      return 0;\n    }\n    var uniqTokensB = tokensB.filter(token => !tokensA.includes(token));\n    var distanceB = uniqTokensB.join(\" \").length / tokensB.join(\" \").length;\n    return 1 - distanceB;\n  },\n\n  _checkByline: function(node, matchString) {\n    if (this._articleByline) {\n      return false;\n    }\n\n    if (node.getAttribute !== undefined) {\n      var rel = node.getAttribute(\"rel\");\n      var itemprop = node.getAttribute(\"itemprop\");\n    }\n\n    if ((rel === \"author\" || (itemprop && itemprop.indexOf(\"author\") !== -1) || this.REGEXPS.byline.test(matchString)) && this._isValidByline(node.textContent)) {\n      this._articleByline = node.textContent.trim();\n      return true;\n    }\n\n    return false;\n  },\n\n  _getNodeAncestors: function(node, maxDepth) {\n    maxDepth = maxDepth || 0;\n    var i = 0, ancestors = [];\n    while (node.parentNode) {\n      ancestors.push(node.parentNode);\n      if (maxDepth && ++i === maxDepth)\n        break;\n      node = node.parentNode;\n    }\n    return ancestors;\n  },\n\n  /***\n   * grabArticle - Using a variety of metrics (content score, classname, element types), find the content that is\n   *         most likely to be the stuff a user wants to read. Then return it wrapped up in a div.\n   *\n   * @param page a document to run upon. Needs to be a full document, complete with body.\n   * @return Element\n  **/\n  _grabArticle: function (page) {\n    this.log(\"**** grabArticle ****\");\n    var doc = this._doc;\n    var isPaging = page !== null;\n    page = page ? page : this._doc.body;\n\n    // We can't grab an article if we don't have a page!\n    if (!page) {\n      this.log(\"No body found in document. Abort.\");\n      return null;\n    }\n\n    var pageCacheHtml = page.innerHTML;\n\n    while (true) {\n      this.log(\"Starting grabArticle loop\");\n      var stripUnlikelyCandidates = this._flagIsActive(this.FLAG_STRIP_UNLIKELYS);\n\n      // First, node prepping. Trash nodes that look cruddy (like ones with the\n      // class name \"comment\", etc), and turn divs into P tags where they have been\n      // used inappropriately (as in, where they contain no other block level elements.)\n      var elementsToScore = [];\n      var node = this._doc.documentElement;\n\n      let shouldRemoveTitleHeader = true;\n\n      while (node) {\n\n        if (node.tagName === \"HTML\") {\n          this._articleLang = node.getAttribute(\"lang\");\n        }\n\n        var matchString = node.className + \" \" + node.id;\n\n        if (!this._isProbablyVisible(node)) {\n          this.log(\"Removing hidden node - \" + matchString);\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // User is not able to see elements applied with both \"aria-modal = true\" and \"role = dialog\"\n        if (node.getAttribute(\"aria-modal\") == \"true\" && node.getAttribute(\"role\") == \"dialog\") {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // Check to see if this node is a byline, and remove it if it is.\n        if (this._checkByline(node, matchString)) {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        if (shouldRemoveTitleHeader && this._headerDuplicatesTitle(node)) {\n          this.log(\"Removing header: \", node.textContent.trim(), this._articleTitle.trim());\n          shouldRemoveTitleHeader = false;\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // Remove unlikely candidates\n        if (stripUnlikelyCandidates) {\n          if (this.REGEXPS.unlikelyCandidates.test(matchString) &&\n              !this.REGEXPS.okMaybeItsACandidate.test(matchString) &&\n              !this._hasAncestorTag(node, \"table\") &&\n              !this._hasAncestorTag(node, \"code\") &&\n              node.tagName !== \"BODY\" &&\n              node.tagName !== \"A\") {\n            this.log(\"Removing unlikely candidate - \" + matchString);\n            node = this._removeAndGetNext(node);\n            continue;\n          }\n\n          if (this.UNLIKELY_ROLES.includes(node.getAttribute(\"role\"))) {\n            this.log(\"Removing content with role \" + node.getAttribute(\"role\") + \" - \" + matchString);\n            node = this._removeAndGetNext(node);\n            continue;\n          }\n        }\n\n        // Remove DIV, SECTION, and HEADER nodes without any content(e.g. text, image, video, or iframe).\n        if ((node.tagName === \"DIV\" || node.tagName === \"SECTION\" || node.tagName === \"HEADER\" ||\n             node.tagName === \"H1\" || node.tagName === \"H2\" || node.tagName === \"H3\" ||\n             node.tagName === \"H4\" || node.tagName === \"H5\" || node.tagName === \"H6\") &&\n            this._isElementWithoutContent(node)) {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        if (this.DEFAULT_TAGS_TO_SCORE.indexOf(node.tagName) !== -1) {\n          elementsToScore.push(node);\n        }\n\n        // Turn all divs that don't have children block level elements into p's\n        if (node.tagName === \"DIV\") {\n          // Put phrasing content into paragraphs.\n          var p = null;\n          var childNode = node.firstChild;\n          while (childNode) {\n            var nextSibling = childNode.nextSibling;\n            if (this._isPhrasingContent(childNode)) {\n              if (p !== null) {\n                p.appendChild(childNode);\n              } else if (!this._isWhitespace(childNode)) {\n                p = doc.createElement(\"p\");\n                node.replaceChild(p, childNode);\n                p.appendChild(childNode);\n              }\n            } else if (p !== null) {\n              while (p.lastChild && this._isWhitespace(p.lastChild)) {\n                p.removeChild(p.lastChild);\n              }\n              p = null;\n            }\n            childNode = nextSibling;\n          }\n\n          // Sites like http://mobile.slate.com encloses each paragraph with a DIV\n          // element. DIVs with only a P element inside and no text content can be\n          // safely converted into plain P elements to avoid confusing the scoring\n          // algorithm with DIVs with are, in practice, paragraphs.\n          if (this._hasSingleTagInsideElement(node, \"P\") && this._getLinkDensity(node) < 0.25) {\n            var newNode = node.children[0];\n            node.parentNode.replaceChild(newNode, node);\n            node = newNode;\n            elementsToScore.push(node);\n          } else if (!this._hasChildBlockElement(node)) {\n            node = this._setNodeTag(node, \"P\");\n            elementsToScore.push(node);\n          }\n        }\n        node = this._getNextNode(node);\n      }\n\n      /**\n       * Loop through all paragraphs, and assign a score to them based on how content-y they look.\n       * Then add their score to their parent node.\n       *\n       * A score is determined by things like number of commas, class names, etc. Maybe eventually link density.\n      **/\n      var candidates = [];\n      this._forEachNode(elementsToScore, function(elementToScore) {\n        if (!elementToScore.parentNode || typeof(elementToScore.parentNode.tagName) === \"undefined\")\n          return;\n\n        // If this paragraph is less than 25 characters, don't even count it.\n        var innerText = this._getInnerText(elementToScore);\n        if (innerText.length < 25)\n          return;\n\n        // Exclude nodes with no ancestor.\n        var ancestors = this._getNodeAncestors(elementToScore, 5);\n        if (ancestors.length === 0)\n          return;\n\n        var contentScore = 0;\n\n        // Add a point for the paragraph itself as a base.\n        contentScore += 1;\n\n        // Add points for any commas within this paragraph.\n        contentScore += innerText.split(this.REGEXPS.commas).length;\n\n        // For every 100 characters in this paragraph, add another point. Up to 3 points.\n        contentScore += Math.min(Math.floor(innerText.length / 100), 3);\n\n        // Initialize and score ancestors.\n        this._forEachNode(ancestors, function(ancestor, level) {\n          if (!ancestor.tagName || !ancestor.parentNode || typeof(ancestor.parentNode.tagName) === \"undefined\")\n            return;\n\n          if (typeof(ancestor.readability) === \"undefined\") {\n            this._initializeNode(ancestor);\n            candidates.push(ancestor);\n          }\n\n          // Node score divider:\n          // - parent:             1 (no division)\n          // - grandparent:        2\n          // - great grandparent+: ancestor level * 3\n          if (level === 0)\n            var scoreDivider = 1;\n          else if (level === 1)\n            scoreDivider = 2;\n          else\n            scoreDivider = level * 3;\n          ancestor.readability.contentScore += contentScore / scoreDivider;\n        });\n      });\n\n      // After we've calculated scores, loop through all of the possible\n      // candidate nodes we found and find the one with the highest score.\n      var topCandidates = [];\n      for (var c = 0, cl = candidates.length; c < cl; c += 1) {\n        var candidate = candidates[c];\n\n        // Scale the final candidates score based on link density. Good content\n        // should have a relatively small link density (5% or less) and be mostly\n        // unaffected by this operation.\n        var candidateScore = candidate.readability.contentScore * (1 - this._getLinkDensity(candidate));\n        candidate.readability.contentScore = candidateScore;\n\n        this.log(\"Candidate:\", candidate, \"with score \" + candidateScore);\n\n        for (var t = 0; t < this._nbTopCandidates; t++) {\n          var aTopCandidate = topCandidates[t];\n\n          if (!aTopCandidate || candidateScore > aTopCandidate.readability.contentScore) {\n            topCandidates.splice(t, 0, candidate);\n            if (topCandidates.length > this._nbTopCandidates)\n              topCandidates.pop();\n            break;\n          }\n        }\n      }\n\n      var topCandidate = topCandidates[0] || null;\n      var neededToCreateTopCandidate = false;\n      var parentOfTopCandidate;\n\n      // If we still have no top candidate, just use the body as a last resort.\n      // We also have to copy the body node so it is something we can modify.\n      if (topCandidate === null || topCandidate.tagName === \"BODY\") {\n        // Move all of the page's children into topCandidate\n        topCandidate = doc.createElement(\"DIV\");\n        neededToCreateTopCandidate = true;\n        // Move everything (not just elements, also text nodes etc.) into the container\n        // so we even include text directly in the body:\n        while (page.firstChild) {\n          this.log(\"Moving child out:\", page.firstChild);\n          topCandidate.appendChild(page.firstChild);\n        }\n\n        page.appendChild(topCandidate);\n\n        this._initializeNode(topCandidate);\n      } else if (topCandidate) {\n        // Find a better top candidate node if it contains (at least three) nodes which belong to `topCandidates` array\n        // and whose scores are quite closed with current `topCandidate` node.\n        var alternativeCandidateAncestors = [];\n        for (var i = 1; i < topCandidates.length; i++) {\n          if (topCandidates[i].readability.contentScore / topCandidate.readability.contentScore >= 0.75) {\n            alternativeCandidateAncestors.push(this._getNodeAncestors(topCandidates[i]));\n          }\n        }\n        var MINIMUM_TOPCANDIDATES = 3;\n        if (alternativeCandidateAncestors.length >= MINIMUM_TOPCANDIDATES) {\n          parentOfTopCandidate = topCandidate.parentNode;\n          while (parentOfTopCandidate.tagName !== \"BODY\") {\n            var listsContainingThisAncestor = 0;\n            for (var ancestorIndex = 0; ancestorIndex < alternativeCandidateAncestors.length && listsContainingThisAncestor < MINIMUM_TOPCANDIDATES; ancestorIndex++) {\n              listsContainingThisAncestor += Number(alternativeCandidateAncestors[ancestorIndex].includes(parentOfTopCandidate));\n            }\n            if (listsContainingThisAncestor >= MINIMUM_TOPCANDIDATES) {\n              topCandidate = parentOfTopCandidate;\n              break;\n            }\n            parentOfTopCandidate = parentOfTopCandidate.parentNode;\n          }\n        }\n        if (!topCandidate.readability) {\n          this._initializeNode(topCandidate);\n        }\n\n        // Because of our bonus system, parents of candidates might have scores\n        // themselves. They get half of the node. There won't be nodes with higher\n        // scores than our topCandidate, but if we see the score going *up* in the first\n        // few steps up the tree, that's a decent sign that there might be more content\n        // lurking in other places that we want to unify in. The sibling stuff\n        // below does some of that - but only if we've looked high enough up the DOM\n        // tree.\n        parentOfTopCandidate = topCandidate.parentNode;\n        var lastScore = topCandidate.readability.contentScore;\n        // The scores shouldn't get too low.\n        var scoreThreshold = lastScore / 3;\n        while (parentOfTopCandidate.tagName !== \"BODY\") {\n          if (!parentOfTopCandidate.readability) {\n            parentOfTopCandidate = parentOfTopCandidate.parentNode;\n            continue;\n          }\n          var parentScore = parentOfTopCandidate.readability.contentScore;\n          if (parentScore < scoreThreshold)\n            break;\n          if (parentScore > lastScore) {\n            // Alright! We found a better parent to use.\n            topCandidate = parentOfTopCandidate;\n            break;\n          }\n          lastScore = parentOfTopCandidate.readability.contentScore;\n          parentOfTopCandidate = parentOfTopCandidate.parentNode;\n        }\n\n        // If the top candidate is the only child, use parent instead. This will help sibling\n        // joining logic when adjacent content is actually located in parent's sibling node.\n        parentOfTopCandidate = topCandidate.parentNode;\n        while (parentOfTopCandidate.tagName != \"BODY\" && parentOfTopCandidate.children.length == 1) {\n          topCandidate = parentOfTopCandidate;\n          parentOfTopCandidate = topCandidate.parentNode;\n        }\n        if (!topCandidate.readability) {\n          this._initializeNode(topCandidate);\n        }\n      }\n\n      // Now that we have the top candidate, look through its siblings for content\n      // that might also be related. Things like preambles, content split by ads\n      // that we removed, etc.\n      var articleContent = doc.createElement(\"DIV\");\n      if (isPaging)\n        articleContent.id = \"readability-content\";\n\n      var siblingScoreThreshold = Math.max(10, topCandidate.readability.contentScore * 0.2);\n      // Keep potential top candidate's parent node to try to get text direction of it later.\n      parentOfTopCandidate = topCandidate.parentNode;\n      var siblings = parentOfTopCandidate.children;\n\n      for (var s = 0, sl = siblings.length; s < sl; s++) {\n        var sibling = siblings[s];\n        var append = false;\n\n        this.log(\"Looking at sibling node:\", sibling, sibling.readability ? (\"with score \" + sibling.readability.contentScore) : \"\");\n        this.log(\"Sibling has score\", sibling.readability ? sibling.readability.contentScore : \"Unknown\");\n\n        if (sibling === topCandidate) {\n          append = true;\n        } else {\n          var contentBonus = 0;\n\n          // Give a bonus if sibling nodes and top candidates have the example same classname\n          if (sibling.className === topCandidate.className && topCandidate.className !== \"\")\n            contentBonus += topCandidate.readability.contentScore * 0.2;\n\n          if (sibling.readability &&\n              ((sibling.readability.contentScore + contentBonus) >= siblingScoreThreshold)) {\n            append = true;\n          } else if (sibling.nodeName === \"P\") {\n            var linkDensity = this._getLinkDensity(sibling);\n            var nodeContent = this._getInnerText(sibling);\n            var nodeLength = nodeContent.length;\n\n            if (nodeLength > 80 && linkDensity < 0.25) {\n              append = true;\n            } else if (nodeLength < 80 && nodeLength > 0 && linkDensity === 0 &&\n                       nodeContent.search(/\\.( |$)/) !== -1) {\n              append = true;\n            }\n          }\n        }\n\n        if (append) {\n          this.log(\"Appending node:\", sibling);\n\n          if (this.ALTER_TO_DIV_EXCEPTIONS.indexOf(sibling.nodeName) === -1) {\n            // We have a node that isn't a common block level element, like a form or td tag.\n            // Turn it into a div so it doesn't get filtered out later by accident.\n            this.log(\"Altering sibling:\", sibling, \"to div.\");\n\n            sibling = this._setNodeTag(sibling, \"DIV\");\n          }\n\n          articleContent.appendChild(sibling);\n          // Fetch children again to make it compatible\n          // with DOM parsers without live collection support.\n          siblings = parentOfTopCandidate.children;\n          // siblings is a reference to the children array, and\n          // sibling is removed from the array when we call appendChild().\n          // As a result, we must revisit this index since the nodes\n          // have been shifted.\n          s -= 1;\n          sl -= 1;\n        }\n      }\n\n      if (this._debug)\n        this.log(\"Article content pre-prep: \" + articleContent.innerHTML);\n      // So we have all of the content that we need. Now we clean it up for presentation.\n      this._prepArticle(articleContent);\n      if (this._debug)\n        this.log(\"Article content post-prep: \" + articleContent.innerHTML);\n\n      if (neededToCreateTopCandidate) {\n        // We already created a fake div thing, and there wouldn't have been any siblings left\n        // for the previous loop, so there's no point trying to create a new div, and then\n        // move all the children over. Just assign IDs and class names here. No need to append\n        // because that already happened anyway.\n        topCandidate.id = \"readability-page-1\";\n        topCandidate.className = \"page\";\n      } else {\n        var div = doc.createElement(\"DIV\");\n        div.id = \"readability-page-1\";\n        div.className = \"page\";\n        while (articleContent.firstChild) {\n          div.appendChild(articleContent.firstChild);\n        }\n        articleContent.appendChild(div);\n      }\n\n      if (this._debug)\n        this.log(\"Article content after paging: \" + articleContent.innerHTML);\n\n      var parseSuccessful = true;\n\n      // Now that we've gone through the full algorithm, check to see if\n      // we got any meaningful content. If we didn't, we may need to re-run\n      // grabArticle with different flags set. This gives us a higher likelihood of\n      // finding the content, and the sieve approach gives us a higher likelihood of\n      // finding the -right- content.\n      var textLength = this._getInnerText(articleContent, true).length;\n      if (textLength < this._charThreshold) {\n        parseSuccessful = false;\n        page.innerHTML = pageCacheHtml;\n\n        if (this._flagIsActive(this.FLAG_STRIP_UNLIKELYS)) {\n          this._removeFlag(this.FLAG_STRIP_UNLIKELYS);\n          this._attempts.push({articleContent: articleContent, textLength: textLength});\n        } else if (this._flagIsActive(this.FLAG_WEIGHT_CLASSES)) {\n          this._removeFlag(this.FLAG_WEIGHT_CLASSES);\n          this._attempts.push({articleContent: articleContent, textLength: textLength});\n        } else if (this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)) {\n          this._removeFlag(this.FLAG_CLEAN_CONDITIONALLY);\n          this._attempts.push({articleContent: articleContent, textLength: textLength});\n        } else {\n          this._attempts.push({articleContent: articleContent, textLength: textLength});\n          // No luck after removing flags, just return the longest text we found during the different loops\n          this._attempts.sort(function (a, b) {\n            return b.textLength - a.textLength;\n          });\n\n          // But first check if we actually have something\n          if (!this._attempts[0].textLength) {\n            return null;\n          }\n\n          articleContent = this._attempts[0].articleContent;\n          parseSuccessful = true;\n        }\n      }\n\n      if (parseSuccessful) {\n        // Find out text direction from ancestors of final top candidate.\n        var ancestors = [parentOfTopCandidate, topCandidate].concat(this._getNodeAncestors(parentOfTopCandidate));\n        this._someNode(ancestors, function(ancestor) {\n          if (!ancestor.tagName)\n            return false;\n          var articleDir = ancestor.getAttribute(\"dir\");\n          if (articleDir) {\n            this._articleDir = articleDir;\n            return true;\n          }\n          return false;\n        });\n        return articleContent;\n      }\n    }\n  },\n\n  /**\n   * Check whether the input string could be a byline.\n   * This verifies that the input is a string, and that the length\n   * is less than 100 chars.\n   *\n   * @param possibleByline {string} - a string to check whether its a byline.\n   * @return Boolean - whether the input string is a byline.\n   */\n  _isValidByline: function(byline) {\n    if (typeof byline == \"string\" || byline instanceof String) {\n      byline = byline.trim();\n      return (byline.length > 0) && (byline.length < 100);\n    }\n    return false;\n  },\n\n  /**\n   * Converts some of the common HTML entities in string to their corresponding characters.\n   *\n   * @param str {string} - a string to unescape.\n   * @return string without HTML entity.\n   */\n  _unescapeHtmlEntities: function(str) {\n    if (!str) {\n      return str;\n    }\n\n    var htmlEscapeMap = this.HTML_ESCAPE_MAP;\n    return str.replace(/&(quot|amp|apos|lt|gt);/g, function(_, tag) {\n      return htmlEscapeMap[tag];\n    }).replace(/&#(?:x([0-9a-z]{1,4})|([0-9]{1,4}));/gi, function(_, hex, numStr) {\n      var num = parseInt(hex || numStr, hex ? 16 : 10);\n      return String.fromCharCode(num);\n    });\n  },\n\n  /**\n   * Try to extract metadata from JSON-LD object.\n   * For now, only Schema.org objects of type Article or its subtypes are supported.\n   * @return Object with any metadata that could be extracted (possibly none)\n   */\n  _getJSONLD: function (doc) {\n    var scripts = this._getAllNodesWithTag(doc, [\"script\"]);\n\n    var metadata;\n\n    this._forEachNode(scripts, function(jsonLdElement) {\n      if (!metadata && jsonLdElement.getAttribute(\"type\") === \"application/ld+json\") {\n        try {\n          // Strip CDATA markers if present\n          var content = jsonLdElement.textContent.replace(/^\\s*<!\\[CDATA\\[|\\]\\]>\\s*$/g, \"\");\n          var parsed = JSON.parse(content);\n          if (\n            !parsed[\"@context\"] ||\n            !parsed[\"@context\"].match(/^https?\\:\\/\\/schema\\.org$/)\n          ) {\n            return;\n          }\n\n          if (!parsed[\"@type\"] && Array.isArray(parsed[\"@graph\"])) {\n            parsed = parsed[\"@graph\"].find(function(it) {\n              return (it[\"@type\"] || \"\").match(\n                this.REGEXPS.jsonLdArticleTypes\n              );\n            });\n          }\n\n          if (\n            !parsed ||\n            !parsed[\"@type\"] ||\n            !parsed[\"@type\"].match(this.REGEXPS.jsonLdArticleTypes)\n          ) {\n            return;\n          }\n\n          metadata = {};\n\n          if (typeof parsed.name === \"string\" && typeof parsed.headline === \"string\" && parsed.name !== parsed.headline) {\n            // we have both name and headline element in the JSON-LD. They should both be the same but some websites like aktualne.cz\n            // put their own name into \"name\" and the article title to \"headline\" which confuses Readability. So we try to check if either\n            // \"name\" or \"headline\" closely matches the html title, and if so, use that one. If not, then we use \"name\" by default.\n\n            var title = this._getArticleTitle();\n            var nameMatches = this._textSimilarity(parsed.name, title) > 0.75;\n            var headlineMatches = this._textSimilarity(parsed.headline, title) > 0.75;\n\n            if (headlineMatches && !nameMatches) {\n              metadata.title = parsed.headline;\n            } else {\n              metadata.title = parsed.name;\n            }\n          } else if (typeof parsed.name === \"string\") {\n            metadata.title = parsed.name.trim();\n          } else if (typeof parsed.headline === \"string\") {\n            metadata.title = parsed.headline.trim();\n          }\n          if (parsed.author) {\n            if (typeof parsed.author.name === \"string\") {\n              metadata.byline = parsed.author.name.trim();\n            } else if (Array.isArray(parsed.author) && parsed.author[0] && typeof parsed.author[0].name === \"string\") {\n              metadata.byline = parsed.author\n                .filter(function(author) {\n                  return author && typeof author.name === \"string\";\n                })\n                .map(function(author) {\n                  return author.name.trim();\n                })\n                .join(\", \");\n            }\n          }\n          if (typeof parsed.description === \"string\") {\n            metadata.excerpt = parsed.description.trim();\n          }\n          if (\n            parsed.publisher &&\n            typeof parsed.publisher.name === \"string\"\n          ) {\n            metadata.siteName = parsed.publisher.name.trim();\n          }\n          if (typeof parsed.datePublished === \"string\") {\n            metadata.datePublished = parsed.datePublished.trim();\n          }\n          return;\n        } catch (err) {\n          this.log(err.message);\n        }\n      }\n    });\n    return metadata ? metadata : {};\n  },\n\n  /**\n   * Attempts to get excerpt and byline metadata for the article.\n   *\n   * @param {Object} jsonld — object containing any metadata that\n   * could be extracted from JSON-LD object.\n   *\n   * @return Object with optional \"excerpt\" and \"byline\" properties\n   */\n  _getArticleMetadata: function(jsonld) {\n    var metadata = {};\n    var values = {};\n    var metaElements = this._doc.getElementsByTagName(\"meta\");\n\n    // property is a space-separated list of values\n    var propertyPattern = /\\s*(article|dc|dcterm|og|twitter)\\s*:\\s*(author|creator|description|published_time|title|site_name)\\s*/gi;\n\n    // name is a single value\n    var namePattern = /^\\s*(?:(dc|dcterm|og|twitter|weibo:(article|webpage))\\s*[\\.:]\\s*)?(author|creator|description|title|site_name)\\s*$/i;\n\n    // Find description tags.\n    this._forEachNode(metaElements, function(element) {\n      var elementName = element.getAttribute(\"name\");\n      var elementProperty = element.getAttribute(\"property\");\n      var content = element.getAttribute(\"content\");\n      if (!content) {\n        return;\n      }\n      var matches = null;\n      var name = null;\n\n      if (elementProperty) {\n        matches = elementProperty.match(propertyPattern);\n        if (matches) {\n          // Convert to lowercase, and remove any whitespace\n          // so we can match below.\n          name = matches[0].toLowerCase().replace(/\\s/g, \"\");\n          // multiple authors\n          values[name] = content.trim();\n        }\n      }\n      if (!matches && elementName && namePattern.test(elementName)) {\n        name = elementName;\n        if (content) {\n          // Convert to lowercase, remove any whitespace, and convert dots\n          // to colons so we can match below.\n          name = name.toLowerCase().replace(/\\s/g, \"\").replace(/\\./g, \":\");\n          values[name] = content.trim();\n        }\n      }\n    });\n\n    // get title\n    metadata.title = jsonld.title ||\n                     values[\"dc:title\"] ||\n                     values[\"dcterm:title\"] ||\n                     values[\"og:title\"] ||\n                     values[\"weibo:article:title\"] ||\n                     values[\"weibo:webpage:title\"] ||\n                     values[\"title\"] ||\n                     values[\"twitter:title\"];\n\n    if (!metadata.title) {\n      metadata.title = this._getArticleTitle();\n    }\n\n    // get author\n    metadata.byline = jsonld.byline ||\n                      values[\"dc:creator\"] ||\n                      values[\"dcterm:creator\"] ||\n                      values[\"author\"];\n\n    // get description\n    metadata.excerpt = jsonld.excerpt ||\n                       values[\"dc:description\"] ||\n                       values[\"dcterm:description\"] ||\n                       values[\"og:description\"] ||\n                       values[\"weibo:article:description\"] ||\n                       values[\"weibo:webpage:description\"] ||\n                       values[\"description\"] ||\n                       values[\"twitter:description\"];\n\n    // get site name\n    metadata.siteName = jsonld.siteName ||\n                        values[\"og:site_name\"];\n\n    // get article published time\n    metadata.publishedTime = jsonld.datePublished ||\n      values[\"article:published_time\"] || null;\n\n    // in many sites the meta value is escaped with HTML entities,\n    // so here we need to unescape it\n    metadata.title = this._unescapeHtmlEntities(metadata.title);\n    metadata.byline = this._unescapeHtmlEntities(metadata.byline);\n    metadata.excerpt = this._unescapeHtmlEntities(metadata.excerpt);\n    metadata.siteName = this._unescapeHtmlEntities(metadata.siteName);\n    metadata.publishedTime = this._unescapeHtmlEntities(metadata.publishedTime);\n\n    return metadata;\n  },\n\n  /**\n   * Check if node is image, or if node contains exactly only one image\n   * whether as a direct child or as its descendants.\n   *\n   * @param Element\n  **/\n  _isSingleImage: function(node) {\n    if (node.tagName === \"IMG\") {\n      return true;\n    }\n\n    if (node.children.length !== 1 || node.textContent.trim() !== \"\") {\n      return false;\n    }\n\n    return this._isSingleImage(node.children[0]);\n  },\n\n  /**\n   * Find all <noscript> that are located after <img> nodes, and which contain only one\n   * <img> element. Replace the first image with the image from inside the <noscript> tag,\n   * and remove the <noscript> tag. This improves the quality of the images we use on\n   * some sites (e.g. Medium).\n   *\n   * @param Element\n  **/\n  _unwrapNoscriptImages: function(doc) {\n    // Find img without source or attributes that might contains image, and remove it.\n    // This is done to prevent a placeholder img is replaced by img from noscript in next step.\n    var imgs = Array.from(doc.getElementsByTagName(\"img\"));\n    this._forEachNode(imgs, function(img) {\n      for (var i = 0; i < img.attributes.length; i++) {\n        var attr = img.attributes[i];\n        switch (attr.name) {\n          case \"src\":\n          case \"srcset\":\n          case \"data-src\":\n          case \"data-srcset\":\n            return;\n        }\n\n        if (/\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n          return;\n        }\n      }\n\n      img.parentNode.removeChild(img);\n    });\n\n    // Next find noscript and try to extract its image\n    var noscripts = Array.from(doc.getElementsByTagName(\"noscript\"));\n    this._forEachNode(noscripts, function(noscript) {\n      // Parse content of noscript and make sure it only contains image\n      var tmp = doc.createElement(\"div\");\n      tmp.innerHTML = noscript.innerHTML;\n      if (!this._isSingleImage(tmp)) {\n        return;\n      }\n\n      // If noscript has previous sibling and it only contains image,\n      // replace it with noscript content. However we also keep old\n      // attributes that might contains image.\n      var prevElement = noscript.previousElementSibling;\n      if (prevElement && this._isSingleImage(prevElement)) {\n        var prevImg = prevElement;\n        if (prevImg.tagName !== \"IMG\") {\n          prevImg = prevElement.getElementsByTagName(\"img\")[0];\n        }\n\n        var newImg = tmp.getElementsByTagName(\"img\")[0];\n        for (var i = 0; i < prevImg.attributes.length; i++) {\n          var attr = prevImg.attributes[i];\n          if (attr.value === \"\") {\n            continue;\n          }\n\n          if (attr.name === \"src\" || attr.name === \"srcset\" || /\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n            if (newImg.getAttribute(attr.name) === attr.value) {\n              continue;\n            }\n\n            var attrName = attr.name;\n            if (newImg.hasAttribute(attrName)) {\n              attrName = \"data-old-\" + attrName;\n            }\n\n            newImg.setAttribute(attrName, attr.value);\n          }\n        }\n\n        noscript.parentNode.replaceChild(tmp.firstElementChild, prevElement);\n      }\n    });\n  },\n\n  /**\n   * Removes script tags from the document.\n   *\n   * @param Element\n  **/\n  _removeScripts: function(doc) {\n    this._removeNodes(this._getAllNodesWithTag(doc, [\"script\", \"noscript\"]));\n  },\n\n  /**\n   * Check if this node has only whitespace and a single element with given tag\n   * Returns false if the DIV node contains non-empty text nodes\n   * or if it contains no element with given tag or more than 1 element.\n   *\n   * @param Element\n   * @param string tag of child element\n  **/\n  _hasSingleTagInsideElement: function(element, tag) {\n    // There should be exactly 1 element child with given tag\n    if (element.children.length != 1 || element.children[0].tagName !== tag) {\n      return false;\n    }\n\n    // And there should be no text nodes with real content\n    return !this._someNode(element.childNodes, function(node) {\n      return node.nodeType === this.TEXT_NODE &&\n             this.REGEXPS.hasContent.test(node.textContent);\n    });\n  },\n\n  _isElementWithoutContent: function(node) {\n    return node.nodeType === this.ELEMENT_NODE &&\n      node.textContent.trim().length == 0 &&\n      (node.children.length == 0 ||\n       node.children.length == node.getElementsByTagName(\"br\").length + node.getElementsByTagName(\"hr\").length);\n  },\n\n  /**\n   * Determine whether element has any children block level elements.\n   *\n   * @param Element\n   */\n  _hasChildBlockElement: function (element) {\n    return this._someNode(element.childNodes, function(node) {\n      return this.DIV_TO_P_ELEMS.has(node.tagName) ||\n             this._hasChildBlockElement(node);\n    });\n  },\n\n  /***\n   * Determine if a node qualifies as phrasing content.\n   * https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/Content_categories#Phrasing_content\n  **/\n  _isPhrasingContent: function(node) {\n    return node.nodeType === this.TEXT_NODE || this.PHRASING_ELEMS.indexOf(node.tagName) !== -1 ||\n      ((node.tagName === \"A\" || node.tagName === \"DEL\" || node.tagName === \"INS\") &&\n        this._everyNode(node.childNodes, this._isPhrasingContent));\n  },\n\n  _isWhitespace: function(node) {\n    return (node.nodeType === this.TEXT_NODE && node.textContent.trim().length === 0) ||\n           (node.nodeType === this.ELEMENT_NODE && node.tagName === \"BR\");\n  },\n\n  /**\n   * Get the inner text of a node - cross browser compatibly.\n   * This also strips out any excess whitespace to be found.\n   *\n   * @param Element\n   * @param Boolean normalizeSpaces (default: true)\n   * @return string\n  **/\n  _getInnerText: function(e, normalizeSpaces) {\n    normalizeSpaces = (typeof normalizeSpaces === \"undefined\") ? true : normalizeSpaces;\n    var textContent = e.textContent.trim();\n\n    if (normalizeSpaces) {\n      return textContent.replace(this.REGEXPS.normalize, \" \");\n    }\n    return textContent;\n  },\n\n  /**\n   * Get the number of times a string s appears in the node e.\n   *\n   * @param Element\n   * @param string - what to split on. Default is \",\"\n   * @return number (integer)\n  **/\n  _getCharCount: function(e, s) {\n    s = s || \",\";\n    return this._getInnerText(e).split(s).length - 1;\n  },\n\n  /**\n   * Remove the style attribute on every e and under.\n   * TODO: Test if getElementsByTagName(*) is faster.\n   *\n   * @param Element\n   * @return void\n  **/\n  _cleanStyles: function(e) {\n    if (!e || e.tagName.toLowerCase() === \"svg\")\n      return;\n\n    // Remove `style` and deprecated presentational attributes\n    for (var i = 0; i < this.PRESENTATIONAL_ATTRIBUTES.length; i++) {\n      e.removeAttribute(this.PRESENTATIONAL_ATTRIBUTES[i]);\n    }\n\n    if (this.DEPRECATED_SIZE_ATTRIBUTE_ELEMS.indexOf(e.tagName) !== -1) {\n      e.removeAttribute(\"width\");\n      e.removeAttribute(\"height\");\n    }\n\n    var cur = e.firstElementChild;\n    while (cur !== null) {\n      this._cleanStyles(cur);\n      cur = cur.nextElementSibling;\n    }\n  },\n\n  /**\n   * Get the density of links as a percentage of the content\n   * This is the amount of text that is inside a link divided by the total text in the node.\n   *\n   * @param Element\n   * @return number (float)\n  **/\n  _getLinkDensity: function(element) {\n    var textLength = this._getInnerText(element).length;\n    if (textLength === 0)\n      return 0;\n\n    var linkLength = 0;\n\n    // XXX implement _reduceNodeList?\n    this._forEachNode(element.getElementsByTagName(\"a\"), function(linkNode) {\n      var href = linkNode.getAttribute(\"href\");\n      var coefficient = href && this.REGEXPS.hashUrl.test(href) ? 0.3 : 1;\n      linkLength += this._getInnerText(linkNode).length * coefficient;\n    });\n\n    return linkLength / textLength;\n  },\n\n  /**\n   * Get an elements class/id weight. Uses regular expressions to tell if this\n   * element looks good or bad.\n   *\n   * @param Element\n   * @return number (Integer)\n  **/\n  _getClassWeight: function(e) {\n    if (!this._flagIsActive(this.FLAG_WEIGHT_CLASSES))\n      return 0;\n\n    var weight = 0;\n\n    // Look for a special classname\n    if (typeof(e.className) === \"string\" && e.className !== \"\") {\n      if (this.REGEXPS.negative.test(e.className))\n        weight -= 25;\n\n      if (this.REGEXPS.positive.test(e.className))\n        weight += 25;\n    }\n\n    // Look for a special ID\n    if (typeof(e.id) === \"string\" && e.id !== \"\") {\n      if (this.REGEXPS.negative.test(e.id))\n        weight -= 25;\n\n      if (this.REGEXPS.positive.test(e.id))\n        weight += 25;\n    }\n\n    return weight;\n  },\n\n  /**\n   * Clean a node of all elements of type \"tag\".\n   * (Unless it's a youtube/vimeo video. People love movies.)\n   *\n   * @param Element\n   * @param string tag to clean\n   * @return void\n   **/\n  _clean: function(e, tag) {\n    var isEmbed = [\"object\", \"embed\", \"iframe\"].indexOf(tag) !== -1;\n\n    this._removeNodes(this._getAllNodesWithTag(e, [tag]), function(element) {\n      // Allow youtube and vimeo videos through as people usually want to see those.\n      if (isEmbed) {\n        // First, check the elements attributes to see if any of them contain youtube or vimeo\n        for (var i = 0; i < element.attributes.length; i++) {\n          if (this._allowedVideoRegex.test(element.attributes[i].value)) {\n            return false;\n          }\n        }\n\n        // For embed with <object> tag, check inner HTML as well.\n        if (element.tagName === \"object\" && this._allowedVideoRegex.test(element.innerHTML)) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  },\n\n  /**\n   * Check if a given node has one of its ancestor tag name matching the\n   * provided one.\n   * @param  HTMLElement node\n   * @param  String      tagName\n   * @param  Number      maxDepth\n   * @param  Function    filterFn a filter to invoke to determine whether this node 'counts'\n   * @return Boolean\n   */\n  _hasAncestorTag: function(node, tagName, maxDepth, filterFn) {\n    maxDepth = maxDepth || 3;\n    tagName = tagName.toUpperCase();\n    var depth = 0;\n    while (node.parentNode) {\n      if (maxDepth > 0 && depth > maxDepth)\n        return false;\n      if (node.parentNode.tagName === tagName && (!filterFn || filterFn(node.parentNode)))\n        return true;\n      node = node.parentNode;\n      depth++;\n    }\n    return false;\n  },\n\n  /**\n   * Return an object indicating how many rows and columns this table has.\n   */\n  _getRowAndColumnCount: function(table) {\n    var rows = 0;\n    var columns = 0;\n    var trs = table.getElementsByTagName(\"tr\");\n    for (var i = 0; i < trs.length; i++) {\n      var rowspan = trs[i].getAttribute(\"rowspan\") || 0;\n      if (rowspan) {\n        rowspan = parseInt(rowspan, 10);\n      }\n      rows += (rowspan || 1);\n\n      // Now look for column-related info\n      var columnsInThisRow = 0;\n      var cells = trs[i].getElementsByTagName(\"td\");\n      for (var j = 0; j < cells.length; j++) {\n        var colspan = cells[j].getAttribute(\"colspan\") || 0;\n        if (colspan) {\n          colspan = parseInt(colspan, 10);\n        }\n        columnsInThisRow += (colspan || 1);\n      }\n      columns = Math.max(columns, columnsInThisRow);\n    }\n    return {rows: rows, columns: columns};\n  },\n\n  /**\n   * Look for 'data' (as opposed to 'layout') tables, for which we use\n   * similar checks as\n   * https://searchfox.org/mozilla-central/rev/f82d5c549f046cb64ce5602bfd894b7ae807c8f8/accessible/generic/TableAccessible.cpp#19\n   */\n  _markDataTables: function(root) {\n    var tables = root.getElementsByTagName(\"table\");\n    for (var i = 0; i < tables.length; i++) {\n      var table = tables[i];\n      var role = table.getAttribute(\"role\");\n      if (role == \"presentation\") {\n        table._readabilityDataTable = false;\n        continue;\n      }\n      var datatable = table.getAttribute(\"datatable\");\n      if (datatable == \"0\") {\n        table._readabilityDataTable = false;\n        continue;\n      }\n      var summary = table.getAttribute(\"summary\");\n      if (summary) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      var caption = table.getElementsByTagName(\"caption\")[0];\n      if (caption && caption.childNodes.length > 0) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      // If the table has a descendant with any of these tags, consider a data table:\n      var dataTableDescendants = [\"col\", \"colgroup\", \"tfoot\", \"thead\", \"th\"];\n      var descendantExists = function(tag) {\n        return !!table.getElementsByTagName(tag)[0];\n      };\n      if (dataTableDescendants.some(descendantExists)) {\n        this.log(\"Data table because found data-y descendant\");\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      // Nested tables indicate a layout table:\n      if (table.getElementsByTagName(\"table\")[0]) {\n        table._readabilityDataTable = false;\n        continue;\n      }\n\n      var sizeInfo = this._getRowAndColumnCount(table);\n      if (sizeInfo.rows >= 10 || sizeInfo.columns > 4) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n      // Now just go by size entirely:\n      table._readabilityDataTable = sizeInfo.rows * sizeInfo.columns > 10;\n    }\n  },\n\n  /* convert images and figures that have properties like data-src into images that can be loaded without JS */\n  _fixLazyImages: function (root) {\n    this._forEachNode(this._getAllNodesWithTag(root, [\"img\", \"picture\", \"figure\"]), function (elem) {\n      // In some sites (e.g. Kotaku), they put 1px square image as base64 data uri in the src attribute.\n      // So, here we check if the data uri is too short, just might as well remove it.\n      if (elem.src && this.REGEXPS.b64DataUrl.test(elem.src)) {\n        // Make sure it's not SVG, because SVG can have a meaningful image in under 133 bytes.\n        var parts = this.REGEXPS.b64DataUrl.exec(elem.src);\n        if (parts[1] === \"image/svg+xml\") {\n          return;\n        }\n\n        // Make sure this element has other attributes which contains image.\n        // If it doesn't, then this src is important and shouldn't be removed.\n        var srcCouldBeRemoved = false;\n        for (var i = 0; i < elem.attributes.length; i++) {\n          var attr = elem.attributes[i];\n          if (attr.name === \"src\") {\n            continue;\n          }\n\n          if (/\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n            srcCouldBeRemoved = true;\n            break;\n          }\n        }\n\n        // Here we assume if image is less than 100 bytes (or 133B after encoded to base64)\n        // it will be too small, therefore it might be placeholder image.\n        if (srcCouldBeRemoved) {\n          var b64starts = elem.src.search(/base64\\s*/i) + 7;\n          var b64length = elem.src.length - b64starts;\n          if (b64length < 133) {\n            elem.removeAttribute(\"src\");\n          }\n        }\n      }\n\n      // also check for \"null\" to work around https://github.com/jsdom/jsdom/issues/2580\n      if ((elem.src || (elem.srcset && elem.srcset != \"null\")) && elem.className.toLowerCase().indexOf(\"lazy\") === -1) {\n        return;\n      }\n\n      for (var j = 0; j < elem.attributes.length; j++) {\n        attr = elem.attributes[j];\n        if (attr.name === \"src\" || attr.name === \"srcset\" || attr.name === \"alt\") {\n          continue;\n        }\n        var copyTo = null;\n        if (/\\.(jpg|jpeg|png|webp)\\s+\\d/.test(attr.value)) {\n          copyTo = \"srcset\";\n        } else if (/^\\s*\\S+\\.(jpg|jpeg|png|webp)\\S*\\s*$/.test(attr.value)) {\n          copyTo = \"src\";\n        }\n        if (copyTo) {\n          //if this is an img or picture, set the attribute directly\n          if (elem.tagName === \"IMG\" || elem.tagName === \"PICTURE\") {\n            elem.setAttribute(copyTo, attr.value);\n          } else if (elem.tagName === \"FIGURE\" && !this._getAllNodesWithTag(elem, [\"img\", \"picture\"]).length) {\n            //if the item is a <figure> that does not contain an image or picture, create one and place it inside the figure\n            //see the nytimes-3 testcase for an example\n            var img = this._doc.createElement(\"img\");\n            img.setAttribute(copyTo, attr.value);\n            elem.appendChild(img);\n          }\n        }\n      }\n    });\n  },\n\n  _getTextDensity: function(e, tags) {\n    var textLength = this._getInnerText(e, true).length;\n    if (textLength === 0) {\n      return 0;\n    }\n    var childrenLength = 0;\n    var children = this._getAllNodesWithTag(e, tags);\n    this._forEachNode(children, (child) => childrenLength += this._getInnerText(child, true).length);\n    return childrenLength / textLength;\n  },\n\n  /**\n   * Clean an element of all tags of type \"tag\" if they look fishy.\n   * \"Fishy\" is an algorithm based on content length, classnames, link density, number of images & embeds, etc.\n   *\n   * @return void\n   **/\n  _cleanConditionally: function(e, tag) {\n    if (!this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY))\n      return;\n\n    // Gather counts for other typical elements embedded within.\n    // Traverse backwards so we can remove nodes at the same time\n    // without effecting the traversal.\n    //\n    // TODO: Consider taking into account original contentScore here.\n    this._removeNodes(this._getAllNodesWithTag(e, [tag]), function(node) {\n      // First check if this node IS data table, in which case don't remove it.\n      var isDataTable = function(t) {\n        return t._readabilityDataTable;\n      };\n\n      var isList = tag === \"ul\" || tag === \"ol\";\n      if (!isList) {\n        var listLength = 0;\n        var listNodes = this._getAllNodesWithTag(node, [\"ul\", \"ol\"]);\n        this._forEachNode(listNodes, (list) => listLength += this._getInnerText(list).length);\n        isList = listLength / this._getInnerText(node).length > 0.9;\n      }\n\n      if (tag === \"table\" && isDataTable(node)) {\n        return false;\n      }\n\n      // Next check if we're inside a data table, in which case don't remove it as well.\n      if (this._hasAncestorTag(node, \"table\", -1, isDataTable)) {\n        return false;\n      }\n\n      if (this._hasAncestorTag(node, \"code\")) {\n        return false;\n      }\n\n      var weight = this._getClassWeight(node);\n\n      this.log(\"Cleaning Conditionally\", node);\n\n      var contentScore = 0;\n\n      if (weight + contentScore < 0) {\n        return true;\n      }\n\n      if (this._getCharCount(node, \",\") < 10) {\n        // If there are not very many commas, and the number of\n        // non-paragraph elements is more than paragraphs or other\n        // ominous signs, remove the element.\n        var p = node.getElementsByTagName(\"p\").length;\n        var img = node.getElementsByTagName(\"img\").length;\n        var li = node.getElementsByTagName(\"li\").length - 100;\n        var input = node.getElementsByTagName(\"input\").length;\n        var headingDensity = this._getTextDensity(node, [\"h1\", \"h2\", \"h3\", \"h4\", \"h5\", \"h6\"]);\n\n        var embedCount = 0;\n        var embeds = this._getAllNodesWithTag(node, [\"object\", \"embed\", \"iframe\"]);\n\n        for (var i = 0; i < embeds.length; i++) {\n          // If this embed has attribute that matches video regex, don't delete it.\n          for (var j = 0; j < embeds[i].attributes.length; j++) {\n            if (this._allowedVideoRegex.test(embeds[i].attributes[j].value)) {\n              return false;\n            }\n          }\n\n          // For embed with <object> tag, check inner HTML as well.\n          if (embeds[i].tagName === \"object\" && this._allowedVideoRegex.test(embeds[i].innerHTML)) {\n            return false;\n          }\n\n          embedCount++;\n        }\n\n        var linkDensity = this._getLinkDensity(node);\n        var contentLength = this._getInnerText(node).length;\n\n        var haveToRemove =\n          (img > 1 && p / img < 0.5 && !this._hasAncestorTag(node, \"figure\")) ||\n          (!isList && li > p) ||\n          (input > Math.floor(p/3)) ||\n          (!isList && headingDensity < 0.9 && contentLength < 25 && (img === 0 || img > 2) && !this._hasAncestorTag(node, \"figure\")) ||\n          (!isList && weight < 25 && linkDensity > 0.2) ||\n          (weight >= 25 && linkDensity > 0.5) ||\n          ((embedCount === 1 && contentLength < 75) || embedCount > 1);\n        // Allow simple lists of images to remain in pages\n        if (isList && haveToRemove) {\n          for (var x = 0; x < node.children.length; x++) {\n            let child = node.children[x];\n            // Don't filter in lists with li's that contain more than one child\n            if (child.children.length > 1) {\n              return haveToRemove;\n            }\n          }\n          let li_count = node.getElementsByTagName(\"li\").length;\n          // Only allow the list to remain if every li contains an image\n          if (img == li_count) {\n            return false;\n          }\n        }\n        return haveToRemove;\n      }\n      return false;\n    });\n  },\n\n  /**\n   * Clean out elements that match the specified conditions\n   *\n   * @param Element\n   * @param Function determines whether a node should be removed\n   * @return void\n   **/\n  _cleanMatchedNodes: function(e, filter) {\n    var endOfSearchMarkerNode = this._getNextNode(e, true);\n    var next = this._getNextNode(e);\n    while (next && next != endOfSearchMarkerNode) {\n      if (filter.call(this, next, next.className + \" \" + next.id)) {\n        next = this._removeAndGetNext(next);\n      } else {\n        next = this._getNextNode(next);\n      }\n    }\n  },\n\n  /**\n   * Clean out spurious headers from an Element.\n   *\n   * @param Element\n   * @return void\n  **/\n  _cleanHeaders: function(e) {\n    let headingNodes = this._getAllNodesWithTag(e, [\"h1\", \"h2\"]);\n    this._removeNodes(headingNodes, function(node) {\n      let shouldRemove = this._getClassWeight(node) < 0;\n      if (shouldRemove) {\n        this.log(\"Removing header with low class weight:\", node);\n      }\n      return shouldRemove;\n    });\n  },\n\n  /**\n   * Check if this node is an H1 or H2 element whose content is mostly\n   * the same as the article title.\n   *\n   * @param Element  the node to check.\n   * @return boolean indicating whether this is a title-like header.\n   */\n  _headerDuplicatesTitle: function(node) {\n    if (node.tagName != \"H1\" && node.tagName != \"H2\") {\n      return false;\n    }\n    var heading = this._getInnerText(node, false);\n    this.log(\"Evaluating similarity of header:\", heading, this._articleTitle);\n    return this._textSimilarity(this._articleTitle, heading) > 0.75;\n  },\n\n  _flagIsActive: function(flag) {\n    return (this._flags & flag) > 0;\n  },\n\n  _removeFlag: function(flag) {\n    this._flags = this._flags & ~flag;\n  },\n\n  _isProbablyVisible: function(node) {\n    // Have to null-check node.style and node.className.indexOf to deal with SVG and MathML nodes.\n    return (!node.style || node.style.display != \"none\")\n      && (!node.style || node.style.visibility != \"hidden\")\n      && !node.hasAttribute(\"hidden\")\n      //check for \"fallback-image\" so that wikimedia math images are displayed\n      && (!node.hasAttribute(\"aria-hidden\") || node.getAttribute(\"aria-hidden\") != \"true\" || (node.className && node.className.indexOf && node.className.indexOf(\"fallback-image\") !== -1));\n  },\n\n  /**\n   * Runs readability.\n   *\n   * Workflow:\n   *  1. Prep the document by removing script tags, css, etc.\n   *  2. Build readability's DOM tree.\n   *  3. Grab the article content from the current dom tree.\n   *  4. Replace the current DOM tree with the new one.\n   *  5. Read peacefully.\n   *\n   * @return void\n   **/\n  parse: function () {\n    // Avoid parsing too large documents, as per configuration option\n    if (this._maxElemsToParse > 0) {\n      var numTags = this._doc.getElementsByTagName(\"*\").length;\n      if (numTags > this._maxElemsToParse) {\n        throw new Error(\"Aborting parsing document; \" + numTags + \" elements found\");\n      }\n    }\n\n    // Unwrap image from noscript\n    this._unwrapNoscriptImages(this._doc);\n\n    // Extract JSON-LD metadata before removing scripts\n    var jsonLd = this._disableJSONLD ? {} : this._getJSONLD(this._doc);\n\n    // Remove script tags from the document.\n    this._removeScripts(this._doc);\n\n    this._prepDocument();\n\n    var metadata = this._getArticleMetadata(jsonLd);\n    this._articleTitle = metadata.title;\n\n    var articleContent = this._grabArticle();\n    if (!articleContent)\n      return null;\n\n    this.log(\"Grabbed: \" + articleContent.innerHTML);\n\n    this._postProcessContent(articleContent);\n\n    // If we haven't found an excerpt in the article's metadata, use the article's\n    // first paragraph as the excerpt. This is used for displaying a preview of\n    // the article's content.\n    if (!metadata.excerpt) {\n      var paragraphs = articleContent.getElementsByTagName(\"p\");\n      if (paragraphs.length > 0) {\n        metadata.excerpt = paragraphs[0].textContent.trim();\n      }\n    }\n\n    var textContent = articleContent.textContent;\n    return {\n      title: this._articleTitle,\n      byline: metadata.byline || this._articleByline,\n      dir: this._articleDir,\n      lang: this._articleLang,\n      content: this._serializer(articleContent),\n      textContent: textContent,\n      length: textContent.length,\n      excerpt: metadata.excerpt,\n      siteName: metadata.siteName || this._articleSiteName,\n      publishedTime: metadata.publishedTime\n    };\n  }\n};\n\nif (typeof module === \"object\") {\n  /* global module */\n  module.exports = Readability;\n}\n", "/*\n * Copyright (c) 2010 Arc90 Inc\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This code is heavily based on Arc90's readability.js (1.7.1) script\n * available at: http://code.google.com/p/arc90labs-readability\n */\n\nvar REGEXPS = {\n  // NOTE: These two regular expressions are duplicated in\n  // Readability.js. Please keep both copies in sync.\n  unlikelyCandidates: /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,\n  okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,\n};\n\nfunction isNodeVisible(node) {\n  // Have to null-check node.style and node.className.indexOf to deal with SVG and MathML nodes.\n  return (!node.style || node.style.display != \"none\")\n    && !node.hasAttribute(\"hidden\")\n    //check for \"fallback-image\" so that wikimedia math images are displayed\n    && (!node.hasAttribute(\"aria-hidden\") || node.getAttribute(\"aria-hidden\") != \"true\" || (node.className && node.className.indexOf && node.className.indexOf(\"fallback-image\") !== -1));\n}\n\n/**\n * Decides whether or not the document is reader-able without parsing the whole thing.\n * @param {Object} options Configuration object.\n * @param {number} [options.minContentLength=140] The minimum node content length used to decide if the document is readerable.\n * @param {number} [options.minScore=20] The minumum cumulated 'score' used to determine if the document is readerable.\n * @param {Function} [options.visibilityChecker=isNodeVisible] The function used to determine if a node is visible.\n * @return {boolean} Whether or not we suspect Readability.parse() will suceeed at returning an article object.\n */\nfunction isProbablyReaderable(doc, options = {}) {\n  // For backward compatibility reasons 'options' can either be a configuration object or the function used\n  // to determine if a node is visible.\n  if (typeof options == \"function\") {\n    options = { visibilityChecker: options };\n  }\n\n  var defaultOptions = { minScore: 20, minContentLength: 140, visibilityChecker: isNodeVisible };\n  options = Object.assign(defaultOptions, options);\n\n  var nodes = doc.querySelectorAll(\"p, pre, article\");\n\n  // Get <div> nodes which have <br> node(s) and append them into the `nodes` variable.\n  // Some articles' DOM structures might look like\n  // <div>\n  //   Sentences<br>\n  //   <br>\n  //   Sentences<br>\n  // </div>\n  var brNodes = doc.querySelectorAll(\"div > br\");\n  if (brNodes.length) {\n    var set = new Set(nodes);\n    [].forEach.call(brNodes, function (node) {\n      set.add(node.parentNode);\n    });\n    nodes = Array.from(set);\n  }\n\n  var score = 0;\n  // This is a little cheeky, we use the accumulator 'score' to decide what to return from\n  // this callback:\n  return [].some.call(nodes, function (node) {\n    if (!options.visibilityChecker(node)) {\n      return false;\n    }\n\n    var matchString = node.className + \" \" + node.id;\n    if (REGEXPS.unlikelyCandidates.test(matchString) &&\n        !REGEXPS.okMaybeItsACandidate.test(matchString)) {\n      return false;\n    }\n\n    if (node.matches(\"li p\")) {\n      return false;\n    }\n\n    var textContentLength = node.textContent.trim().length;\n    if (textContentLength < options.minContentLength) {\n      return false;\n    }\n\n    score += Math.sqrt(textContentLength - options.minContentLength);\n\n    if (score > options.minScore) {\n      return true;\n    }\n    return false;\n  });\n}\n\nif (typeof module === \"object\") {\n  /* global module */\n  module.exports = isProbablyReaderable;\n}\n", "/* eslint-env node */\nvar Readability = require(\"./Readability\");\nvar isProbablyReaderable = require(\"./Readability-readerable\");\n\nmodule.exports = {\n  Readability: Readability,\n  isProbablyReaderable: isProbablyReaderable\n};\n"], "names": ["Readability", "doc", "options", "documentElement", "arguments", "Error", "_doc", "_doc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "__<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>__", "_articleTitle", "_article<PERSON><PERSON><PERSON>", "_articleDir", "_articleSiteName", "_attempts", "_debug", "debug", "_maxElemsToParse", "maxElemsToParse", "DEFAULT_MAX_ELEMS_TO_PARSE", "_nbTopCandidates", "nbTopCandidates", "DEFAULT_N_TOP_CANDIDATES", "_char<PERSON><PERSON><PERSON><PERSON>", "char<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_CHAR_THRESHOLD", "_classesToPreserve", "CLASSES_TO_PRESERVE", "concat", "classesToPreserve", "_keepClasses", "keepClasses", "_serializer", "serializer", "el", "innerHTML", "_disableJSONLD", "disableJSONLD", "_allowedVideoRegex", "allowedVideoRegex", "REGEXPS", "videos", "_flags", "FLAG_STRIP_UNLIKELYS", "FLAG_WEIGHT_CLASSES", "FLAG_CLEAN_CONDITIONALLY", "logNode", "node", "nodeType", "TEXT_NODE", "nodeName", "textContent", "attrPairs", "Array", "from", "attributes", "attr", "name", "value", "join", "localName", "log", "_this", "console", "args", "arg", "ELEMENT_NODE", "unshift", "apply", "dump", "msg", "prototype", "map", "call", "x", "DEFAULT_TAGS_TO_SCORE", "toUpperCase", "split", "unlikelyCandidates", "okMaybeItsACandidate", "positive", "negative", "extraneous", "byline", "replaceFonts", "normalize", "shareElements", "nextLink", "prevLink", "tokenize", "whitespace", "<PERSON><PERSON><PERSON><PERSON>", "hashUrl", "srcsetUrl", "b64DataUrl", "commas", "jsonLdArticleTypes", "UNLIKELY_ROLES", "DIV_TO_P_ELEMS", "Set", "ALTER_TO_DIV_EXCEPTIONS", "PRESENTATIONAL_ATTRIBUTES", "DEPRECATED_SIZE_ATTRIBUTE_ELEMS", "PHRASING_ELEMS", "HTML_ESCAPE_MAP", "_postProcessContent", "articleContent", "_fixRelativeUris", "_simplifyNestedElements", "_cleanClasses", "_removeNodes", "nodeList", "filterFn", "_isLiveNodeList", "i", "length", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_replaceNodeTags", "newTagName", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "_setNodeTag", "err", "e", "f", "_forEachNode", "fn", "for<PERSON>ach", "_findNode", "find", "_someNode", "some", "_everyNode", "every", "_concatNodeLists", "slice", "nodeLists", "list", "_getAllNodesWithTag", "tagNames", "querySelectorAll", "tag", "collection", "getElementsByTagName", "isArray", "className", "getAttribute", "filter", "cls", "indexOf", "setAttribute", "removeAttribute", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "baseURI", "documentURI", "toAbsoluteURI", "uri", "char<PERSON>t", "URL", "href", "ex", "links", "link", "childNodes", "text", "createTextNode", "<PERSON><PERSON><PERSON><PERSON>", "container", "createElement", "append<PERSON><PERSON><PERSON>", "medias", "media", "src", "poster", "srcset", "newSrcset", "replace", "_", "p1", "p2", "p3", "includes", "tagName", "id", "startsWith", "_isElementWithoutContent", "_removeAndGetNext", "_hasSingleTagInsideElement", "child", "children", "_getNextNode", "_getArticleTitle", "curTitle", "origTitle", "title", "trim", "_getInnerText", "titleHadHierarchicalSeparators", "wordCount", "str", "test", "headings", "trimmedTitle", "match", "heading", "substring", "lastIndexOf", "substr", "hOnes", "curT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_prepDocument", "body", "_replaceBrs", "_nextNode", "next", "nextS<PERSON>ling", "elem", "br", "replaced", "<PERSON><PERSON><PERSON><PERSON>", "p", "nextElem", "_isPhrasingContent", "sibling", "<PERSON><PERSON><PERSON><PERSON>", "_isWhitespace", "toLowerCase", "replacement", "ownerDocument", "readability", "_prepArticle", "_cleanStyles", "_markDataTables", "_fixLazyImages", "_cleanConditionally", "_clean", "shareElementThreshold", "topCandidate", "_cleanMatchedNodes", "matchString", "_cleanHeaders", "paragraph", "imgCount", "embedCount", "objectCount", "iframeCount", "totalCount", "table", "tbody", "row", "cell", "_initializeNode", "contentScore", "_getClassWeight", "nextNode", "ignoreSelfAndKids", "_textSimilarity", "textA", "textB", "tokensA", "Boolean", "tokensB", "uniqTokensB", "token", "distanceB", "_check<PERSON><PERSON>ine", "undefined", "rel", "itemprop", "_isValidByline", "_getNodeAncestors", "max<PERSON><PERSON><PERSON>", "ancestors", "push", "_grabArticle", "page", "isPaging", "pageCacheHtml", "stripUnlikelyCandidates", "_flagIsActive", "elementsToScore", "shouldRemoveTitleHeader", "_articleLang", "_isProbablyVisible", "_headerDuplicatesTitle", "_hasAncestorTag", "childNode", "_getLinkDensity", "newNode", "_hasChildBlockElement", "candidates", "elementToScore", "innerText", "Math", "min", "floor", "ancestor", "level", "scoreDivider", "topCandidates", "c", "cl", "candidate", "candidateScore", "t", "aTopCandidate", "splice", "pop", "neededToCreateTopCandidate", "parentOfTopCandidate", "alternativeCandidateAncestors", "MINIMUM_TOPCANDIDATES", "listsContainingThisAncestor", "ancestorIndex", "Number", "lastScore", "scoreThreshold", "parentScore", "siblingScoreThreshold", "max", "siblings", "sl", "append", "contentBonus", "linkDensity", "nodeContent", "node<PERSON><PERSON><PERSON>", "search", "div", "parseSuccessful", "textLength", "_removeFlag", "sort", "a", "b", "articleDir", "String", "_unescapeHtmlEntities", "htmlEscapeMap", "hex", "numStr", "num", "parseInt", "fromCharCode", "_getJSONLD", "scripts", "metadata", "jsonLdElement", "content", "parsed", "JSON", "parse", "it", "headline", "nameMatches", "headlineMatches", "author", "description", "excerpt", "publisher", "siteName", "datePublished", "message", "_getArticleMetadata", "j<PERSON>ld", "values", "metaElements", "propertyPattern", "namePattern", "element", "elementName", "elementProperty", "matches", "publishedTime", "_isSingleImage", "_unwrapNoscriptImages", "imgs", "img", "noscripts", "noscript", "tmp", "prevElement", "previousElementSibling", "prevImg", "newImg", "attrName", "hasAttribute", "_removeScripts", "has", "normalizeSpaces", "_getCharCount", "cur", "linkLength", "linkNode", "coefficient", "weight", "isEmbed", "depth", "_getRowAndColumnCount", "rows", "columns", "trs", "rowspan", "columnsInThisRow", "cells", "j", "colspan", "root", "tables", "role", "_readabilityDataTable", "datatable", "summary", "caption", "dataTableDescendants", "descendantExists", "sizeInfo", "parts", "exec", "srcCouldBeRemoved", "b64starts", "b64length", "copyTo", "_getTextDensity", "tags", "_this2", "<PERSON><PERSON><PERSON><PERSON>", "_this3", "isDataTable", "isList", "listLength", "listNodes", "li", "input", "headingDensity", "embeds", "contentLength", "haveToRemove", "li_count", "endOfSearchMarkerNode", "headingNodes", "<PERSON><PERSON><PERSON><PERSON>", "flag", "style", "display", "visibility", "numTags", "jsonLd", "paragraphs", "dir", "lang", "module", "isNodeVisible", "isProbablyReaderable", "<PERSON><PERSON><PERSON><PERSON>", "defaultOptions", "minScore", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "nodes", "brNodes", "set", "add", "score", "textContent<PERSON>ength", "sqrt", "require$$0", "require$$1"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,EAAA,SAASA,WAAWA,CAACC,GAAG,EAAEC,OAAO,EAAE;AACnC;AACE,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAACC,eAAe,EAAE;AACtCF,MAAAA,GAAG,GAAGC,OAAO;AACbA,MAAAA,OAAO,GAAGE,SAAS,CAAC,CAAC,CAAC;KACvB,MAAM,IAAI,CAACH,GAAG,IAAI,CAACA,GAAG,CAACE,eAAe,EAAE;AACvC,MAAA,MAAM,IAAIE,KAAK,CAAC,wEAAwE,CAAC;AAC7F;AACEH,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAEvB,IAAI,CAACI,IAAI,GAAGL,GAAG;IACf,IAAI,CAACM,eAAe,GAAG,IAAI,CAACD,IAAI,CAACE,UAAU,CAACC,eAAe;IAC3D,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,SAAS,GAAG,EAAE;;AAErB;AACE,IAAA,IAAI,CAACC,MAAM,GAAG,CAAC,CAACb,OAAO,CAACc,KAAK;IAC7B,IAAI,CAACC,gBAAgB,GAAGf,OAAO,CAACgB,eAAe,IAAI,IAAI,CAACC,0BAA0B;IAClF,IAAI,CAACC,gBAAgB,GAAGlB,OAAO,CAACmB,eAAe,IAAI,IAAI,CAACC,wBAAwB;IAChF,IAAI,CAACC,cAAc,GAAGrB,OAAO,CAACsB,aAAa,IAAI,IAAI,CAACC,sBAAsB;AAC1E,IAAA,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACC,mBAAmB,CAACC,MAAM,CAAC1B,OAAO,CAAC2B,iBAAiB,IAAI,EAAE,CAAC;AAC1F,IAAA,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC5B,OAAO,CAAC6B,WAAW;IACzC,IAAI,CAACC,WAAW,GAAG9B,OAAO,CAAC+B,UAAU,IAAI,UAASC,EAAE,EAAE;MACpD,OAAOA,EAAE,CAACC,SAAS;KACpB;AACD,IAAA,IAAI,CAACC,cAAc,GAAG,CAAC,CAAClC,OAAO,CAACmC,aAAa;IAC7C,IAAI,CAACC,kBAAkB,GAAGpC,OAAO,CAACqC,iBAAiB,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM;;AAE5E;AACE,IAAA,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,oBAAoB,GACzB,IAAI,CAACC,mBAAmB,GACxB,IAAI,CAACC,wBAAwB;;AAG7C;IACE,IAAI,IAAI,CAAC9B,MAAM,EAAE;AACf,MAAA,IAAI+B,OAAO,GAAG,SAAVA,OAAOA,CAAYC,IAAI,EAAE;AAC3B,QAAA,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,SAAS,EAAE;UACnC,OAAArB,EAAAA,CAAAA,MAAA,CAAUmB,IAAI,CAACG,QAAQ,UAAAtB,MAAA,CAAMmB,IAAI,CAACI,WAAW,EAAA,KAAA,CAAA;AACrD;AACM,QAAA,IAAIC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACP,IAAI,CAACQ,UAAU,IAAI,EAAE,EAAE,UAASC,IAAI,EAAE;UAC/D,OAAA5B,EAAAA,CAAAA,MAAA,CAAU4B,IAAI,CAACC,IAAI,SAAA7B,MAAA,CAAK4B,IAAI,CAACE,KAAK,EAAA,IAAA,CAAA;AAC1C,SAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;QACZ,OAAA/B,GAAAA,CAAAA,MAAA,CAAWmB,IAAI,CAACa,SAAS,EAAAhC,GAAAA,CAAAA,CAAAA,MAAA,CAAIwB,SAAS,EAAA,GAAA,CAAA;OACvC;MACD,IAAI,CAACS,GAAG,GAAG,YAAY;AAAA,QAAA,IAAAC,KAAA,GAAA,IAAA;AACrB,QAAA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;UAClC,IAAIC,IAAI,GAAGX,KAAK,CAACC,IAAI,CAAClD,SAAS,EAAE,UAAA6D,GAAG,EAAI;YACtC,IAAIA,GAAG,IAAIA,GAAG,CAACjB,QAAQ,IAAIc,KAAI,CAACI,YAAY,EAAE;cAC5C,OAAOpB,OAAO,CAACmB,GAAG,CAAC;AAC/B;AACU,YAAA,OAAOA,GAAG;AACpB,WAAS,CAAC;AACFD,UAAAA,IAAI,CAACG,OAAO,CAAC,uBAAuB,CAAC;UACrCJ,OAAO,CAACF,GAAG,CAACO,KAAK,CAACL,OAAO,EAAEC,IAAI,CAAC;AACxC,SAAO,MAAM,IAAI,OAAOK,IAAI,KAAK,WAAW,EAAE;AAC9C;AACQ,UAAA,IAAIC,GAAG,GAAGjB,KAAK,CAACkB,SAAS,CAACC,GAAG,CAACC,IAAI,CAACrE,SAAS,EAAE,UAASsE,CAAC,EAAE;YACxD,OAAQA,CAAC,IAAIA,CAAC,CAACxB,QAAQ,GAAIJ,OAAO,CAAC4B,CAAC,CAAC,GAAGA,CAAC;AACnD,WAAS,CAAC,CAACf,IAAI,CAAC,GAAG,CAAC;AACZU,UAAAA,IAAI,CAAC,wBAAwB,GAAGC,GAAG,GAAG,IAAI,CAAC;AACnD;OACK;AACL,KAAG,MAAM;AACL,MAAA,IAAI,CAACT,GAAG,GAAG,YAAY,EAAE;AAC7B;AACA;EAEA7D,WAAW,CAACuE,SAAS,GAAG;AACtB5B,IAAAA,oBAAoB,EAAE,GAAG;AACzBC,IAAAA,mBAAmB,EAAE,GAAG;AACxBC,IAAAA,wBAAwB,EAAE,GAAG;AAE/B;AACEqB,IAAAA,YAAY,EAAE,CAAC;AACfjB,IAAAA,SAAS,EAAE,CAAC;AAEd;AACE9B,IAAAA,0BAA0B,EAAE,CAAC;AAE/B;AACA;AACEG,IAAAA,wBAAwB,EAAE,CAAC;AAE7B;IACEqD,qBAAqB,EAAE,iCAAiC,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC;AAEnF;AACEpD,IAAAA,sBAAsB,EAAE,GAAG;AAE7B;AACA;AACEe,IAAAA,OAAO,EAAE;AACX;AACA;AACIsC,MAAAA,kBAAkB,EAAE,wPAAwP;AAC5QC,MAAAA,oBAAoB,EAAE,8CAA8C;AAEpEC,MAAAA,QAAQ,EAAE,sFAAsF;AAChGC,MAAAA,QAAQ,EAAE,wNAAwN;AAClOC,MAAAA,UAAU,EAAE,qFAAqF;AACjGC,MAAAA,MAAM,EAAE,4CAA4C;AACpDC,MAAAA,YAAY,EAAE,oBAAoB;AAClCC,MAAAA,SAAS,EAAE,SAAS;AACpB5C,MAAAA,MAAM,EAAE,oIAAoI;AAC5I6C,MAAAA,aAAa,EAAE,iCAAiC;AAChDC,MAAAA,QAAQ,EAAE,+CAA+C;AACzDC,MAAAA,QAAQ,EAAE,0BAA0B;AACpCC,MAAAA,QAAQ,EAAE,MAAM;AAChBC,MAAAA,UAAU,EAAE,OAAO;AACnBC,MAAAA,UAAU,EAAE,KAAK;AACjBC,MAAAA,OAAO,EAAE,MAAM;AACfC,MAAAA,SAAS,EAAE,oCAAoC;AAC/CC,MAAAA,UAAU,EAAE,uCAAuC;AACvD;AACA;AACIC,MAAAA,MAAM,EAAE,iEAAiE;AAC7E;AACIC,MAAAA,kBAAkB,EAAE;KACrB;AAEDC,IAAAA,cAAc,EAAE,CAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAE;IAEtGC,cAAc,EAAE,IAAIC,GAAG,CAAC,CAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAE,CAAC;IAE9FC,uBAAuB,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,CAAC;IAE3DC,yBAAyB,EAAE,CAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAE;IAEhKC,+BAA+B,EAAE,CAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAE;AAEvE;AACA;AACEC,IAAAA,cAAc,EAAE;AAClB;AACI,IAAA,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EACnE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EACrE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,EACxE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EACpE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,KAAA,CACnC;AAEH;IACE5E,mBAAmB,EAAE,CAAE,MAAM,CAAE;AAEjC;AACE6E,IAAAA,eAAe,EAAE;AACf,MAAA,IAAI,EAAE,GAAG;AACT,MAAA,IAAI,EAAE,GAAG;AACT,MAAA,KAAK,EAAE,GAAG;AACV,MAAA,MAAM,EAAE,GAAG;AACX,MAAA,MAAM,EAAE;KACT;AAEH;AACA;AACA;AACA;AACA;AACA;AACEC,IAAAA,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAWC,cAAc,EAAE;AAChD;AACI,MAAA,IAAI,CAACC,gBAAgB,CAACD,cAAc,CAAC;AAErC,MAAA,IAAI,CAACE,uBAAuB,CAACF,cAAc,CAAC;AAE5C,MAAA,IAAI,CAAC,IAAI,CAAC5E,YAAY,EAAE;AAC5B;AACM,QAAA,IAAI,CAAC+E,aAAa,CAACH,cAAc,CAAC;AACxC;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEI,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAWC,QAAQ,EAAEC,QAAQ,EAAE;AAC7C;AACI,MAAA,IAAI,IAAI,CAACzG,eAAe,IAAIwG,QAAQ,CAACE,eAAe,EAAE;AACpD,QAAA,MAAM,IAAI5G,KAAK,CAAC,6CAA6C,CAAC;AACpE;AACI,MAAA,KAAK,IAAI6G,CAAC,GAAGH,QAAQ,CAACI,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;AAC7C,QAAA,IAAInE,IAAI,GAAGgE,QAAQ,CAACG,CAAC,CAAC;AACtB,QAAA,IAAIE,UAAU,GAAGrE,IAAI,CAACqE,UAAU;AAChC,QAAA,IAAIA,UAAU,EAAE;AACd,UAAA,IAAI,CAACJ,QAAQ,IAAIA,QAAQ,CAACvC,IAAI,CAAC,IAAI,EAAE1B,IAAI,EAAEmE,CAAC,EAAEH,QAAQ,CAAC,EAAE;AACvDK,YAAAA,UAAU,CAACC,WAAW,CAACtE,IAAI,CAAC;AACtC;AACA;AACA;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEuE,IAAAA,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAWP,QAAQ,EAAEQ,UAAU,EAAE;AACnD;AACI,MAAA,IAAI,IAAI,CAAChH,eAAe,IAAIwG,QAAQ,CAACE,eAAe,EAAE;AACpD,QAAA,MAAM,IAAI5G,KAAK,CAAC,iDAAiD,CAAC;AACxE;AAAA,MAAA,IAAAmH,SAAA,GAAAC,0BAAA,CACuBV,QAAQ,CAAA;QAAAW,KAAA;AAAA,MAAA,IAAA;QAA3B,KAAAF,SAAA,CAAAG,CAAA,EAAAD,EAAAA,CAAAA,CAAAA,KAAA,GAAAF,SAAA,CAAAI,CAAA,EAAAC,EAAAA,IAAA,GAA6B;AAAA,UAAA,IAAlB9E,IAAI,GAAA2E,KAAA,CAAAhE,KAAA;AACb,UAAA,IAAI,CAACoE,WAAW,CAAC/E,IAAI,EAAEwE,UAAU,CAAC;AACxC;AAAA,OAAA,CAAA,OAAAQ,GAAA,EAAA;QAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA,CAAA;AAAA,OAAA,SAAA;AAAAP,QAAAA,SAAA,CAAAS,CAAA,EAAA;AAAA;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEC,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAWnB,QAAQ,EAAEoB,EAAE,EAAE;AACnC9E,MAAAA,KAAK,CAACkB,SAAS,CAAC6D,OAAO,CAAC3D,IAAI,CAACsC,QAAQ,EAAEoB,EAAE,EAAE,IAAI,CAAC;KACjD;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEE,IAAAA,SAAS,EAAE,SAAXA,SAASA,CAAWtB,QAAQ,EAAEoB,EAAE,EAAE;AAChC,MAAA,OAAO9E,KAAK,CAACkB,SAAS,CAAC+D,IAAI,CAAC7D,IAAI,CAACsC,QAAQ,EAAEoB,EAAE,EAAE,IAAI,CAAC;KACrD;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEI,IAAAA,SAAS,EAAE,SAAXA,SAASA,CAAWxB,QAAQ,EAAEoB,EAAE,EAAE;AAChC,MAAA,OAAO9E,KAAK,CAACkB,SAAS,CAACiE,IAAI,CAAC/D,IAAI,CAACsC,QAAQ,EAAEoB,EAAE,EAAE,IAAI,CAAC;KACrD;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEM,IAAAA,UAAU,EAAE,SAAZA,UAAUA,CAAW1B,QAAQ,EAAEoB,EAAE,EAAE;AACjC,MAAA,OAAO9E,KAAK,CAACkB,SAAS,CAACmE,KAAK,CAACjE,IAAI,CAACsC,QAAQ,EAAEoB,EAAE,EAAE,IAAI,CAAC;KACtD;AAEH;AACA;AACA;AACA;AACA;AACA;AACEQ,IAAAA,gBAAgB,EAAE,SAAlBA,gBAAgBA,GAAa;AAC3B,MAAA,IAAIC,KAAK,GAAGvF,KAAK,CAACkB,SAAS,CAACqE,KAAK;AACjC,MAAA,IAAI5E,IAAI,GAAG4E,KAAK,CAACnE,IAAI,CAACrE,SAAS,CAAC;MAChC,IAAIyI,SAAS,GAAG7E,IAAI,CAACQ,GAAG,CAAC,UAASsE,IAAI,EAAE;AACtC,QAAA,OAAOF,KAAK,CAACnE,IAAI,CAACqE,IAAI,CAAC;AAC7B,OAAK,CAAC;MACF,OAAOzF,KAAK,CAACkB,SAAS,CAAC3C,MAAM,CAACwC,KAAK,CAAC,EAAE,EAAEyE,SAAS,CAAC;KACnD;AAEDE,IAAAA,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAWhG,IAAI,EAAEiG,QAAQ,EAAE;MAC5C,IAAIjG,IAAI,CAACkG,gBAAgB,EAAE;QACzB,OAAOlG,IAAI,CAACkG,gBAAgB,CAACD,QAAQ,CAACrF,IAAI,CAAC,GAAG,CAAC,CAAC;AACtD;AACI,MAAA,OAAO,EAAE,CAAC/B,MAAM,CAACwC,KAAK,CAAC,EAAE,EAAE4E,QAAQ,CAACxE,GAAG,CAAC,UAAS0E,GAAG,EAAE;AACpD,QAAA,IAAIC,UAAU,GAAGpG,IAAI,CAACqG,oBAAoB,CAACF,GAAG,CAAC;AAC/C,QAAA,OAAO7F,KAAK,CAACgG,OAAO,CAACF,UAAU,CAAC,GAAGA,UAAU,GAAG9F,KAAK,CAACC,IAAI,CAAC6F,UAAU,CAAC;AAC5E,OAAK,CAAC,CAAC;KACJ;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEtC,IAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAW9D,IAAI,EAAE;AAC5B,MAAA,IAAIlB,iBAAiB,GAAG,IAAI,CAACH,kBAAkB;MAC/C,IAAI4H,SAAS,GAAG,CAACvG,IAAI,CAACwG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAC9C1E,KAAK,CAAC,KAAK,CAAA,CACX2E,MAAM,CAAC,UAASC,GAAG,EAAE;QACpB,OAAO5H,iBAAiB,CAAC6H,OAAO,CAACD,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5C,OAAA,CAAA,CACA9F,IAAI,CAAC,GAAG,CAAC;AAEZ,MAAA,IAAI2F,SAAS,EAAE;AACbvG,QAAAA,IAAI,CAAC4G,YAAY,CAAC,OAAO,EAAEL,SAAS,CAAC;AAC3C,OAAK,MAAM;AACLvG,QAAAA,IAAI,CAAC6G,eAAe,CAAC,OAAO,CAAC;AACnC;AAEI,MAAA,KAAK7G,IAAI,GAAGA,IAAI,CAAC8G,iBAAiB,EAAE9G,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAAC+G,kBAAkB,EAAE;AACxE,QAAA,IAAI,CAACjD,aAAa,CAAC9D,IAAI,CAAC;AAC9B;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACE4D,IAAAA,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAWD,cAAc,EAAE;AACzC,MAAA,IAAIqD,OAAO,GAAG,IAAI,CAACzJ,IAAI,CAACyJ,OAAO;AAC/B,MAAA,IAAIC,WAAW,GAAG,IAAI,CAAC1J,IAAI,CAAC0J,WAAW;MACvC,SAASC,aAAaA,CAACC,GAAG,EAAE;AAChC;AACM,QAAA,IAAIH,OAAO,IAAIC,WAAW,IAAIE,GAAG,CAACC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;AAClD,UAAA,OAAOD,GAAG;AAClB;;AAEA;QACM,IAAI;UACF,OAAO,IAAIE,GAAG,CAACF,GAAG,EAAEH,OAAO,CAAC,CAACM,IAAI;SAClC,CAAC,OAAOC,EAAE,EAAE;AACnB;AAAA;AAEM,QAAA,OAAOJ,GAAG;AAChB;MAEI,IAAIK,KAAK,GAAG,IAAI,CAACxB,mBAAmB,CAACrC,cAAc,EAAE,CAAC,GAAG,CAAC,CAAC;AAC3D,MAAA,IAAI,CAACwB,YAAY,CAACqC,KAAK,EAAE,UAASC,IAAI,EAAE;AACtC,QAAA,IAAIH,IAAI,GAAGG,IAAI,CAACjB,YAAY,CAAC,MAAM,CAAC;AACpC,QAAA,IAAIc,IAAI,EAAE;AAChB;AACA;UACQ,IAAIA,IAAI,CAACX,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;AAC/C;YACU,IAAIc,IAAI,CAACC,UAAU,CAACtD,MAAM,KAAK,CAAC,IAAIqD,IAAI,CAACC,UAAU,CAAC,CAAC,CAAC,CAACzH,QAAQ,KAAK,IAAI,CAACC,SAAS,EAAE;cAClF,IAAIyH,IAAI,GAAG,IAAI,CAACpK,IAAI,CAACqK,cAAc,CAACH,IAAI,CAACrH,WAAW,CAAC;cACrDqH,IAAI,CAACpD,UAAU,CAACwD,YAAY,CAACF,IAAI,EAAEF,IAAI,CAAC;AACpD,aAAW,MAAM;AACjB;cACY,IAAIK,SAAS,GAAG,IAAI,CAACvK,IAAI,CAACwK,aAAa,CAAC,MAAM,CAAC;cAC/C,OAAON,IAAI,CAAChK,UAAU,EAAE;AACtBqK,gBAAAA,SAAS,CAACE,WAAW,CAACP,IAAI,CAAChK,UAAU,CAAC;AACpD;cACYgK,IAAI,CAACpD,UAAU,CAACwD,YAAY,CAACC,SAAS,EAAEL,IAAI,CAAC;AACzD;AACA,WAAS,MAAM;YACLA,IAAI,CAACb,YAAY,CAAC,MAAM,EAAEM,aAAa,CAACI,IAAI,CAAC,CAAC;AACxD;AACA;AACA,OAAK,CAAC;MAEF,IAAIW,MAAM,GAAG,IAAI,CAACjC,mBAAmB,CAACrC,cAAc,EAAE,CACpD,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAA,CAC/C,CAAC;AAEF,MAAA,IAAI,CAACwB,YAAY,CAAC8C,MAAM,EAAE,UAASC,KAAK,EAAE;AACxC,QAAA,IAAIC,GAAG,GAAGD,KAAK,CAAC1B,YAAY,CAAC,KAAK,CAAC;AACnC,QAAA,IAAI4B,MAAM,GAAGF,KAAK,CAAC1B,YAAY,CAAC,QAAQ,CAAC;AACzC,QAAA,IAAI6B,MAAM,GAAGH,KAAK,CAAC1B,YAAY,CAAC,QAAQ,CAAC;AAEzC,QAAA,IAAI2B,GAAG,EAAE;UACPD,KAAK,CAACtB,YAAY,CAAC,KAAK,EAAEM,aAAa,CAACiB,GAAG,CAAC,CAAC;AACrD;AAEM,QAAA,IAAIC,MAAM,EAAE;UACVF,KAAK,CAACtB,YAAY,CAAC,QAAQ,EAAEM,aAAa,CAACkB,MAAM,CAAC,CAAC;AAC3D;AAEM,QAAA,IAAIC,MAAM,EAAE;UACV,IAAIC,SAAS,GAAGD,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC9I,OAAO,CAACqD,SAAS,EAAE,UAAS0F,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;YAC7E,OAAOzB,aAAa,CAACuB,EAAE,CAAC,IAAIC,EAAE,IAAI,EAAE,CAAC,GAAGC,EAAE;AACpD,WAAS,CAAC;AAEFT,UAAAA,KAAK,CAACtB,YAAY,CAAC,QAAQ,EAAE0B,SAAS,CAAC;AAC/C;AACA,OAAK,CAAC;KACH;AAEDzE,IAAAA,uBAAuB,EAAE,SAAzBA,uBAAuBA,CAAWF,cAAc,EAAE;MAChD,IAAI3D,IAAI,GAAG2D,cAAc;AAEzB,MAAA,OAAO3D,IAAI,EAAE;AACX,QAAA,IAAIA,IAAI,CAACqE,UAAU,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAACuE,QAAQ,CAAC5I,IAAI,CAAC6I,OAAO,CAAC,IAAI,EAAE7I,IAAI,CAAC8I,EAAE,IAAI9I,IAAI,CAAC8I,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,CAAC,EAAE;AACnH,UAAA,IAAI,IAAI,CAACC,wBAAwB,CAAChJ,IAAI,CAAC,EAAE;AACvCA,YAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,YAAA;AACV,WAAS,MAAM,IAAI,IAAI,CAACkJ,0BAA0B,CAAClJ,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAACkJ,0BAA0B,CAAClJ,IAAI,EAAE,SAAS,CAAC,EAAE;AAC3G,YAAA,IAAImJ,KAAK,GAAGnJ,IAAI,CAACoJ,QAAQ,CAAC,CAAC,CAAC;AAC5B,YAAA,KAAK,IAAIjF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnE,IAAI,CAACQ,UAAU,CAAC4D,MAAM,EAAED,CAAC,EAAE,EAAE;cAC/CgF,KAAK,CAACvC,YAAY,CAAC5G,IAAI,CAACQ,UAAU,CAAC2D,CAAC,CAAC,CAACzD,IAAI,EAAEV,IAAI,CAACQ,UAAU,CAAC2D,CAAC,CAAC,CAACxD,KAAK,CAAC;AACjF;YACUX,IAAI,CAACqE,UAAU,CAACwD,YAAY,CAACsB,KAAK,EAAEnJ,IAAI,CAAC;AACzCA,YAAAA,IAAI,GAAGmJ,KAAK;AACZ,YAAA;AACV;AACA;AAEMnJ,QAAAA,IAAI,GAAG,IAAI,CAACqJ,YAAY,CAACrJ,IAAI,CAAC;AACpC;KACG;AAEH;AACA;AACA;AACA;AACA;AACEsJ,IAAAA,gBAAgB,EAAE,SAAlBA,gBAAgBA,GAAa;AAC3B,MAAA,IAAIpM,GAAG,GAAG,IAAI,CAACK,IAAI;MACnB,IAAIgM,QAAQ,GAAG,EAAE;MACjB,IAAIC,SAAS,GAAG,EAAE;MAElB,IAAI;QACFD,QAAQ,GAAGC,SAAS,GAAGtM,GAAG,CAACuM,KAAK,CAACC,IAAI,EAAE;;AAE7C;QACM,IAAI,OAAOH,QAAQ,KAAK,QAAQ,EAC9BA,QAAQ,GAAGC,SAAS,GAAG,IAAI,CAACG,aAAa,CAACzM,GAAG,CAACmJ,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAClF,OAAA,CAAC,OAAOpB,CAAC,EAAE;MAEZ,IAAI2E,8BAA8B,GAAG,KAAK;MAC1C,SAASC,SAASA,CAACC,GAAG,EAAE;AACtB,QAAA,OAAOA,GAAG,CAAChI,KAAK,CAAC,KAAK,CAAC,CAACsC,MAAM;AACpC;;AAEA;AACI,MAAA,IAAK,gBAAgB,CAAE2F,IAAI,CAACR,QAAQ,CAAC,EAAE;AACrCK,QAAAA,8BAA8B,GAAG,YAAY,CAACG,IAAI,CAACR,QAAQ,CAAC;QAC5DA,QAAQ,GAAGC,SAAS,CAACjB,OAAO,CAAC,uBAAuB,EAAE,IAAI,CAAC;;AAEjE;AACA;AACM,QAAA,IAAIsB,SAAS,CAACN,QAAQ,CAAC,GAAG,CAAC,EACzBA,QAAQ,GAAGC,SAAS,CAACjB,OAAO,CAAC,kCAAkC,EAAE,IAAI,CAAC;OACzE,MAAM,IAAIgB,QAAQ,CAAC5C,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AAC9C;AACA;AACM,QAAA,IAAIqD,QAAQ,GAAG,IAAI,CAACpE,gBAAgB,CAClC1I,GAAG,CAACmJ,oBAAoB,CAAC,IAAI,CAAC,EAC9BnJ,GAAG,CAACmJ,oBAAoB,CAAC,IAAI,CAC9B,CAAA;AACD,QAAA,IAAI4D,YAAY,GAAGV,QAAQ,CAACG,IAAI,EAAE;QAClC,IAAIQ,KAAK,GAAG,IAAI,CAAC1E,SAAS,CAACwE,QAAQ,EAAE,UAASG,OAAO,EAAE;UACrD,OAAOA,OAAO,CAAC/J,WAAW,CAACsJ,IAAI,EAAE,KAAKO,YAAY;AAC1D,SAAO,CAAC;;AAER;QACM,IAAI,CAACC,KAAK,EAAE;AACVX,UAAAA,QAAQ,GAAGC,SAAS,CAACY,SAAS,CAACZ,SAAS,CAACa,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;;AAEtE;AACQ,UAAA,IAAIR,SAAS,CAACN,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC3BA,YAAAA,QAAQ,GAAGC,SAAS,CAACY,SAAS,CAACZ,SAAS,CAAC7C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpE;AACA;WACS,MAAM,IAAIkD,SAAS,CAACL,SAAS,CAACc,MAAM,CAAC,CAAC,EAAEd,SAAS,CAAC7C,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACrE4C,YAAAA,QAAQ,GAAGC,SAAS;AAC9B;AACA;AACA,OAAK,MAAM,IAAID,QAAQ,CAACnF,MAAM,GAAG,GAAG,IAAImF,QAAQ,CAACnF,MAAM,GAAG,EAAE,EAAE;AACxD,QAAA,IAAImG,KAAK,GAAGrN,GAAG,CAACmJ,oBAAoB,CAAC,IAAI,CAAC;AAE1C,QAAA,IAAIkE,KAAK,CAACnG,MAAM,KAAK,CAAC,EACpBmF,QAAQ,GAAG,IAAI,CAACI,aAAa,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/C;AAEIhB,MAAAA,QAAQ,GAAGA,QAAQ,CAACG,IAAI,EAAE,CAACnB,OAAO,CAAC,IAAI,CAAC9I,OAAO,CAAC6C,SAAS,EAAE,GAAG,CAAC;AACnE;AACA;AACA;AACA;AACI,MAAA,IAAIkI,iBAAiB,GAAGX,SAAS,CAACN,QAAQ,CAAC;MAC3C,IAAIiB,iBAAiB,IAAI,CAAC,KACrB,CAACZ,8BAA8B,IAC/BY,iBAAiB,IAAIX,SAAS,CAACL,SAAS,CAACjB,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AACjFgB,QAAAA,QAAQ,GAAGC,SAAS;AAC1B;AAEI,MAAA,OAAOD,QAAQ;KAChB;AAEH;AACA;AACA;AACA;AACA;AACA;AACEkB,IAAAA,aAAa,EAAE,SAAfA,aAAaA,GAAa;AACxB,MAAA,IAAIvN,GAAG,GAAG,IAAI,CAACK,IAAI;;AAEvB;AACI,MAAA,IAAI,CAACwG,YAAY,CAAC,IAAI,CAACiC,mBAAmB,CAAC9I,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;MAE3D,IAAIA,GAAG,CAACwN,IAAI,EAAE;AACZ,QAAA,IAAI,CAACC,WAAW,CAACzN,GAAG,CAACwN,IAAI,CAAC;AAChC;AAEI,MAAA,IAAI,CAACnG,gBAAgB,CAAC,IAAI,CAACyB,mBAAmB,CAAC9I,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC;KACvE;AAEH;AACA;AACA;AACA;AACA;AACE0N,IAAAA,SAAS,EAAE,SAAXA,SAASA,CAAY5K,IAAI,EAAE;MACzB,IAAI6K,IAAI,GAAG7K,IAAI;MACf,OAAO6K,IAAA,IACCA,IAAI,CAAC5K,QAAQ,IAAI,IAAI,CAACkB,YAAY,IACnC,IAAI,CAAC1B,OAAO,CAACkD,UAAU,CAACoH,IAAI,CAACc,IAAI,CAACzK,WAAW,CAAC,EAAE;QACrDyK,IAAI,GAAGA,IAAI,CAACC,WAAW;AAC7B;AACI,MAAA,OAAOD,IAAI;KACZ;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEF,IAAAA,WAAW,EAAE,SAAbA,WAAWA,CAAYI,IAAI,EAAE;AAC3B,MAAA,IAAI,CAAC5F,YAAY,CAAC,IAAI,CAACa,mBAAmB,CAAC+E,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,UAASC,EAAE,EAAE;AACrE,QAAA,IAAIH,IAAI,GAAGG,EAAE,CAACF,WAAW;;AAE/B;AACA;QACM,IAAIG,QAAQ,GAAG,KAAK;;AAE1B;AACA;AACA;AACM,QAAA,OAAO,CAACJ,IAAI,GAAG,IAAI,CAACD,SAAS,CAACC,IAAI,CAAC,KAAMA,IAAI,CAAChC,OAAO,IAAI,IAAK,EAAE;AAC9DoC,UAAAA,QAAQ,GAAG,IAAI;AACf,UAAA,IAAIC,SAAS,GAAGL,IAAI,CAACC,WAAW;AAChCD,UAAAA,IAAI,CAACxG,UAAU,CAACC,WAAW,CAACuG,IAAI,CAAC;AACjCA,UAAAA,IAAI,GAAGK,SAAS;AACxB;;AAEA;AACA;AACA;AACM,QAAA,IAAID,QAAQ,EAAE;UACZ,IAAIE,CAAC,GAAG,IAAI,CAAC5N,IAAI,CAACwK,aAAa,CAAC,GAAG,CAAC;UACpCiD,EAAE,CAAC3G,UAAU,CAACwD,YAAY,CAACsD,CAAC,EAAEH,EAAE,CAAC;UAEjCH,IAAI,GAAGM,CAAC,CAACL,WAAW;AACpB,UAAA,OAAOD,IAAI,EAAE;AACrB;AACU,YAAA,IAAIA,IAAI,CAAChC,OAAO,IAAI,IAAI,EAAE;cACxB,IAAIuC,QAAQ,GAAG,IAAI,CAACR,SAAS,CAACC,IAAI,CAACC,WAAW,CAAC;AAC/C,cAAA,IAAIM,QAAQ,IAAIA,QAAQ,CAACvC,OAAO,IAAI,IAAI,EACtC;AACd;AAEU,YAAA,IAAI,CAAC,IAAI,CAACwC,kBAAkB,CAACR,IAAI,CAAC,EAChC;;AAEZ;AACU,YAAA,IAAIS,OAAO,GAAGT,IAAI,CAACC,WAAW;AAC9BK,YAAAA,CAAC,CAACnD,WAAW,CAAC6C,IAAI,CAAC;AACnBA,YAAAA,IAAI,GAAGS,OAAO;AACxB;AAEQ,UAAA,OAAOH,CAAC,CAACI,SAAS,IAAI,IAAI,CAACC,aAAa,CAACL,CAAC,CAACI,SAAS,CAAC,EAAE;AACrDJ,YAAAA,CAAC,CAAC7G,WAAW,CAAC6G,CAAC,CAACI,SAAS,CAAC;AACpC;AAEQ,UAAA,IAAIJ,CAAC,CAAC9G,UAAU,CAACwE,OAAO,KAAK,GAAG,EAC9B,IAAI,CAAC9D,WAAW,CAACoG,CAAC,CAAC9G,UAAU,EAAE,KAAK,CAAC;AAC/C;AACA,OAAK,CAAC;KACH;AAEDU,IAAAA,WAAW,EAAE,SAAbA,WAAWA,CAAY/E,IAAI,EAAEmG,GAAG,EAAE;MAChC,IAAI,CAACrF,GAAG,CAAC,aAAa,EAAEd,IAAI,EAAEmG,GAAG,CAAC;MAClC,IAAI,IAAI,CAAC3I,eAAe,EAAE;AACxBwC,QAAAA,IAAI,CAACa,SAAS,GAAGsF,GAAG,CAACsF,WAAW,EAAE;AAClCzL,QAAAA,IAAI,CAAC6I,OAAO,GAAG1C,GAAG,CAACtE,WAAW,EAAE;AAChC,QAAA,OAAO7B,IAAI;AACjB;MAEI,IAAI0L,WAAW,GAAG1L,IAAI,CAAC2L,aAAa,CAAC5D,aAAa,CAAC5B,GAAG,CAAC;MACvD,OAAOnG,IAAI,CAACvC,UAAU,EAAE;AACtBiO,QAAAA,WAAW,CAAC1D,WAAW,CAAChI,IAAI,CAACvC,UAAU,CAAC;AAC9C;MACIuC,IAAI,CAACqE,UAAU,CAACwD,YAAY,CAAC6D,WAAW,EAAE1L,IAAI,CAAC;MAC/C,IAAIA,IAAI,CAAC4L,WAAW,EAClBF,WAAW,CAACE,WAAW,GAAG5L,IAAI,CAAC4L,WAAW;AAE5C,MAAA,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnE,IAAI,CAACQ,UAAU,CAAC4D,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAI;UACFuH,WAAW,CAAC9E,YAAY,CAAC5G,IAAI,CAACQ,UAAU,CAAC2D,CAAC,CAAC,CAACzD,IAAI,EAAEV,IAAI,CAACQ,UAAU,CAAC2D,CAAC,CAAC,CAACxD,KAAK,CAAC;SAC5E,CAAC,OAAO4G,EAAE,EAAE;AACnB;AACA;AACA;AACA;AACA;AACA;AALA;AAOA;AACI,MAAA,OAAOmE,WAAW;KACnB;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEG,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAWlI,cAAc,EAAE;AACrC,MAAA,IAAI,CAACmI,YAAY,CAACnI,cAAc,CAAC;;AAErC;AACA;AACA;AACI,MAAA,IAAI,CAACoI,eAAe,CAACpI,cAAc,CAAC;AAEpC,MAAA,IAAI,CAACqI,cAAc,CAACrI,cAAc,CAAC;;AAEvC;AACI,MAAA,IAAI,CAACsI,mBAAmB,CAACtI,cAAc,EAAE,MAAM,CAAC;AAChD,MAAA,IAAI,CAACsI,mBAAmB,CAACtI,cAAc,EAAE,UAAU,CAAC;AACpD,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,QAAQ,CAAC;AACrC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,OAAO,CAAC;AACpC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,QAAQ,CAAC;AACrC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,MAAM,CAAC;AACnC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,OAAO,CAAC;;AAExC;AACA;;AAEI,MAAA,IAAIwI,qBAAqB,GAAG,IAAI,CAACzN,sBAAsB;MAEvD,IAAI,CAACyG,YAAY,CAACxB,cAAc,CAACyF,QAAQ,EAAE,UAAUgD,YAAY,EAAE;QACjE,IAAI,CAACC,kBAAkB,CAACD,YAAY,EAAE,UAAUpM,IAAI,EAAEsM,WAAW,EAAE;AACjE,UAAA,OAAO,IAAI,CAAC7M,OAAO,CAAC8C,aAAa,CAACwH,IAAI,CAACuC,WAAW,CAAC,IAAItM,IAAI,CAACI,WAAW,CAACgE,MAAM,GAAG+H,qBAAqB;AAC9G,SAAO,CAAC;AACR,OAAK,CAAC;AAEF,MAAA,IAAI,CAACD,MAAM,CAACvI,cAAc,EAAE,QAAQ,CAAC;AACrC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,OAAO,CAAC;AACpC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,UAAU,CAAC;AACvC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,QAAQ,CAAC;AACrC,MAAA,IAAI,CAACuI,MAAM,CAACvI,cAAc,EAAE,QAAQ,CAAC;AACrC,MAAA,IAAI,CAAC4I,aAAa,CAAC5I,cAAc,CAAC;;AAEtC;AACA;AACI,MAAA,IAAI,CAACsI,mBAAmB,CAACtI,cAAc,EAAE,OAAO,CAAC;AACjD,MAAA,IAAI,CAACsI,mBAAmB,CAACtI,cAAc,EAAE,IAAI,CAAC;AAC9C,MAAA,IAAI,CAACsI,mBAAmB,CAACtI,cAAc,EAAE,KAAK,CAAC;;AAEnD;AACI,MAAA,IAAI,CAACY,gBAAgB,CAAC,IAAI,CAACyB,mBAAmB,CAACrC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC;;AAEjF;AACI,MAAA,IAAI,CAACI,YAAY,CAAC,IAAI,CAACiC,mBAAmB,CAACrC,cAAc,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU6I,SAAS,EAAE;QACtF,IAAIC,QAAQ,GAAGD,SAAS,CAACnG,oBAAoB,CAAC,KAAK,CAAC,CAACjC,MAAM;QAC3D,IAAIsI,UAAU,GAAGF,SAAS,CAACnG,oBAAoB,CAAC,OAAO,CAAC,CAACjC,MAAM;QAC/D,IAAIuI,WAAW,GAAGH,SAAS,CAACnG,oBAAoB,CAAC,QAAQ,CAAC,CAACjC,MAAM;AACvE;QACM,IAAIwI,WAAW,GAAGJ,SAAS,CAACnG,oBAAoB,CAAC,QAAQ,CAAC,CAACjC,MAAM;QACjE,IAAIyI,UAAU,GAAGJ,QAAQ,GAAGC,UAAU,GAAGC,WAAW,GAAGC,WAAW;AAElE,QAAA,OAAOC,UAAU,KAAK,CAAC,IAAI,CAAC,IAAI,CAAClD,aAAa,CAAC6C,SAAS,EAAE,KAAK,CAAC;AACtE,OAAK,CAAC;AAEF,MAAA,IAAI,CAACrH,YAAY,CAAC,IAAI,CAACa,mBAAmB,CAACrC,cAAc,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,UAASqH,EAAE,EAAE;QAC/E,IAAIH,IAAI,GAAG,IAAI,CAACD,SAAS,CAACI,EAAE,CAACF,WAAW,CAAC;AACzC,QAAA,IAAID,IAAI,IAAIA,IAAI,CAAChC,OAAO,IAAI,GAAG,EAC7BmC,EAAE,CAAC3G,UAAU,CAACC,WAAW,CAAC0G,EAAE,CAAC;AACrC,OAAK,CAAC;;AAEN;AACI,MAAA,IAAI,CAAC7F,YAAY,CAAC,IAAI,CAACa,mBAAmB,CAACrC,cAAc,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,UAASmJ,KAAK,EAAE;AACrF,QAAA,IAAIC,KAAK,GAAG,IAAI,CAAC7D,0BAA0B,CAAC4D,KAAK,EAAE,OAAO,CAAC,GAAGA,KAAK,CAAChG,iBAAiB,GAAGgG,KAAK;QAC7F,IAAI,IAAI,CAAC5D,0BAA0B,CAAC6D,KAAK,EAAE,IAAI,CAAC,EAAE;AAChD,UAAA,IAAIC,GAAG,GAAGD,KAAK,CAACjG,iBAAiB;UACjC,IAAI,IAAI,CAACoC,0BAA0B,CAAC8D,GAAG,EAAE,IAAI,CAAC,EAAE;AAC9C,YAAA,IAAIC,IAAI,GAAGD,GAAG,CAAClG,iBAAiB;YAChCmG,IAAI,GAAG,IAAI,CAAClI,WAAW,CAACkI,IAAI,EAAE,IAAI,CAACvH,UAAU,CAACuH,IAAI,CAACvF,UAAU,EAAE,IAAI,CAAC2D,kBAAkB,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;YACtGyB,KAAK,CAACzI,UAAU,CAACwD,YAAY,CAACoF,IAAI,EAAEH,KAAK,CAAC;AACpD;AACA;AACA,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEI,IAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAWlN,IAAI,EAAE;MAC9BA,IAAI,CAAC4L,WAAW,GAAG;AAAC,QAAA,cAAc,EAAE;OAAE;MAEtC,QAAQ5L,IAAI,CAAC6I,OAAO;AAClB,QAAA,KAAK,KAAK;AACR7I,UAAAA,IAAI,CAAC4L,WAAW,CAACuB,YAAY,IAAI,CAAC;AAClC,UAAA;AAEF,QAAA,KAAK,KAAK;AACV,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,YAAY;AACfnN,UAAAA,IAAI,CAAC4L,WAAW,CAACuB,YAAY,IAAI,CAAC;AAClC,UAAA;AAEF,QAAA,KAAK,SAAS;AACd,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,MAAM;AACTnN,UAAAA,IAAI,CAAC4L,WAAW,CAACuB,YAAY,IAAI,CAAC;AAClC,UAAA;AAEF,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACT,QAAA,KAAK,IAAI;AACPnN,UAAAA,IAAI,CAAC4L,WAAW,CAACuB,YAAY,IAAI,CAAC;AAClC,UAAA;AACR;MAEInN,IAAI,CAAC4L,WAAW,CAACuB,YAAY,IAAI,IAAI,CAACC,eAAe,CAACpN,IAAI,CAAC;KAC5D;AAEDiJ,IAAAA,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAWjJ,IAAI,EAAE;MAChC,IAAIqN,QAAQ,GAAG,IAAI,CAAChE,YAAY,CAACrJ,IAAI,EAAE,IAAI,CAAC;AAC5CA,MAAAA,IAAI,CAACqE,UAAU,CAACC,WAAW,CAACtE,IAAI,CAAC;AACjC,MAAA,OAAOqN,QAAQ;KAChB;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEhE,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAWrJ,IAAI,EAAEsN,iBAAiB,EAAE;AAClD;AACI,MAAA,IAAI,CAACA,iBAAiB,IAAItN,IAAI,CAAC8G,iBAAiB,EAAE;QAChD,OAAO9G,IAAI,CAAC8G,iBAAiB;AACnC;AACA;MACI,IAAI9G,IAAI,CAAC+G,kBAAkB,EAAE;QAC3B,OAAO/G,IAAI,CAAC+G,kBAAkB;AACpC;AACA;AACA;AACA;MACI,GAAG;QACD/G,IAAI,GAAGA,IAAI,CAACqE,UAAU;AAC5B,OAAK,QAAQrE,IAAI,IAAI,CAACA,IAAI,CAAC+G,kBAAkB;AACzC,MAAA,OAAO/G,IAAI,IAAIA,IAAI,CAAC+G,kBAAkB;KACvC;AAEH;AACA;AACA;AACA;AACEwG,IAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAWC,KAAK,EAAEC,KAAK,EAAE;MACtC,IAAIC,OAAO,GAAGF,KAAK,CAAC/B,WAAW,EAAE,CAAC3J,KAAK,CAAC,IAAI,CAACrC,OAAO,CAACiD,QAAQ,CAAC,CAAC+D,MAAM,CAACkH,OAAO,CAAC;MAC9E,IAAIC,OAAO,GAAGH,KAAK,CAAChC,WAAW,EAAE,CAAC3J,KAAK,CAAC,IAAI,CAACrC,OAAO,CAACiD,QAAQ,CAAC,CAAC+D,MAAM,CAACkH,OAAO,CAAC;MAC9E,IAAI,CAACD,OAAO,CAACtJ,MAAM,IAAI,CAACwJ,OAAO,CAACxJ,MAAM,EAAE;AACtC,QAAA,OAAO,CAAC;AACd;AACI,MAAA,IAAIyJ,WAAW,GAAGD,OAAO,CAACnH,MAAM,CAAC,UAAAqH,KAAK,EAAA;AAAA,QAAA,OAAI,CAACJ,OAAO,CAAC9E,QAAQ,CAACkF,KAAK,CAAC;OAAC,CAAA;AACnE,MAAA,IAAIC,SAAS,GAAGF,WAAW,CAACjN,IAAI,CAAC,GAAG,CAAC,CAACwD,MAAM,GAAGwJ,OAAO,CAAChN,IAAI,CAAC,GAAG,CAAC,CAACwD,MAAM;MACvE,OAAO,CAAC,GAAG2J,SAAS;KACrB;AAEDC,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAWhO,IAAI,EAAEsM,WAAW,EAAE;MACxC,IAAI,IAAI,CAAC1O,cAAc,EAAE;AACvB,QAAA,OAAO,KAAK;AAClB;AAEI,MAAA,IAAIoC,IAAI,CAACwG,YAAY,KAAKyH,SAAS,EAAE;AACnC,QAAA,IAAIC,GAAG,GAAGlO,IAAI,CAACwG,YAAY,CAAC,KAAK,CAAC;AAClC,QAAA,IAAI2H,QAAQ,GAAGnO,IAAI,CAACwG,YAAY,CAAC,UAAU,CAAC;AAClD;AAEI,MAAA,IAAI,CAAC0H,GAAG,KAAK,QAAQ,IAAKC,QAAQ,IAAIA,QAAQ,CAACxH,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAE,IAAI,IAAI,CAAClH,OAAO,CAAC2C,MAAM,CAAC2H,IAAI,CAACuC,WAAW,CAAC,KAAK,IAAI,CAAC8B,cAAc,CAACpO,IAAI,CAACI,WAAW,CAAC,EAAE;QAC3J,IAAI,CAACxC,cAAc,GAAGoC,IAAI,CAACI,WAAW,CAACsJ,IAAI,EAAE;AAC7C,QAAA,OAAO,IAAI;AACjB;AAEI,MAAA,OAAO,KAAK;KACb;AAED2E,IAAAA,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAWrO,IAAI,EAAEsO,QAAQ,EAAE;MAC1CA,QAAQ,GAAGA,QAAQ,IAAI,CAAC;MACxB,IAAInK,CAAC,GAAG,CAAC;AAAEoK,QAAAA,SAAS,GAAG,EAAE;MACzB,OAAOvO,IAAI,CAACqE,UAAU,EAAE;AACtBkK,QAAAA,SAAS,CAACC,IAAI,CAACxO,IAAI,CAACqE,UAAU,CAAC;AAC/B,QAAA,IAAIiK,QAAQ,IAAI,EAAEnK,CAAC,KAAKmK,QAAQ,EAC9B;QACFtO,IAAI,GAAGA,IAAI,CAACqE,UAAU;AAC5B;AACI,MAAA,OAAOkK,SAAS;KACjB;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEE,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAYC,IAAI,EAAE;AAC5B,MAAA,IAAI,CAAC5N,GAAG,CAAC,uBAAuB,CAAC;AACjC,MAAA,IAAI5D,GAAG,GAAG,IAAI,CAACK,IAAI;AACnB,MAAA,IAAIoR,QAAQ,GAAGD,IAAI,KAAK,IAAI;MAC5BA,IAAI,GAAGA,IAAI,GAAGA,IAAI,GAAG,IAAI,CAACnR,IAAI,CAACmN,IAAI;;AAEvC;MACI,IAAI,CAACgE,IAAI,EAAE;AACT,QAAA,IAAI,CAAC5N,GAAG,CAAC,mCAAmC,CAAC;AAC7C,QAAA,OAAO,IAAI;AACjB;AAEI,MAAA,IAAI8N,aAAa,GAAGF,IAAI,CAACtP,SAAS;AAElC,MAAA,OAAO,IAAI,EAAE;AACX,QAAA,IAAI,CAAC0B,GAAG,CAAC,2BAA2B,CAAC;QACrC,IAAI+N,uBAAuB,GAAG,IAAI,CAACC,aAAa,CAAC,IAAI,CAAClP,oBAAoB,CAAC;;AAEjF;AACA;AACA;QACM,IAAImP,eAAe,GAAG,EAAE;AACxB,QAAA,IAAI/O,IAAI,GAAG,IAAI,CAACzC,IAAI,CAACH,eAAe;QAEpC,IAAI4R,uBAAuB,GAAG,IAAI;AAElC,QAAA,OAAOhP,IAAI,EAAE;AAEX,UAAA,IAAIA,IAAI,CAAC6I,OAAO,KAAK,MAAM,EAAE;YAC3B,IAAI,CAACoG,YAAY,GAAGjP,IAAI,CAACwG,YAAY,CAAC,MAAM,CAAC;AACvD;UAEQ,IAAI8F,WAAW,GAAGtM,IAAI,CAACuG,SAAS,GAAG,GAAG,GAAGvG,IAAI,CAAC8I,EAAE;AAEhD,UAAA,IAAI,CAAC,IAAI,CAACoG,kBAAkB,CAAClP,IAAI,CAAC,EAAE;AAClC,YAAA,IAAI,CAACc,GAAG,CAAC,yBAAyB,GAAGwL,WAAW,CAAC;AACjDtM,YAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,YAAA;AACV;;AAEA;AACQ,UAAA,IAAIA,IAAI,CAACwG,YAAY,CAAC,YAAY,CAAC,IAAI,MAAM,IAAIxG,IAAI,CAACwG,YAAY,CAAC,MAAM,CAAC,IAAI,QAAQ,EAAE;AACtFxG,YAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,YAAA;AACV;;AAEA;UACQ,IAAI,IAAI,CAACgO,YAAY,CAAChO,IAAI,EAAEsM,WAAW,CAAC,EAAE;AACxCtM,YAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,YAAA;AACV;UAEQ,IAAIgP,uBAAuB,IAAI,IAAI,CAACG,sBAAsB,CAACnP,IAAI,CAAC,EAAE;YAChE,IAAI,CAACc,GAAG,CAAC,mBAAmB,EAAEd,IAAI,CAACI,WAAW,CAACsJ,IAAI,EAAE,EAAE,IAAI,CAAC/L,aAAa,CAAC+L,IAAI,EAAE,CAAC;AACjFsF,YAAAA,uBAAuB,GAAG,KAAK;AAC/BhP,YAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,YAAA;AACV;;AAEA;AACQ,UAAA,IAAI6O,uBAAuB,EAAE;YAC3B,IAAI,IAAI,CAACpP,OAAO,CAACsC,kBAAkB,CAACgI,IAAI,CAACuC,WAAW,CAAC,IACjD,CAAC,IAAI,CAAC7M,OAAO,CAACuC,oBAAoB,CAAC+H,IAAI,CAACuC,WAAW,CAAC,IACpD,CAAC,IAAI,CAAC8C,eAAe,CAACpP,IAAI,EAAE,OAAO,CAAC,IACpC,CAAC,IAAI,CAACoP,eAAe,CAACpP,IAAI,EAAE,MAAM,CAAC,IACnCA,IAAI,CAAC6I,OAAO,KAAK,MAAM,IACvB7I,IAAI,CAAC6I,OAAO,KAAK,GAAG,EAAE;AACxB,cAAA,IAAI,CAAC/H,GAAG,CAAC,gCAAgC,GAAGwL,WAAW,CAAC;AACxDtM,cAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,cAAA;AACZ;AAEU,YAAA,IAAI,IAAI,CAACkD,cAAc,CAAC0F,QAAQ,CAAC5I,IAAI,CAACwG,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE;AAC3D,cAAA,IAAI,CAAC1F,GAAG,CAAC,6BAA6B,GAAGd,IAAI,CAACwG,YAAY,CAAC,MAAM,CAAC,GAAG,KAAK,GAAG8F,WAAW,CAAC;AACzFtM,cAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,cAAA;AACZ;AACA;;AAEA;AACQ,UAAA,IAAI,CAACA,IAAI,CAAC6I,OAAO,KAAK,KAAK,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,SAAS,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,QAAQ,IACjF7I,IAAI,CAAC6I,OAAO,KAAK,IAAI,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,IAAI,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,IAAI,IACvE7I,IAAI,CAAC6I,OAAO,KAAK,IAAI,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,IAAI,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,IAAI,KACxE,IAAI,CAACG,wBAAwB,CAAChJ,IAAI,CAAC,EAAE;AACvCA,YAAAA,IAAI,GAAG,IAAI,CAACiJ,iBAAiB,CAACjJ,IAAI,CAAC;AACnC,YAAA;AACV;AAEQ,UAAA,IAAI,IAAI,CAAC4B,qBAAqB,CAAC+E,OAAO,CAAC3G,IAAI,CAAC6I,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AAC3DkG,YAAAA,eAAe,CAACP,IAAI,CAACxO,IAAI,CAAC;AACpC;;AAEA;AACQ,UAAA,IAAIA,IAAI,CAAC6I,OAAO,KAAK,KAAK,EAAE;AACpC;YACU,IAAIsC,CAAC,GAAG,IAAI;AACZ,YAAA,IAAIkE,SAAS,GAAGrP,IAAI,CAACvC,UAAU;AAC/B,YAAA,OAAO4R,SAAS,EAAE;AAChB,cAAA,IAAIvE,WAAW,GAAGuE,SAAS,CAACvE,WAAW;AACvC,cAAA,IAAI,IAAI,CAACO,kBAAkB,CAACgE,SAAS,CAAC,EAAE;gBACtC,IAAIlE,CAAC,KAAK,IAAI,EAAE;AACdA,kBAAAA,CAAC,CAACnD,WAAW,CAACqH,SAAS,CAAC;iBACzB,MAAM,IAAI,CAAC,IAAI,CAAC7D,aAAa,CAAC6D,SAAS,CAAC,EAAE;AACzClE,kBAAAA,CAAC,GAAGjO,GAAG,CAAC6K,aAAa,CAAC,GAAG,CAAC;AAC1B/H,kBAAAA,IAAI,CAAC6H,YAAY,CAACsD,CAAC,EAAEkE,SAAS,CAAC;AAC/BlE,kBAAAA,CAAC,CAACnD,WAAW,CAACqH,SAAS,CAAC;AACxC;AACA,eAAa,MAAM,IAAIlE,CAAC,KAAK,IAAI,EAAE;AACrB,gBAAA,OAAOA,CAAC,CAACI,SAAS,IAAI,IAAI,CAACC,aAAa,CAACL,CAAC,CAACI,SAAS,CAAC,EAAE;AACrDJ,kBAAAA,CAAC,CAAC7G,WAAW,CAAC6G,CAAC,CAACI,SAAS,CAAC;AAC1C;AACcJ,gBAAAA,CAAC,GAAG,IAAI;AACtB;AACYkE,cAAAA,SAAS,GAAGvE,WAAW;AACnC;;AAEA;AACA;AACA;AACA;AACU,YAAA,IAAI,IAAI,CAAC5B,0BAA0B,CAAClJ,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAACsP,eAAe,CAACtP,IAAI,CAAC,GAAG,IAAI,EAAE;AACnF,cAAA,IAAIuP,OAAO,GAAGvP,IAAI,CAACoJ,QAAQ,CAAC,CAAC,CAAC;cAC9BpJ,IAAI,CAACqE,UAAU,CAACwD,YAAY,CAAC0H,OAAO,EAAEvP,IAAI,CAAC;AAC3CA,cAAAA,IAAI,GAAGuP,OAAO;AACdR,cAAAA,eAAe,CAACP,IAAI,CAACxO,IAAI,CAAC;aAC3B,MAAM,IAAI,CAAC,IAAI,CAACwP,qBAAqB,CAACxP,IAAI,CAAC,EAAE;cAC5CA,IAAI,GAAG,IAAI,CAAC+E,WAAW,CAAC/E,IAAI,EAAE,GAAG,CAAC;AAClC+O,cAAAA,eAAe,CAACP,IAAI,CAACxO,IAAI,CAAC;AACtC;AACA;AACQA,UAAAA,IAAI,GAAG,IAAI,CAACqJ,YAAY,CAACrJ,IAAI,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA;AACA;QACM,IAAIyP,UAAU,GAAG,EAAE;AACnB,QAAA,IAAI,CAACtK,YAAY,CAAC4J,eAAe,EAAE,UAASW,cAAc,EAAE;AAC1D,UAAA,IAAI,CAACA,cAAc,CAACrL,UAAU,IAAI,OAAOqL,cAAc,CAACrL,UAAU,CAACwE,OAAQ,KAAK,WAAW,EACzF;;AAEV;AACQ,UAAA,IAAI8G,SAAS,GAAG,IAAI,CAAChG,aAAa,CAAC+F,cAAc,CAAC;AAClD,UAAA,IAAIC,SAAS,CAACvL,MAAM,GAAG,EAAE,EACvB;;AAEV;UACQ,IAAImK,SAAS,GAAG,IAAI,CAACF,iBAAiB,CAACqB,cAAc,EAAE,CAAC,CAAC;AACzD,UAAA,IAAInB,SAAS,CAACnK,MAAM,KAAK,CAAC,EACxB;UAEF,IAAI+I,YAAY,GAAG,CAAC;;AAE5B;AACQA,UAAAA,YAAY,IAAI,CAAC;;AAEzB;AACQA,UAAAA,YAAY,IAAIwC,SAAS,CAAC7N,KAAK,CAAC,IAAI,CAACrC,OAAO,CAACuD,MAAM,CAAC,CAACoB,MAAM;;AAEnE;AACQ+I,UAAAA,YAAY,IAAIyC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACH,SAAS,CAACvL,MAAM,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;;AAEvE;UACQ,IAAI,CAACe,YAAY,CAACoJ,SAAS,EAAE,UAASwB,QAAQ,EAAEC,KAAK,EAAE;AACrD,YAAA,IAAI,CAACD,QAAQ,CAAClH,OAAO,IAAI,CAACkH,QAAQ,CAAC1L,UAAU,IAAI,OAAO0L,QAAQ,CAAC1L,UAAU,CAACwE,OAAQ,KAAK,WAAW,EAClG;AAEF,YAAA,IAAI,OAAOkH,QAAQ,CAACnE,WAAY,KAAK,WAAW,EAAE;AAChD,cAAA,IAAI,CAACsB,eAAe,CAAC6C,QAAQ,CAAC;AAC9BN,cAAAA,UAAU,CAACjB,IAAI,CAACuB,QAAQ,CAAC;AACrC;;AAEA;AACA;AACA;AACA;YACU,IAAIC,KAAK,KAAK,CAAC,EACb,IAAIC,YAAY,GAAG,CAAC,CAAA,KACjB,IAAID,KAAK,KAAK,CAAC,EAClBC,YAAY,GAAG,CAAC,CAAA,KAEhBA,YAAY,GAAGD,KAAK,GAAG,CAAC;AAC1BD,YAAAA,QAAQ,CAACnE,WAAW,CAACuB,YAAY,IAAIA,YAAY,GAAG8C,YAAY;AAC1E,WAAS,CAAC;AACV,SAAO,CAAC;;AAER;AACA;QACM,IAAIC,aAAa,GAAG,EAAE;AACtB,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,EAAE,GAAGX,UAAU,CAACrL,MAAM,EAAE+L,CAAC,GAAGC,EAAE,EAAED,CAAC,IAAI,CAAC,EAAE;AACtD,UAAA,IAAIE,SAAS,GAAGZ,UAAU,CAACU,CAAC,CAAC;;AAErC;AACA;AACA;AACQ,UAAA,IAAIG,cAAc,GAAGD,SAAS,CAACzE,WAAW,CAACuB,YAAY,IAAI,CAAC,GAAG,IAAI,CAACmC,eAAe,CAACe,SAAS,CAAC,CAAC;AAC/FA,UAAAA,SAAS,CAACzE,WAAW,CAACuB,YAAY,GAAGmD,cAAc;UAEnD,IAAI,CAACxP,GAAG,CAAC,YAAY,EAAEuP,SAAS,EAAE,aAAa,GAAGC,cAAc,CAAC;AAEjE,UAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAClS,gBAAgB,EAAEkS,CAAC,EAAE,EAAE;AAC9C,YAAA,IAAIC,aAAa,GAAGN,aAAa,CAACK,CAAC,CAAC;YAEpC,IAAI,CAACC,aAAa,IAAIF,cAAc,GAAGE,aAAa,CAAC5E,WAAW,CAACuB,YAAY,EAAE;cAC7E+C,aAAa,CAACO,MAAM,CAACF,CAAC,EAAE,CAAC,EAAEF,SAAS,CAAC;AACrC,cAAA,IAAIH,aAAa,CAAC9L,MAAM,GAAG,IAAI,CAAC/F,gBAAgB,EAC9C6R,aAAa,CAACQ,GAAG,EAAE;AACrB,cAAA;AACZ;AACA;AACA;AAEM,QAAA,IAAItE,YAAY,GAAG8D,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI;QAC3C,IAAIS,0BAA0B,GAAG,KAAK;AACtC,QAAA,IAAIC,oBAAoB;;AAE9B;AACA;QACM,IAAIxE,YAAY,KAAK,IAAI,IAAIA,YAAY,CAACvD,OAAO,KAAK,MAAM,EAAE;AACpE;AACQuD,UAAAA,YAAY,GAAGlP,GAAG,CAAC6K,aAAa,CAAC,KAAK,CAAC;AACvC4I,UAAAA,0BAA0B,GAAG,IAAI;AACzC;AACA;UACQ,OAAOjC,IAAI,CAACjR,UAAU,EAAE;YACtB,IAAI,CAACqD,GAAG,CAAC,mBAAmB,EAAE4N,IAAI,CAACjR,UAAU,CAAC;AAC9C2O,YAAAA,YAAY,CAACpE,WAAW,CAAC0G,IAAI,CAACjR,UAAU,CAAC;AACnD;AAEQiR,UAAAA,IAAI,CAAC1G,WAAW,CAACoE,YAAY,CAAC;AAE9B,UAAA,IAAI,CAACc,eAAe,CAACd,YAAY,CAAC;SACnC,MAAM,IAAIA,YAAY,EAAE;AAC/B;AACA;UACQ,IAAIyE,6BAA6B,GAAG,EAAE;AACtC,UAAA,KAAK,IAAI1M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+L,aAAa,CAAC9L,MAAM,EAAED,CAAC,EAAE,EAAE;AAC7C,YAAA,IAAI+L,aAAa,CAAC/L,CAAC,CAAC,CAACyH,WAAW,CAACuB,YAAY,GAAGf,YAAY,CAACR,WAAW,CAACuB,YAAY,IAAI,IAAI,EAAE;AAC7F0D,cAAAA,6BAA6B,CAACrC,IAAI,CAAC,IAAI,CAACH,iBAAiB,CAAC6B,aAAa,CAAC/L,CAAC,CAAC,CAAC,CAAC;AACxF;AACA;UACQ,IAAI2M,qBAAqB,GAAG,CAAC;AAC7B,UAAA,IAAID,6BAA6B,CAACzM,MAAM,IAAI0M,qBAAqB,EAAE;YACjEF,oBAAoB,GAAGxE,YAAY,CAAC/H,UAAU;AAC9C,YAAA,OAAOuM,oBAAoB,CAAC/H,OAAO,KAAK,MAAM,EAAE;cAC9C,IAAIkI,2BAA2B,GAAG,CAAC;AACnC,cAAA,KAAK,IAAIC,aAAa,GAAG,CAAC,EAAEA,aAAa,GAAGH,6BAA6B,CAACzM,MAAM,IAAI2M,2BAA2B,GAAGD,qBAAqB,EAAEE,aAAa,EAAE,EAAE;AACxJD,gBAAAA,2BAA2B,IAAIE,MAAM,CAACJ,6BAA6B,CAACG,aAAa,CAAC,CAACpI,QAAQ,CAACgI,oBAAoB,CAAC,CAAC;AAChI;cACY,IAAIG,2BAA2B,IAAID,qBAAqB,EAAE;AACxD1E,gBAAAA,YAAY,GAAGwE,oBAAoB;AACnC,gBAAA;AACd;cACYA,oBAAoB,GAAGA,oBAAoB,CAACvM,UAAU;AAClE;AACA;AACQ,UAAA,IAAI,CAAC+H,YAAY,CAACR,WAAW,EAAE;AAC7B,YAAA,IAAI,CAACsB,eAAe,CAACd,YAAY,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;UACQwE,oBAAoB,GAAGxE,YAAY,CAAC/H,UAAU;AAC9C,UAAA,IAAI6M,SAAS,GAAG9E,YAAY,CAACR,WAAW,CAACuB,YAAY;AAC7D;AACQ,UAAA,IAAIgE,cAAc,GAAGD,SAAS,GAAG,CAAC;AAClC,UAAA,OAAON,oBAAoB,CAAC/H,OAAO,KAAK,MAAM,EAAE;AAC9C,YAAA,IAAI,CAAC+H,oBAAoB,CAAChF,WAAW,EAAE;cACrCgF,oBAAoB,GAAGA,oBAAoB,CAACvM,UAAU;AACtD,cAAA;AACZ;AACU,YAAA,IAAI+M,WAAW,GAAGR,oBAAoB,CAAChF,WAAW,CAACuB,YAAY;YAC/D,IAAIiE,WAAW,GAAGD,cAAc,EAC9B;YACF,IAAIC,WAAW,GAAGF,SAAS,EAAE;AACvC;AACY9E,cAAAA,YAAY,GAAGwE,oBAAoB;AACnC,cAAA;AACZ;AACUM,YAAAA,SAAS,GAAGN,oBAAoB,CAAChF,WAAW,CAACuB,YAAY;YACzDyD,oBAAoB,GAAGA,oBAAoB,CAACvM,UAAU;AAChE;;AAEA;AACA;UACQuM,oBAAoB,GAAGxE,YAAY,CAAC/H,UAAU;AAC9C,UAAA,OAAOuM,oBAAoB,CAAC/H,OAAO,IAAI,MAAM,IAAI+H,oBAAoB,CAACxH,QAAQ,CAAChF,MAAM,IAAI,CAAC,EAAE;AAC1FgI,YAAAA,YAAY,GAAGwE,oBAAoB;YACnCA,oBAAoB,GAAGxE,YAAY,CAAC/H,UAAU;AACxD;AACQ,UAAA,IAAI,CAAC+H,YAAY,CAACR,WAAW,EAAE;AAC7B,YAAA,IAAI,CAACsB,eAAe,CAACd,YAAY,CAAC;AAC5C;AACA;;AAEA;AACA;AACA;AACM,QAAA,IAAIzI,cAAc,GAAGzG,GAAG,CAAC6K,aAAa,CAAC,KAAK,CAAC;AAC7C,QAAA,IAAI4G,QAAQ,EACVhL,cAAc,CAACmF,EAAE,GAAG,qBAAqB;AAE3C,QAAA,IAAIuI,qBAAqB,GAAGzB,IAAI,CAAC0B,GAAG,CAAC,EAAE,EAAElF,YAAY,CAACR,WAAW,CAACuB,YAAY,GAAG,GAAG,CAAC;AAC3F;QACMyD,oBAAoB,GAAGxE,YAAY,CAAC/H,UAAU;AAC9C,QAAA,IAAIkN,QAAQ,GAAGX,oBAAoB,CAACxH,QAAQ;AAE5C,QAAA,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAE4M,EAAE,GAAGD,QAAQ,CAACnN,MAAM,EAAEQ,CAAC,GAAG4M,EAAE,EAAE5M,CAAC,EAAE,EAAE;AACjD,UAAA,IAAI0G,OAAO,GAAGiG,QAAQ,CAAC3M,CAAC,CAAC;UACzB,IAAI6M,MAAM,GAAG,KAAK;UAElB,IAAI,CAAC3Q,GAAG,CAAC,0BAA0B,EAAEwK,OAAO,EAAEA,OAAO,CAACM,WAAW,GAAI,aAAa,GAAGN,OAAO,CAACM,WAAW,CAACuB,YAAY,GAAI,EAAE,CAAC;AAC5H,UAAA,IAAI,CAACrM,GAAG,CAAC,mBAAmB,EAAEwK,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACM,WAAW,CAACuB,YAAY,GAAG,SAAS,CAAC;UAEjG,IAAI7B,OAAO,KAAKc,YAAY,EAAE;AAC5BqF,YAAAA,MAAM,GAAG,IAAI;AACvB,WAAS,MAAM;YACL,IAAIC,YAAY,GAAG,CAAC;;AAE9B;YACU,IAAIpG,OAAO,CAAC/E,SAAS,KAAK6F,YAAY,CAAC7F,SAAS,IAAI6F,YAAY,CAAC7F,SAAS,KAAK,EAAE,EAC/EmL,YAAY,IAAItF,YAAY,CAACR,WAAW,CAACuB,YAAY,GAAG,GAAG;AAE7D,YAAA,IAAI7B,OAAO,CAACM,WAAW,IACjBN,OAAO,CAACM,WAAW,CAACuB,YAAY,GAAGuE,YAAY,IAAKL,qBAAsB,EAAE;AAChFI,cAAAA,MAAM,GAAG,IAAI;AACzB,aAAW,MAAM,IAAInG,OAAO,CAACnL,QAAQ,KAAK,GAAG,EAAE;AACnC,cAAA,IAAIwR,WAAW,GAAG,IAAI,CAACrC,eAAe,CAAChE,OAAO,CAAC;AAC/C,cAAA,IAAIsG,WAAW,GAAG,IAAI,CAACjI,aAAa,CAAC2B,OAAO,CAAC;AAC7C,cAAA,IAAIuG,UAAU,GAAGD,WAAW,CAACxN,MAAM;AAEnC,cAAA,IAAIyN,UAAU,GAAG,EAAE,IAAIF,WAAW,GAAG,IAAI,EAAE;AACzCF,gBAAAA,MAAM,GAAG,IAAI;eACd,MAAM,IAAII,UAAU,GAAG,EAAE,IAAIA,UAAU,GAAG,CAAC,IAAIF,WAAW,KAAK,CAAC,IACtDC,WAAW,CAACE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;AAC/CL,gBAAAA,MAAM,GAAG,IAAI;AAC3B;AACA;AACA;AAEQ,UAAA,IAAIA,MAAM,EAAE;AACV,YAAA,IAAI,CAAC3Q,GAAG,CAAC,iBAAiB,EAAEwK,OAAO,CAAC;AAEpC,YAAA,IAAI,IAAI,CAACjI,uBAAuB,CAACsD,OAAO,CAAC2E,OAAO,CAACnL,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;AAC7E;AACA;cACY,IAAI,CAACW,GAAG,CAAC,mBAAmB,EAAEwK,OAAO,EAAE,SAAS,CAAC;cAEjDA,OAAO,GAAG,IAAI,CAACvG,WAAW,CAACuG,OAAO,EAAE,KAAK,CAAC;AACtD;AAEU3H,YAAAA,cAAc,CAACqE,WAAW,CAACsD,OAAO,CAAC;AAC7C;AACA;YACUiG,QAAQ,GAAGX,oBAAoB,CAACxH,QAAQ;AAClD;AACA;AACA;AACA;AACUxE,YAAAA,CAAC,IAAI,CAAC;AACN4M,YAAAA,EAAE,IAAI,CAAC;AACjB;AACA;AAEM,QAAA,IAAI,IAAI,CAACxT,MAAM,EACb,IAAI,CAAC8C,GAAG,CAAC,4BAA4B,GAAG6C,cAAc,CAACvE,SAAS,CAAC;AACzE;AACM,QAAA,IAAI,CAACyM,YAAY,CAAClI,cAAc,CAAC;AACjC,QAAA,IAAI,IAAI,CAAC3F,MAAM,EACb,IAAI,CAAC8C,GAAG,CAAC,6BAA6B,GAAG6C,cAAc,CAACvE,SAAS,CAAC;AAEpE,QAAA,IAAIuR,0BAA0B,EAAE;AACtC;AACA;AACA;AACA;UACQvE,YAAY,CAACtD,EAAE,GAAG,oBAAoB;UACtCsD,YAAY,CAAC7F,SAAS,GAAG,MAAM;AACvC,SAAO,MAAM;AACL,UAAA,IAAIwL,GAAG,GAAG7U,GAAG,CAAC6K,aAAa,CAAC,KAAK,CAAC;UAClCgK,GAAG,CAACjJ,EAAE,GAAG,oBAAoB;UAC7BiJ,GAAG,CAACxL,SAAS,GAAG,MAAM;UACtB,OAAO5C,cAAc,CAAClG,UAAU,EAAE;AAChCsU,YAAAA,GAAG,CAAC/J,WAAW,CAACrE,cAAc,CAAClG,UAAU,CAAC;AACpD;AACQkG,UAAAA,cAAc,CAACqE,WAAW,CAAC+J,GAAG,CAAC;AACvC;AAEM,QAAA,IAAI,IAAI,CAAC/T,MAAM,EACb,IAAI,CAAC8C,GAAG,CAAC,gCAAgC,GAAG6C,cAAc,CAACvE,SAAS,CAAC;QAEvE,IAAI4S,eAAe,GAAG,IAAI;;AAEhC;AACA;AACA;AACA;AACA;QACM,IAAIC,UAAU,GAAG,IAAI,CAACtI,aAAa,CAAChG,cAAc,EAAE,IAAI,CAAC,CAACS,MAAM;AAChE,QAAA,IAAI6N,UAAU,GAAG,IAAI,CAACzT,cAAc,EAAE;AACpCwT,UAAAA,eAAe,GAAG,KAAK;UACvBtD,IAAI,CAACtP,SAAS,GAAGwP,aAAa;UAE9B,IAAI,IAAI,CAACE,aAAa,CAAC,IAAI,CAAClP,oBAAoB,CAAC,EAAE;AACjD,YAAA,IAAI,CAACsS,WAAW,CAAC,IAAI,CAACtS,oBAAoB,CAAC;AAC3C,YAAA,IAAI,CAAC7B,SAAS,CAACyQ,IAAI,CAAC;AAAC7K,cAAAA,cAAc,EAAEA,cAAc;AAAEsO,cAAAA,UAAU,EAAEA;AAAU,aAAC,CAAC;WAC9E,MAAM,IAAI,IAAI,CAACnD,aAAa,CAAC,IAAI,CAACjP,mBAAmB,CAAC,EAAE;AACvD,YAAA,IAAI,CAACqS,WAAW,CAAC,IAAI,CAACrS,mBAAmB,CAAC;AAC1C,YAAA,IAAI,CAAC9B,SAAS,CAACyQ,IAAI,CAAC;AAAC7K,cAAAA,cAAc,EAAEA,cAAc;AAAEsO,cAAAA,UAAU,EAAEA;AAAU,aAAC,CAAC;WAC9E,MAAM,IAAI,IAAI,CAACnD,aAAa,CAAC,IAAI,CAAChP,wBAAwB,CAAC,EAAE;AAC5D,YAAA,IAAI,CAACoS,WAAW,CAAC,IAAI,CAACpS,wBAAwB,CAAC;AAC/C,YAAA,IAAI,CAAC/B,SAAS,CAACyQ,IAAI,CAAC;AAAC7K,cAAAA,cAAc,EAAEA,cAAc;AAAEsO,cAAAA,UAAU,EAAEA;AAAU,aAAC,CAAC;AACvF,WAAS,MAAM;AACL,YAAA,IAAI,CAAClU,SAAS,CAACyQ,IAAI,CAAC;AAAC7K,cAAAA,cAAc,EAAEA,cAAc;AAAEsO,cAAAA,UAAU,EAAEA;AAAU,aAAC,CAAC;AACvF;YACU,IAAI,CAAClU,SAAS,CAACoU,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;AAClC,cAAA,OAAOA,CAAC,CAACJ,UAAU,GAAGG,CAAC,CAACH,UAAU;AAC9C,aAAW,CAAC;;AAEZ;YACU,IAAI,CAAC,IAAI,CAAClU,SAAS,CAAC,CAAC,CAAC,CAACkU,UAAU,EAAE;AACjC,cAAA,OAAO,IAAI;AACvB;YAEUtO,cAAc,GAAG,IAAI,CAAC5F,SAAS,CAAC,CAAC,CAAC,CAAC4F,cAAc;AACjDqO,YAAAA,eAAe,GAAG,IAAI;AAChC;AACA;AAEM,QAAA,IAAIA,eAAe,EAAE;AAC3B;AACQ,UAAA,IAAIzD,SAAS,GAAG,CAACqC,oBAAoB,EAAExE,YAAY,CAAC,CAACvN,MAAM,CAAC,IAAI,CAACwP,iBAAiB,CAACuC,oBAAoB,CAAC,CAAC;AACzG,UAAA,IAAI,CAACpL,SAAS,CAAC+I,SAAS,EAAE,UAASwB,QAAQ,EAAE;AAC3C,YAAA,IAAI,CAACA,QAAQ,CAAClH,OAAO,EACnB,OAAO,KAAK;AACd,YAAA,IAAIyJ,UAAU,GAAGvC,QAAQ,CAACvJ,YAAY,CAAC,KAAK,CAAC;AAC7C,YAAA,IAAI8L,UAAU,EAAE;cACd,IAAI,CAACzU,WAAW,GAAGyU,UAAU;AAC7B,cAAA,OAAO,IAAI;AACvB;AACU,YAAA,OAAO,KAAK;AACtB,WAAS,CAAC;AACF,UAAA,OAAO3O,cAAc;AAC7B;AACA;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEyK,IAAAA,cAAc,EAAE,SAAhBA,cAAcA,CAAWhM,MAAM,EAAE;MAC/B,IAAI,OAAOA,MAAM,IAAI,QAAQ,IAAIA,MAAM,YAAYmQ,MAAM,EAAE;AACzDnQ,QAAAA,MAAM,GAAGA,MAAM,CAACsH,IAAI,EAAE;QACtB,OAAQtH,MAAM,CAACgC,MAAM,GAAG,CAAC,IAAMhC,MAAM,CAACgC,MAAM,GAAG,GAAI;AACzD;AACI,MAAA,OAAO,KAAK;KACb;AAEH;AACA;AACA;AACA;AACA;AACA;AACEoO,IAAAA,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAW1I,GAAG,EAAE;MACnC,IAAI,CAACA,GAAG,EAAE;AACR,QAAA,OAAOA,GAAG;AAChB;AAEI,MAAA,IAAI2I,aAAa,GAAG,IAAI,CAAChP,eAAe;MACxC,OAAOqG,GAAG,CAACvB,OAAO,CAAC,0BAA0B,EAAE,UAASC,CAAC,EAAErC,GAAG,EAAE;QAC9D,OAAOsM,aAAa,CAACtM,GAAG,CAAC;AAC/B,OAAK,CAAC,CAACoC,OAAO,CAAC,wCAAwC,EAAE,UAASC,CAAC,EAAEkK,GAAG,EAAEC,MAAM,EAAE;AAC5E,QAAA,IAAIC,GAAG,GAAGC,QAAQ,CAACH,GAAG,IAAIC,MAAM,EAAED,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC;AAChD,QAAA,OAAOH,MAAM,CAACO,YAAY,CAACF,GAAG,CAAC;AACrC,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACA;AACEG,IAAAA,UAAU,EAAE,SAAZA,UAAUA,CAAY7V,GAAG,EAAE;MACzB,IAAI8V,OAAO,GAAG,IAAI,CAAChN,mBAAmB,CAAC9I,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC;AAEvD,MAAA,IAAI+V,QAAQ;AAEZ,MAAA,IAAI,CAAC9N,YAAY,CAAC6N,OAAO,EAAE,UAASE,aAAa,EAAE;QACjD,IAAI,CAACD,QAAQ,IAAIC,aAAa,CAAC1M,YAAY,CAAC,MAAM,CAAC,KAAK,qBAAqB,EAAE;UAC7E,IAAI;AACZ;YACU,IAAI2M,OAAO,GAAGD,aAAa,CAAC9S,WAAW,CAACmI,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC;AACjF,YAAA,IAAI6K,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAC;AAChC,YAAA,IACE,CAACC,MAAM,CAAC,UAAU,CAAC,IACnB,CAACA,MAAM,CAAC,UAAU,CAAC,CAAClJ,KAAK,CAAC,2BAA2B,CAAA,EACrD;AACA,cAAA;AACZ;AAEU,YAAA,IAAI,CAACkJ,MAAM,CAAC,OAAO,CAAC,IAAI9S,KAAK,CAACgG,OAAO,CAAC8M,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE;cACvDA,MAAM,GAAGA,MAAM,CAAC,QAAQ,CAAC,CAAC7N,IAAI,CAAC,UAASgO,EAAE,EAAE;AAC1C,gBAAA,OAAO,CAACA,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAErJ,KAAK,CAC9B,IAAI,CAACzK,OAAO,CAACwD,kBACd,CAAA;AACf,eAAa,CAAC;AACd;YAEU,IACE,CAACmQ,MAAM,IACP,CAACA,MAAM,CAAC,OAAO,CAAC,IAChB,CAACA,MAAM,CAAC,OAAO,CAAC,CAAClJ,KAAK,CAAC,IAAI,CAACzK,OAAO,CAACwD,kBAAkB,CAAA,EACtD;AACA,cAAA;AACZ;YAEUgQ,QAAQ,GAAG,EAAE;YAEb,IAAI,OAAOG,MAAM,CAAC1S,IAAI,KAAK,QAAQ,IAAI,OAAO0S,MAAM,CAACI,QAAQ,KAAK,QAAQ,IAAIJ,MAAM,CAAC1S,IAAI,KAAK0S,MAAM,CAACI,QAAQ,EAAE;AACzH;AACA;AACA;;AAEY,cAAA,IAAI/J,KAAK,GAAG,IAAI,CAACH,gBAAgB,EAAE;AACnC,cAAA,IAAImK,WAAW,GAAG,IAAI,CAAClG,eAAe,CAAC6F,MAAM,CAAC1S,IAAI,EAAE+I,KAAK,CAAC,GAAG,IAAI;AACjE,cAAA,IAAIiK,eAAe,GAAG,IAAI,CAACnG,eAAe,CAAC6F,MAAM,CAACI,QAAQ,EAAE/J,KAAK,CAAC,GAAG,IAAI;AAEzE,cAAA,IAAIiK,eAAe,IAAI,CAACD,WAAW,EAAE;AACnCR,gBAAAA,QAAQ,CAACxJ,KAAK,GAAG2J,MAAM,CAACI,QAAQ;AAC9C,eAAa,MAAM;AACLP,gBAAAA,QAAQ,CAACxJ,KAAK,GAAG2J,MAAM,CAAC1S,IAAI;AAC1C;aACW,MAAM,IAAI,OAAO0S,MAAM,CAAC1S,IAAI,KAAK,QAAQ,EAAE;cAC1CuS,QAAQ,CAACxJ,KAAK,GAAG2J,MAAM,CAAC1S,IAAI,CAACgJ,IAAI,EAAE;aACpC,MAAM,IAAI,OAAO0J,MAAM,CAACI,QAAQ,KAAK,QAAQ,EAAE;cAC9CP,QAAQ,CAACxJ,KAAK,GAAG2J,MAAM,CAACI,QAAQ,CAAC9J,IAAI,EAAE;AACnD;YACU,IAAI0J,MAAM,CAACO,MAAM,EAAE;cACjB,IAAI,OAAOP,MAAM,CAACO,MAAM,CAACjT,IAAI,KAAK,QAAQ,EAAE;gBAC1CuS,QAAQ,CAAC7Q,MAAM,GAAGgR,MAAM,CAACO,MAAM,CAACjT,IAAI,CAACgJ,IAAI,EAAE;AACzD,eAAa,MAAM,IAAIpJ,KAAK,CAACgG,OAAO,CAAC8M,MAAM,CAACO,MAAM,CAAC,IAAIP,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,IAAI,OAAOP,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,CAACjT,IAAI,KAAK,QAAQ,EAAE;gBACxGuS,QAAQ,CAAC7Q,MAAM,GAAGgR,MAAM,CAACO,MAAA,CACtBlN,MAAM,CAAC,UAASkN,MAAM,EAAE;AACvB,kBAAA,OAAOA,MAAM,IAAI,OAAOA,MAAM,CAACjT,IAAI,KAAK,QAAQ;AACjD,iBAAA,CAAA,CACAe,GAAG,CAAC,UAASkS,MAAM,EAAE;AACpB,kBAAA,OAAOA,MAAM,CAACjT,IAAI,CAACgJ,IAAI,EAAE;AAC1B,iBAAA,CAAA,CACA9I,IAAI,CAAC,IAAI,CAAC;AAC3B;AACA;AACU,YAAA,IAAI,OAAOwS,MAAM,CAACQ,WAAW,KAAK,QAAQ,EAAE;cAC1CX,QAAQ,CAACY,OAAO,GAAGT,MAAM,CAACQ,WAAW,CAAClK,IAAI,EAAE;AACxD;AACU,YAAA,IACE0J,MAAM,CAACU,SAAS,IAChB,OAAOV,MAAM,CAACU,SAAS,CAACpT,IAAI,KAAK,QAAA,EACjC;cACAuS,QAAQ,CAACc,QAAQ,GAAGX,MAAM,CAACU,SAAS,CAACpT,IAAI,CAACgJ,IAAI,EAAE;AAC5D;AACU,YAAA,IAAI,OAAO0J,MAAM,CAACY,aAAa,KAAK,QAAQ,EAAE;cAC5Cf,QAAQ,CAACe,aAAa,GAAGZ,MAAM,CAACY,aAAa,CAACtK,IAAI,EAAE;AAChE;AACU,YAAA;WACD,CAAC,OAAO1E,GAAG,EAAE;AACZ,YAAA,IAAI,CAAClE,GAAG,CAACkE,GAAG,CAACiP,OAAO,CAAC;AAC/B;AACA;AACA,OAAK,CAAC;AACF,MAAA,OAAOhB,QAAQ,GAAGA,QAAQ,GAAG,EAAE;KAChC;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEiB,IAAAA,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAWC,MAAM,EAAE;MACpC,IAAIlB,QAAQ,GAAG,EAAE;MACjB,IAAImB,MAAM,GAAG,EAAE;MACf,IAAIC,YAAY,GAAG,IAAI,CAAC9W,IAAI,CAAC8I,oBAAoB,CAAC,MAAM,CAAC;;AAE7D;MACI,IAAIiO,eAAe,GAAG,0GAA0G;;AAEpI;MACI,IAAIC,WAAW,GAAG,qHAAqH;;AAE3I;AACI,MAAA,IAAI,CAACpP,YAAY,CAACkP,YAAY,EAAE,UAASG,OAAO,EAAE;AAChD,QAAA,IAAIC,WAAW,GAAGD,OAAO,CAAChO,YAAY,CAAC,MAAM,CAAC;AAC9C,QAAA,IAAIkO,eAAe,GAAGF,OAAO,CAAChO,YAAY,CAAC,UAAU,CAAC;AACtD,QAAA,IAAI2M,OAAO,GAAGqB,OAAO,CAAChO,YAAY,CAAC,SAAS,CAAC;QAC7C,IAAI,CAAC2M,OAAO,EAAE;AACZ,UAAA;AACR;QACM,IAAIwB,OAAO,GAAG,IAAI;QAClB,IAAIjU,IAAI,GAAG,IAAI;AAEf,QAAA,IAAIgU,eAAe,EAAE;AACnBC,UAAAA,OAAO,GAAGD,eAAe,CAACxK,KAAK,CAACoK,eAAe,CAAC;AAChD,UAAA,IAAIK,OAAO,EAAE;AACrB;AACA;AACUjU,YAAAA,IAAI,GAAGiU,OAAO,CAAC,CAAC,CAAC,CAAClJ,WAAW,EAAE,CAAClD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAC5D;YACU6L,MAAM,CAAC1T,IAAI,CAAC,GAAGyS,OAAO,CAACzJ,IAAI,EAAE;AACvC;AACA;QACM,IAAI,CAACiL,OAAO,IAAIF,WAAW,IAAIF,WAAW,CAACxK,IAAI,CAAC0K,WAAW,CAAC,EAAE;AAC5D/T,UAAAA,IAAI,GAAG+T,WAAW;AAClB,UAAA,IAAItB,OAAO,EAAE;AACrB;AACA;AACUzS,YAAAA,IAAI,GAAGA,IAAI,CAAC+K,WAAW,EAAE,CAAClD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;YAChE6L,MAAM,CAAC1T,IAAI,CAAC,GAAGyS,OAAO,CAACzJ,IAAI,EAAE;AACvC;AACA;AACA,OAAK,CAAC;;AAEN;AACIuJ,MAAAA,QAAQ,CAACxJ,KAAK,GAAG0K,MAAM,CAAC1K,KAAK,IACZ2K,MAAM,CAAC,UAAU,CAAC,IAClBA,MAAM,CAAC,cAAc,CAAC,IACtBA,MAAM,CAAC,UAAU,CAAC,IAClBA,MAAM,CAAC,qBAAqB,CAAC,IAC7BA,MAAM,CAAC,qBAAqB,CAAC,IAC7BA,MAAM,CAAC,OAAO,CAAC,IACfA,MAAM,CAAC,eAAe,CAAC;AAExC,MAAA,IAAI,CAACnB,QAAQ,CAACxJ,KAAK,EAAE;AACnBwJ,QAAAA,QAAQ,CAACxJ,KAAK,GAAG,IAAI,CAACH,gBAAgB,EAAE;AAC9C;;AAEA;MACI2J,QAAQ,CAAC7Q,MAAM,GAAG+R,MAAM,CAAC/R,MAAM,IACbgS,MAAM,CAAC,YAAY,CAAC,IACpBA,MAAM,CAAC,gBAAgB,CAAC,IACxBA,MAAM,CAAC,QAAQ,CAAC;;AAEtC;AACInB,MAAAA,QAAQ,CAACY,OAAO,GAAGM,MAAM,CAACN,OAAO,IACdO,MAAM,CAAC,gBAAgB,CAAC,IACxBA,MAAM,CAAC,oBAAoB,CAAC,IAC5BA,MAAM,CAAC,gBAAgB,CAAC,IACxBA,MAAM,CAAC,2BAA2B,CAAC,IACnCA,MAAM,CAAC,2BAA2B,CAAC,IACnCA,MAAM,CAAC,aAAa,CAAC,IACrBA,MAAM,CAAC,qBAAqB,CAAC;;AAEpD;MACInB,QAAQ,CAACc,QAAQ,GAAGI,MAAM,CAACJ,QAAQ,IACfK,MAAM,CAAC,cAAc,CAAC;;AAE9C;AACInB,MAAAA,QAAQ,CAAC2B,aAAa,GAAGT,MAAM,CAACH,aAAa,IAC3CI,MAAM,CAAC,wBAAwB,CAAC,IAAI,IAAI;;AAE9C;AACA;MACInB,QAAQ,CAACxJ,KAAK,GAAG,IAAI,CAAC+I,qBAAqB,CAACS,QAAQ,CAACxJ,KAAK,CAAC;MAC3DwJ,QAAQ,CAAC7Q,MAAM,GAAG,IAAI,CAACoQ,qBAAqB,CAACS,QAAQ,CAAC7Q,MAAM,CAAC;MAC7D6Q,QAAQ,CAACY,OAAO,GAAG,IAAI,CAACrB,qBAAqB,CAACS,QAAQ,CAACY,OAAO,CAAC;MAC/DZ,QAAQ,CAACc,QAAQ,GAAG,IAAI,CAACvB,qBAAqB,CAACS,QAAQ,CAACc,QAAQ,CAAC;MACjEd,QAAQ,CAAC2B,aAAa,GAAG,IAAI,CAACpC,qBAAqB,CAACS,QAAQ,CAAC2B,aAAa,CAAC;AAE3E,MAAA,OAAO3B,QAAQ;KAChB;AAEH;AACA;AACA;AACA;AACA;AACA;AACE4B,IAAAA,cAAc,EAAE,SAAhBA,cAAcA,CAAW7U,IAAI,EAAE;AAC7B,MAAA,IAAIA,IAAI,CAAC6I,OAAO,KAAK,KAAK,EAAE;AAC1B,QAAA,OAAO,IAAI;AACjB;AAEI,MAAA,IAAI7I,IAAI,CAACoJ,QAAQ,CAAChF,MAAM,KAAK,CAAC,IAAIpE,IAAI,CAACI,WAAW,CAACsJ,IAAI,EAAE,KAAK,EAAE,EAAE;AAChE,QAAA,OAAO,KAAK;AAClB;MAEI,OAAO,IAAI,CAACmL,cAAc,CAAC7U,IAAI,CAACoJ,QAAQ,CAAC,CAAC,CAAC,CAAC;KAC7C;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE0L,IAAAA,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAW5X,GAAG,EAAE;AACvC;AACA;AACI,MAAA,IAAI6X,IAAI,GAAGzU,KAAK,CAACC,IAAI,CAACrD,GAAG,CAACmJ,oBAAoB,CAAC,KAAK,CAAC,CAAC;AACtD,MAAA,IAAI,CAAClB,YAAY,CAAC4P,IAAI,EAAE,UAASC,GAAG,EAAE;AACpC,QAAA,KAAK,IAAI7Q,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6Q,GAAG,CAACxU,UAAU,CAAC4D,MAAM,EAAED,CAAC,EAAE,EAAE;AAC9C,UAAA,IAAI1D,IAAI,GAAGuU,GAAG,CAACxU,UAAU,CAAC2D,CAAC,CAAC;UAC5B,QAAQ1D,IAAI,CAACC,IAAI;AACf,YAAA,KAAK,KAAK;AACV,YAAA,KAAK,QAAQ;AACb,YAAA,KAAK,UAAU;AACf,YAAA,KAAK,aAAa;AAChB,cAAA;AACZ;UAEQ,IAAI,wBAAwB,CAACqJ,IAAI,CAACtJ,IAAI,CAACE,KAAK,CAAC,EAAE;AAC7C,YAAA;AACV;AACA;AAEMqU,QAAAA,GAAG,CAAC3Q,UAAU,CAACC,WAAW,CAAC0Q,GAAG,CAAC;AACrC,OAAK,CAAC;;AAEN;AACI,MAAA,IAAIC,SAAS,GAAG3U,KAAK,CAACC,IAAI,CAACrD,GAAG,CAACmJ,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAChE,MAAA,IAAI,CAAClB,YAAY,CAAC8P,SAAS,EAAE,UAASC,QAAQ,EAAE;AACpD;AACM,QAAA,IAAIC,GAAG,GAAGjY,GAAG,CAAC6K,aAAa,CAAC,KAAK,CAAC;AAClCoN,QAAAA,GAAG,CAAC/V,SAAS,GAAG8V,QAAQ,CAAC9V,SAAS;AAClC,QAAA,IAAI,CAAC,IAAI,CAACyV,cAAc,CAACM,GAAG,CAAC,EAAE;AAC7B,UAAA;AACR;;AAEA;AACA;AACA;AACM,QAAA,IAAIC,WAAW,GAAGF,QAAQ,CAACG,sBAAsB;QACjD,IAAID,WAAW,IAAI,IAAI,CAACP,cAAc,CAACO,WAAW,CAAC,EAAE;UACnD,IAAIE,OAAO,GAAGF,WAAW;AACzB,UAAA,IAAIE,OAAO,CAACzM,OAAO,KAAK,KAAK,EAAE;YAC7ByM,OAAO,GAAGF,WAAW,CAAC/O,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9D;UAEQ,IAAIkP,MAAM,GAAGJ,GAAG,CAAC9O,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/C,UAAA,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmR,OAAO,CAAC9U,UAAU,CAAC4D,MAAM,EAAED,CAAC,EAAE,EAAE;AAClD,YAAA,IAAI1D,IAAI,GAAG6U,OAAO,CAAC9U,UAAU,CAAC2D,CAAC,CAAC;AAChC,YAAA,IAAI1D,IAAI,CAACE,KAAK,KAAK,EAAE,EAAE;AACrB,cAAA;AACZ;YAEU,IAAIF,IAAI,CAACC,IAAI,KAAK,KAAK,IAAID,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAI,wBAAwB,CAACqJ,IAAI,CAACtJ,IAAI,CAACE,KAAK,CAAC,EAAE;AAC9F,cAAA,IAAI4U,MAAM,CAAC/O,YAAY,CAAC/F,IAAI,CAACC,IAAI,CAAC,KAAKD,IAAI,CAACE,KAAK,EAAE;AACjD,gBAAA;AACd;AAEY,cAAA,IAAI6U,QAAQ,GAAG/U,IAAI,CAACC,IAAI;AACxB,cAAA,IAAI6U,MAAM,CAACE,YAAY,CAACD,QAAQ,CAAC,EAAE;gBACjCA,QAAQ,GAAG,WAAW,GAAGA,QAAQ;AAC/C;cAEYD,MAAM,CAAC3O,YAAY,CAAC4O,QAAQ,EAAE/U,IAAI,CAACE,KAAK,CAAC;AACrD;AACA;UAEQuU,QAAQ,CAAC7Q,UAAU,CAACwD,YAAY,CAACsN,GAAG,CAACrO,iBAAiB,EAAEsO,WAAW,CAAC;AAC5E;AACA,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACA;AACEM,IAAAA,cAAc,EAAE,SAAhBA,cAAcA,CAAWxY,GAAG,EAAE;AAC5B,MAAA,IAAI,CAAC6G,YAAY,CAAC,IAAI,CAACiC,mBAAmB,CAAC9I,GAAG,EAAE,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC;KACzE;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEgM,IAAAA,0BAA0B,EAAE,SAA5BA,0BAA0BA,CAAWsL,OAAO,EAAErO,GAAG,EAAE;AACrD;AACI,MAAA,IAAIqO,OAAO,CAACpL,QAAQ,CAAChF,MAAM,IAAI,CAAC,IAAIoQ,OAAO,CAACpL,QAAQ,CAAC,CAAC,CAAC,CAACP,OAAO,KAAK1C,GAAG,EAAE;AACvE,QAAA,OAAO,KAAK;AAClB;;AAEA;MACI,OAAO,CAAC,IAAI,CAACX,SAAS,CAACgP,OAAO,CAAC9M,UAAU,EAAE,UAAS1H,IAAI,EAAE;AACxD,QAAA,OAAOA,IAAI,CAACC,QAAQ,KAAK,IAAI,CAACC,SAAS,IAChC,IAAI,CAACT,OAAO,CAACmD,UAAU,CAACmH,IAAI,CAAC/J,IAAI,CAACI,WAAW,CAAC;AAC3D,OAAK,CAAC;KACH;AAED4I,IAAAA,wBAAwB,EAAE,SAA1BA,wBAAwBA,CAAWhJ,IAAI,EAAE;MACvC,OAAOA,IAAI,CAACC,QAAQ,KAAK,IAAI,CAACkB,YAAY,IACxCnB,IAAI,CAACI,WAAW,CAACsJ,IAAI,EAAE,CAACtF,MAAM,IAAI,CAAC,KAClCpE,IAAI,CAACoJ,QAAQ,CAAChF,MAAM,IAAI,CAAC,IACzBpE,IAAI,CAACoJ,QAAQ,CAAChF,MAAM,IAAIpE,IAAI,CAACqG,oBAAoB,CAAC,IAAI,CAAC,CAACjC,MAAM,GAAGpE,IAAI,CAACqG,oBAAoB,CAAC,IAAI,CAAC,CAACjC,MAAM,CAAC;KAC5G;AAEH;AACA;AACA;AACA;AACA;AACEoL,IAAAA,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAYgF,OAAO,EAAE;MACxC,OAAO,IAAI,CAAChP,SAAS,CAACgP,OAAO,CAAC9M,UAAU,EAAE,UAAS1H,IAAI,EAAE;AACvD,QAAA,OAAO,IAAI,CAACmD,cAAc,CAACwS,GAAG,CAAC3V,IAAI,CAAC6I,OAAO,CAAC,IACrC,IAAI,CAAC2G,qBAAqB,CAACxP,IAAI,CAAC;AAC7C,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACEqL,IAAAA,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAWrL,IAAI,EAAE;MACjC,OAAOA,IAAI,CAACC,QAAQ,KAAK,IAAI,CAACC,SAAS,IAAI,IAAI,CAACsD,cAAc,CAACmD,OAAO,CAAC3G,IAAI,CAAC6I,OAAO,CAAC,KAAK,CAAC,CAAC,IACxF,CAAC7I,IAAI,CAAC6I,OAAO,KAAK,GAAG,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,KAAK,IAAI7I,IAAI,CAAC6I,OAAO,KAAK,KAAK,KACxE,IAAI,CAACnD,UAAU,CAAC1F,IAAI,CAAC0H,UAAU,EAAE,IAAI,CAAC2D,kBAAkB,CAAE;KAC/D;AAEDG,IAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAWxL,IAAI,EAAE;AAC5B,MAAA,OAAQA,IAAI,CAACC,QAAQ,KAAK,IAAI,CAACC,SAAS,IAAIF,IAAI,CAACI,WAAW,CAACsJ,IAAI,EAAE,CAACtF,MAAM,KAAK,CAAC,IACxEpE,IAAI,CAACC,QAAQ,KAAK,IAAI,CAACkB,YAAY,IAAInB,IAAI,CAAC6I,OAAO,KAAK,IAAK;KACtE;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEc,IAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAW1E,CAAC,EAAE2Q,eAAe,EAAE;MAC1CA,eAAe,GAAI,OAAOA,eAAe,KAAK,WAAW,GAAI,IAAI,GAAGA,eAAe;MACnF,IAAIxV,WAAW,GAAG6E,CAAC,CAAC7E,WAAW,CAACsJ,IAAI,EAAE;AAEtC,MAAA,IAAIkM,eAAe,EAAE;QACnB,OAAOxV,WAAW,CAACmI,OAAO,CAAC,IAAI,CAAC9I,OAAO,CAAC6C,SAAS,EAAE,GAAG,CAAC;AAC7D;AACI,MAAA,OAAOlC,WAAW;KACnB;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEyV,IAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAW5Q,CAAC,EAAEL,CAAC,EAAE;MAC5BA,CAAC,GAAGA,CAAC,IAAI,GAAG;AACZ,MAAA,OAAO,IAAI,CAAC+E,aAAa,CAAC1E,CAAC,CAAC,CAACnD,KAAK,CAAC8C,CAAC,CAAC,CAACR,MAAM,GAAG,CAAC;KACjD;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACE0H,IAAAA,YAAY,EAAE,SAAdA,YAAYA,CAAW7G,CAAC,EAAE;AACxB,MAAA,IAAI,CAACA,CAAC,IAAIA,CAAC,CAAC4D,OAAO,CAAC4C,WAAW,EAAE,KAAK,KAAK,EACzC;;AAEN;AACI,MAAA,KAAK,IAAItH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACb,yBAAyB,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QAC9Dc,CAAC,CAAC4B,eAAe,CAAC,IAAI,CAACvD,yBAAyB,CAACa,CAAC,CAAC,CAAC;AAC1D;AAEI,MAAA,IAAI,IAAI,CAACZ,+BAA+B,CAACoD,OAAO,CAAC1B,CAAC,CAAC4D,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;AAClE5D,QAAAA,CAAC,CAAC4B,eAAe,CAAC,OAAO,CAAC;AAC1B5B,QAAAA,CAAC,CAAC4B,eAAe,CAAC,QAAQ,CAAC;AACjC;AAEI,MAAA,IAAIiP,GAAG,GAAG7Q,CAAC,CAAC6B,iBAAiB;MAC7B,OAAOgP,GAAG,KAAK,IAAI,EAAE;AACnB,QAAA,IAAI,CAAChK,YAAY,CAACgK,GAAG,CAAC;QACtBA,GAAG,GAAGA,GAAG,CAAC/O,kBAAkB;AAClC;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEuI,IAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAWkF,OAAO,EAAE;MACjC,IAAIvC,UAAU,GAAG,IAAI,CAACtI,aAAa,CAAC6K,OAAO,CAAC,CAACpQ,MAAM;AACnD,MAAA,IAAI6N,UAAU,KAAK,CAAC,EAClB,OAAO,CAAC;MAEV,IAAI8D,UAAU,GAAG,CAAC;;AAEtB;AACI,MAAA,IAAI,CAAC5Q,YAAY,CAACqP,OAAO,CAACnO,oBAAoB,CAAC,GAAG,CAAC,EAAE,UAAS2P,QAAQ,EAAE;AACtE,QAAA,IAAI1O,IAAI,GAAG0O,QAAQ,CAACxP,YAAY,CAAC,MAAM,CAAC;AACxC,QAAA,IAAIyP,WAAW,GAAG3O,IAAI,IAAI,IAAI,CAAC7H,OAAO,CAACoD,OAAO,CAACkH,IAAI,CAACzC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACnEyO,UAAU,IAAI,IAAI,CAACpM,aAAa,CAACqM,QAAQ,CAAC,CAAC5R,MAAM,GAAG6R,WAAW;AACrE,OAAK,CAAC;MAEF,OAAOF,UAAU,GAAG9D,UAAU;KAC/B;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACE7E,IAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAWnI,CAAC,EAAE;MAC3B,IAAI,CAAC,IAAI,CAAC6J,aAAa,CAAC,IAAI,CAACjP,mBAAmB,CAAC,EAC/C,OAAO,CAAC;MAEV,IAAIqW,MAAM,GAAG,CAAC;;AAElB;AACI,MAAA,IAAI,OAAOjR,CAAC,CAACsB,SAAU,KAAK,QAAQ,IAAItB,CAAC,CAACsB,SAAS,KAAK,EAAE,EAAE;AAC1D,QAAA,IAAI,IAAI,CAAC9G,OAAO,CAACyC,QAAQ,CAAC6H,IAAI,CAAC9E,CAAC,CAACsB,SAAS,CAAC,EACzC2P,MAAM,IAAI,EAAE;AAEd,QAAA,IAAI,IAAI,CAACzW,OAAO,CAACwC,QAAQ,CAAC8H,IAAI,CAAC9E,CAAC,CAACsB,SAAS,CAAC,EACzC2P,MAAM,IAAI,EAAE;AACpB;;AAEA;AACI,MAAA,IAAI,OAAOjR,CAAC,CAAC6D,EAAG,KAAK,QAAQ,IAAI7D,CAAC,CAAC6D,EAAE,KAAK,EAAE,EAAE;AAC5C,QAAA,IAAI,IAAI,CAACrJ,OAAO,CAACyC,QAAQ,CAAC6H,IAAI,CAAC9E,CAAC,CAAC6D,EAAE,CAAC,EAClCoN,MAAM,IAAI,EAAE;AAEd,QAAA,IAAI,IAAI,CAACzW,OAAO,CAACwC,QAAQ,CAAC8H,IAAI,CAAC9E,CAAC,CAAC6D,EAAE,CAAC,EAClCoN,MAAM,IAAI,EAAE;AACpB;AAEI,MAAA,OAAOA,MAAM;KACd;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACEhK,IAAAA,MAAM,EAAE,SAARA,MAAMA,CAAWjH,CAAC,EAAEkB,GAAG,EAAE;AACvB,MAAA,IAAIgQ,OAAO,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACxP,OAAO,CAACR,GAAG,CAAC,KAAK,CAAC,CAAC;AAE/D,MAAA,IAAI,CAACpC,YAAY,CAAC,IAAI,CAACiC,mBAAmB,CAACf,CAAC,EAAE,CAACkB,GAAG,CAAC,CAAC,EAAE,UAASqO,OAAO,EAAE;AAC5E;AACM,QAAA,IAAI2B,OAAO,EAAE;AACnB;AACQ,UAAA,KAAK,IAAIhS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqQ,OAAO,CAAChU,UAAU,CAAC4D,MAAM,EAAED,CAAC,EAAE,EAAE;AAClD,YAAA,IAAI,IAAI,CAAC5E,kBAAkB,CAACwK,IAAI,CAACyK,OAAO,CAAChU,UAAU,CAAC2D,CAAC,CAAC,CAACxD,KAAK,CAAC,EAAE;AAC7D,cAAA,OAAO,KAAK;AACxB;AACA;;AAEA;AACQ,UAAA,IAAI6T,OAAO,CAAC3L,OAAO,KAAK,QAAQ,IAAI,IAAI,CAACtJ,kBAAkB,CAACwK,IAAI,CAACyK,OAAO,CAACpV,SAAS,CAAC,EAAE;AACnF,YAAA,OAAO,KAAK;AACtB;AACA;AAEM,QAAA,OAAO,IAAI;AACjB,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACEgQ,eAAe,EAAE,SAAjBA,eAAeA,CAAWpP,IAAI,EAAE6I,OAAO,EAAEyF,QAAQ,EAAErK,QAAQ,EAAE;MAC3DqK,QAAQ,GAAGA,QAAQ,IAAI,CAAC;AACxBzF,MAAAA,OAAO,GAAGA,OAAO,CAAChH,WAAW,EAAE;MAC/B,IAAIuU,KAAK,GAAG,CAAC;MACb,OAAOpW,IAAI,CAACqE,UAAU,EAAE;QACtB,IAAIiK,QAAQ,GAAG,CAAC,IAAI8H,KAAK,GAAG9H,QAAQ,EAClC,OAAO,KAAK;QACd,IAAItO,IAAI,CAACqE,UAAU,CAACwE,OAAO,KAAKA,OAAO,KAAK,CAAC5E,QAAQ,IAAIA,QAAQ,CAACjE,IAAI,CAACqE,UAAU,CAAC,CAAC,EACjF,OAAO,IAAI;QACbrE,IAAI,GAAGA,IAAI,CAACqE,UAAU;AACtB+R,QAAAA,KAAK,EAAE;AACb;AACI,MAAA,OAAO,KAAK;KACb;AAEH;AACA;AACA;AACEC,IAAAA,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAWvJ,KAAK,EAAE;MACrC,IAAIwJ,IAAI,GAAG,CAAC;MACZ,IAAIC,OAAO,GAAG,CAAC;AACf,MAAA,IAAIC,GAAG,GAAG1J,KAAK,CAACzG,oBAAoB,CAAC,IAAI,CAAC;AAC1C,MAAA,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqS,GAAG,CAACpS,MAAM,EAAED,CAAC,EAAE,EAAE;AACnC,QAAA,IAAIsS,OAAO,GAAGD,GAAG,CAACrS,CAAC,CAAC,CAACqC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC;AACjD,QAAA,IAAIiQ,OAAO,EAAE;AACXA,UAAAA,OAAO,GAAG5D,QAAQ,CAAC4D,OAAO,EAAE,EAAE,CAAC;AACvC;QACMH,IAAI,IAAKG,OAAO,IAAI,CAAE;;AAE5B;QACM,IAAIC,gBAAgB,GAAG,CAAC;QACxB,IAAIC,KAAK,GAAGH,GAAG,CAACrS,CAAC,CAAC,CAACkC,oBAAoB,CAAC,IAAI,CAAC;AAC7C,QAAA,KAAK,IAAIuQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACvS,MAAM,EAAEwS,CAAC,EAAE,EAAE;AACrC,UAAA,IAAIC,OAAO,GAAGF,KAAK,CAACC,CAAC,CAAC,CAACpQ,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC;AACnD,UAAA,IAAIqQ,OAAO,EAAE;AACXA,YAAAA,OAAO,GAAGhE,QAAQ,CAACgE,OAAO,EAAE,EAAE,CAAC;AACzC;UACQH,gBAAgB,IAAKG,OAAO,IAAI,CAAE;AAC1C;QACMN,OAAO,GAAG3G,IAAI,CAAC0B,GAAG,CAACiF,OAAO,EAAEG,gBAAgB,CAAC;AACnD;MACI,OAAO;AAACJ,QAAAA,IAAI,EAAEA,IAAI;AAAEC,QAAAA,OAAO,EAAEA;OAAQ;KACtC;AAEH;AACA;AACA;AACA;AACA;AACExK,IAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAW+K,IAAI,EAAE;AAC9B,MAAA,IAAIC,MAAM,GAAGD,IAAI,CAACzQ,oBAAoB,CAAC,OAAO,CAAC;AAC/C,MAAA,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4S,MAAM,CAAC3S,MAAM,EAAED,CAAC,EAAE,EAAE;AACtC,QAAA,IAAI2I,KAAK,GAAGiK,MAAM,CAAC5S,CAAC,CAAC;AACrB,QAAA,IAAI6S,IAAI,GAAGlK,KAAK,CAACtG,YAAY,CAAC,MAAM,CAAC;QACrC,IAAIwQ,IAAI,IAAI,cAAc,EAAE;UAC1BlK,KAAK,CAACmK,qBAAqB,GAAG,KAAK;AACnC,UAAA;AACR;AACM,QAAA,IAAIC,SAAS,GAAGpK,KAAK,CAACtG,YAAY,CAAC,WAAW,CAAC;QAC/C,IAAI0Q,SAAS,IAAI,GAAG,EAAE;UACpBpK,KAAK,CAACmK,qBAAqB,GAAG,KAAK;AACnC,UAAA;AACR;AACM,QAAA,IAAIE,OAAO,GAAGrK,KAAK,CAACtG,YAAY,CAAC,SAAS,CAAC;AAC3C,QAAA,IAAI2Q,OAAO,EAAE;UACXrK,KAAK,CAACmK,qBAAqB,GAAG,IAAI;AAClC,UAAA;AACR;QAEM,IAAIG,OAAO,GAAGtK,KAAK,CAACzG,oBAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACtD,IAAI+Q,OAAO,IAAIA,OAAO,CAAC1P,UAAU,CAACtD,MAAM,GAAG,CAAC,EAAE;UAC5C0I,KAAK,CAACmK,qBAAqB,GAAG,IAAI;AAClC,UAAA;AACR;;AAEA;AACM,QAAA,IAAII,oBAAoB,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;AACtE,QAAA,IAAIC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAYnR,GAAG,EAAE;UACnC,OAAO,CAAC,CAAC2G,KAAK,CAACzG,oBAAoB,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;SAC5C;AACD,QAAA,IAAIkR,oBAAoB,CAAC5R,IAAI,CAAC6R,gBAAgB,CAAC,EAAE;AAC/C,UAAA,IAAI,CAACxW,GAAG,CAAC,4CAA4C,CAAC;UACtDgM,KAAK,CAACmK,qBAAqB,GAAG,IAAI;AAClC,UAAA;AACR;;AAEA;QACM,IAAInK,KAAK,CAACzG,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1CyG,KAAK,CAACmK,qBAAqB,GAAG,KAAK;AACnC,UAAA;AACR;AAEM,QAAA,IAAIM,QAAQ,GAAG,IAAI,CAAClB,qBAAqB,CAACvJ,KAAK,CAAC;QAChD,IAAIyK,QAAQ,CAACjB,IAAI,IAAI,EAAE,IAAIiB,QAAQ,CAAChB,OAAO,GAAG,CAAC,EAAE;UAC/CzJ,KAAK,CAACmK,qBAAqB,GAAG,IAAI;AAClC,UAAA;AACR;AACA;QACMnK,KAAK,CAACmK,qBAAqB,GAAGM,QAAQ,CAACjB,IAAI,GAAGiB,QAAQ,CAAChB,OAAO,GAAG,EAAE;AACzE;KACG;AAEH;AACEvK,IAAAA,cAAc,EAAE,SAAhBA,cAAcA,CAAY8K,IAAI,EAAE;MAC9B,IAAI,CAAC3R,YAAY,CAAC,IAAI,CAACa,mBAAmB,CAAC8Q,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,UAAU/L,IAAI,EAAE;AACpG;AACA;AACM,QAAA,IAAIA,IAAI,CAAC5C,GAAG,IAAI,IAAI,CAAC1I,OAAO,CAACsD,UAAU,CAACgH,IAAI,CAACgB,IAAI,CAAC5C,GAAG,CAAC,EAAE;AAC9D;AACQ,UAAA,IAAIqP,KAAK,GAAG,IAAI,CAAC/X,OAAO,CAACsD,UAAU,CAAC0U,IAAI,CAAC1M,IAAI,CAAC5C,GAAG,CAAC;AAClD,UAAA,IAAIqP,KAAK,CAAC,CAAC,CAAC,KAAK,eAAe,EAAE;AAChC,YAAA;AACV;;AAEA;AACA;UACQ,IAAIE,iBAAiB,GAAG,KAAK;AAC7B,UAAA,KAAK,IAAIvT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4G,IAAI,CAACvK,UAAU,CAAC4D,MAAM,EAAED,CAAC,EAAE,EAAE;AAC/C,YAAA,IAAI1D,IAAI,GAAGsK,IAAI,CAACvK,UAAU,CAAC2D,CAAC,CAAC;AAC7B,YAAA,IAAI1D,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;AACvB,cAAA;AACZ;YAEU,IAAI,wBAAwB,CAACqJ,IAAI,CAACtJ,IAAI,CAACE,KAAK,CAAC,EAAE;AAC7C+W,cAAAA,iBAAiB,GAAG,IAAI;AACxB,cAAA;AACZ;AACA;;AAEA;AACA;AACQ,UAAA,IAAIA,iBAAiB,EAAE;YACrB,IAAIC,SAAS,GAAG5M,IAAI,CAAC5C,GAAG,CAAC2J,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC;YACjD,IAAI8F,SAAS,GAAG7M,IAAI,CAAC5C,GAAG,CAAC/D,MAAM,GAAGuT,SAAS;YAC3C,IAAIC,SAAS,GAAG,GAAG,EAAE;AACnB7M,cAAAA,IAAI,CAAClE,eAAe,CAAC,KAAK,CAAC;AACvC;AACA;AACA;;AAEA;AACM,QAAA,IAAI,CAACkE,IAAI,CAAC5C,GAAG,IAAK4C,IAAI,CAAC1C,MAAM,IAAI0C,IAAI,CAAC1C,MAAM,IAAI,MAAO,KAAK0C,IAAI,CAACxE,SAAS,CAACkF,WAAW,EAAE,CAAC9E,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;AAC/G,UAAA;AACR;AAEM,QAAA,KAAK,IAAIiQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7L,IAAI,CAACvK,UAAU,CAAC4D,MAAM,EAAEwS,CAAC,EAAE,EAAE;AAC/CnW,UAAAA,IAAI,GAAGsK,IAAI,CAACvK,UAAU,CAACoW,CAAC,CAAC;AACzB,UAAA,IAAInW,IAAI,CAACC,IAAI,KAAK,KAAK,IAAID,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAID,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;AACxE,YAAA;AACV;UACQ,IAAImX,MAAM,GAAG,IAAI;UACjB,IAAI,4BAA4B,CAAC9N,IAAI,CAACtJ,IAAI,CAACE,KAAK,CAAC,EAAE;AACjDkX,YAAAA,MAAM,GAAG,QAAQ;WAClB,MAAM,IAAI,qCAAqC,CAAC9N,IAAI,CAACtJ,IAAI,CAACE,KAAK,CAAC,EAAE;AACjEkX,YAAAA,MAAM,GAAG,KAAK;AACxB;AACQ,UAAA,IAAIA,MAAM,EAAE;AACpB;YACU,IAAI9M,IAAI,CAAClC,OAAO,KAAK,KAAK,IAAIkC,IAAI,CAAClC,OAAO,KAAK,SAAS,EAAE;cACxDkC,IAAI,CAACnE,YAAY,CAACiR,MAAM,EAAEpX,IAAI,CAACE,KAAK,CAAC;aACtC,MAAM,IAAIoK,IAAI,CAAClC,OAAO,KAAK,QAAQ,IAAI,CAAC,IAAI,CAAC7C,mBAAmB,CAAC+E,IAAI,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC3G,MAAM,EAAE;AAC9G;AACA;cACY,IAAI4Q,GAAG,GAAG,IAAI,CAACzX,IAAI,CAACwK,aAAa,CAAC,KAAK,CAAC;cACxCiN,GAAG,CAACpO,YAAY,CAACiR,MAAM,EAAEpX,IAAI,CAACE,KAAK,CAAC;AACpCoK,cAAAA,IAAI,CAAC/C,WAAW,CAACgN,GAAG,CAAC;AACjC;AACA;AACA;AACA,OAAK,CAAC;KACH;AAED8C,IAAAA,eAAe,EAAE,SAAjBA,eAAeA,CAAW7S,CAAC,EAAE8S,IAAI,EAAE;AAAA,MAAA,IAAAC,MAAA,GAAA,IAAA;MACjC,IAAI/F,UAAU,GAAG,IAAI,CAACtI,aAAa,CAAC1E,CAAC,EAAE,IAAI,CAAC,CAACb,MAAM;MACnD,IAAI6N,UAAU,KAAK,CAAC,EAAE;AACpB,QAAA,OAAO,CAAC;AACd;MACI,IAAIgG,cAAc,GAAG,CAAC;MACtB,IAAI7O,QAAQ,GAAG,IAAI,CAACpD,mBAAmB,CAACf,CAAC,EAAE8S,IAAI,CAAC;AAChD,MAAA,IAAI,CAAC5S,YAAY,CAACiE,QAAQ,EAAE,UAACD,KAAK,EAAA;QAAA,OAAK8O,cAAc,IAAID,MAAI,CAACrO,aAAa,CAACR,KAAK,EAAE,IAAI,CAAC,CAAC/E,MAAM;OAAC,CAAA;MAChG,OAAO6T,cAAc,GAAGhG,UAAU;KACnC;AAEH;AACA;AACA;AACA;AACA;AACA;AACEhG,IAAAA,mBAAmB,EAAE,SAArBA,mBAAmBA,CAAWhH,CAAC,EAAEkB,GAAG,EAAE;MACpC,IAAI,CAAC,IAAI,CAAC2I,aAAa,CAAC,IAAI,CAAChP,wBAAwB,CAAC,EACpD;;AAEN;AACA;AACA;AACA;AACA;AACI,MAAA,IAAI,CAACiE,YAAY,CAAC,IAAI,CAACiC,mBAAmB,CAACf,CAAC,EAAE,CAACkB,GAAG,CAAC,CAAC,EAAE,UAASnG,IAAI,EAAE;AAAA,QAAA,IAAAkY,MAAA,GAAA,IAAA;AACzE;AACM,QAAA,IAAIC,WAAW,GAAG,SAAdA,WAAWA,CAAY5H,CAAC,EAAE;UAC5B,OAAOA,CAAC,CAAC0G,qBAAqB;SAC/B;QAED,IAAImB,MAAM,GAAGjS,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,IAAI;QACzC,IAAI,CAACiS,MAAM,EAAE;UACX,IAAIC,UAAU,GAAG,CAAC;AAClB,UAAA,IAAIC,SAAS,GAAG,IAAI,CAACtS,mBAAmB,CAAChG,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D,UAAA,IAAI,CAACmF,YAAY,CAACmT,SAAS,EAAE,UAACvS,IAAI,EAAA;YAAA,OAAKsS,UAAU,IAAIH,MAAI,CAACvO,aAAa,CAAC5D,IAAI,CAAC,CAAC3B,MAAM;WAAC,CAAA;AACrFgU,UAAAA,MAAM,GAAGC,UAAU,GAAG,IAAI,CAAC1O,aAAa,CAAC3J,IAAI,CAAC,CAACoE,MAAM,GAAG,GAAG;AACnE;QAEM,IAAI+B,GAAG,KAAK,OAAO,IAAIgS,WAAW,CAACnY,IAAI,CAAC,EAAE;AACxC,UAAA,OAAO,KAAK;AACpB;;AAEA;AACM,QAAA,IAAI,IAAI,CAACoP,eAAe,CAACpP,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,EAAEmY,WAAW,CAAC,EAAE;AACxD,UAAA,OAAO,KAAK;AACpB;QAEM,IAAI,IAAI,CAAC/I,eAAe,CAACpP,IAAI,EAAE,MAAM,CAAC,EAAE;AACtC,UAAA,OAAO,KAAK;AACpB;AAEM,QAAA,IAAIkW,MAAM,GAAG,IAAI,CAAC9I,eAAe,CAACpN,IAAI,CAAC;AAEvC,QAAA,IAAI,CAACc,GAAG,CAAC,wBAAwB,EAAEd,IAAI,CAAC;QAExC,IAAImN,YAAY,GAAG,CAAC;AAEpB,QAAA,IAAI+I,MAAM,GAAG/I,YAAY,GAAG,CAAC,EAAE;AAC7B,UAAA,OAAO,IAAI;AACnB;QAEM,IAAI,IAAI,CAAC0I,aAAa,CAAC7V,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,EAAE;AAC9C;AACA;AACA;UACQ,IAAImL,CAAC,GAAGnL,IAAI,CAACqG,oBAAoB,CAAC,GAAG,CAAC,CAACjC,MAAM;UAC7C,IAAI4Q,GAAG,GAAGhV,IAAI,CAACqG,oBAAoB,CAAC,KAAK,CAAC,CAACjC,MAAM;UACjD,IAAImU,EAAE,GAAGvY,IAAI,CAACqG,oBAAoB,CAAC,IAAI,CAAC,CAACjC,MAAM,GAAG,GAAG;UACrD,IAAIoU,KAAK,GAAGxY,IAAI,CAACqG,oBAAoB,CAAC,OAAO,CAAC,CAACjC,MAAM;UACrD,IAAIqU,cAAc,GAAG,IAAI,CAACX,eAAe,CAAC9X,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;UAErF,IAAI0M,UAAU,GAAG,CAAC;AAClB,UAAA,IAAIgM,MAAM,GAAG,IAAI,CAAC1S,mBAAmB,CAAChG,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;AAE1E,UAAA,KAAK,IAAImE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuU,MAAM,CAACtU,MAAM,EAAED,CAAC,EAAE,EAAE;AAChD;AACU,YAAA,KAAK,IAAIyS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,MAAM,CAACvU,CAAC,CAAC,CAAC3D,UAAU,CAAC4D,MAAM,EAAEwS,CAAC,EAAE,EAAE;AACpD,cAAA,IAAI,IAAI,CAACrX,kBAAkB,CAACwK,IAAI,CAAC2O,MAAM,CAACvU,CAAC,CAAC,CAAC3D,UAAU,CAACoW,CAAC,CAAC,CAACjW,KAAK,CAAC,EAAE;AAC/D,gBAAA,OAAO,KAAK;AAC1B;AACA;;AAEA;YACU,IAAI+X,MAAM,CAACvU,CAAC,CAAC,CAAC0E,OAAO,KAAK,QAAQ,IAAI,IAAI,CAACtJ,kBAAkB,CAACwK,IAAI,CAAC2O,MAAM,CAACvU,CAAC,CAAC,CAAC/E,SAAS,CAAC,EAAE;AACvF,cAAA,OAAO,KAAK;AACxB;AAEUsN,YAAAA,UAAU,EAAE;AACtB;AAEQ,UAAA,IAAIiF,WAAW,GAAG,IAAI,CAACrC,eAAe,CAACtP,IAAI,CAAC;UAC5C,IAAI2Y,aAAa,GAAG,IAAI,CAAChP,aAAa,CAAC3J,IAAI,CAAC,CAACoE,MAAM;UAEnD,IAAIwU,YAAY,GACb5D,GAAG,GAAG,CAAC,IAAI7J,CAAC,GAAG6J,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC5F,eAAe,CAACpP,IAAI,EAAE,QAAQ,CAAC,IACjE,CAACoY,MAAM,IAAIG,EAAE,GAAGpN,CAAE,IAClBqN,KAAK,GAAG5I,IAAI,CAACE,KAAK,CAAC3E,CAAC,GAAC,CAAC,CAAE,IACxB,CAACiN,MAAM,IAAIK,cAAc,GAAG,GAAG,IAAIE,aAAa,GAAG,EAAE,KAAK3D,GAAG,KAAK,CAAC,IAAIA,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC5F,eAAe,CAACpP,IAAI,EAAE,QAAQ,CAAE,IACzH,CAACoY,MAAM,IAAIlC,MAAM,GAAG,EAAE,IAAIvE,WAAW,GAAG,GAAI,IAC5CuE,MAAM,IAAI,EAAE,IAAIvE,WAAW,GAAG,GAAI,IACjCjF,UAAU,KAAK,CAAC,IAAIiM,aAAa,GAAG,EAAE,IAAKjM,UAAU,GAAG,CAAE;AACtE;UACQ,IAAI0L,MAAM,IAAIQ,YAAY,EAAE;AAC1B,YAAA,KAAK,IAAIjX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,IAAI,CAACoJ,QAAQ,CAAChF,MAAM,EAAEzC,CAAC,EAAE,EAAE;AAC7C,cAAA,IAAIwH,KAAK,GAAGnJ,IAAI,CAACoJ,QAAQ,CAACzH,CAAC,CAAC;AACxC;AACY,cAAA,IAAIwH,KAAK,CAACC,QAAQ,CAAChF,MAAM,GAAG,CAAC,EAAE;AAC7B,gBAAA,OAAOwU,YAAY;AACjC;AACA;YACU,IAAIC,QAAQ,GAAG7Y,IAAI,CAACqG,oBAAoB,CAAC,IAAI,CAAC,CAACjC,MAAM;AAC/D;YACU,IAAI4Q,GAAG,IAAI6D,QAAQ,EAAE;AACnB,cAAA,OAAO,KAAK;AACxB;AACA;AACQ,UAAA,OAAOD,YAAY;AAC3B;AACM,QAAA,OAAO,KAAK;AAClB,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACEvM,IAAAA,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAWpH,CAAC,EAAEwB,MAAM,EAAE;MACtC,IAAIqS,qBAAqB,GAAG,IAAI,CAACzP,YAAY,CAACpE,CAAC,EAAE,IAAI,CAAC;AACtD,MAAA,IAAI4F,IAAI,GAAG,IAAI,CAACxB,YAAY,CAACpE,CAAC,CAAC;AAC/B,MAAA,OAAO4F,IAAI,IAAIA,IAAI,IAAIiO,qBAAqB,EAAE;AAC5C,QAAA,IAAIrS,MAAM,CAAC/E,IAAI,CAAC,IAAI,EAAEmJ,IAAI,EAAEA,IAAI,CAACtE,SAAS,GAAG,GAAG,GAAGsE,IAAI,CAAC/B,EAAE,CAAC,EAAE;AAC3D+B,UAAAA,IAAI,GAAG,IAAI,CAAC5B,iBAAiB,CAAC4B,IAAI,CAAC;AAC3C,SAAO,MAAM;AACLA,UAAAA,IAAI,GAAG,IAAI,CAACxB,YAAY,CAACwB,IAAI,CAAC;AACtC;AACA;KACG;AAEH;AACA;AACA;AACA;AACA;AACA;AACE0B,IAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAWtH,CAAC,EAAE;AACzB,MAAA,IAAI8T,YAAY,GAAG,IAAI,CAAC/S,mBAAmB,CAACf,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D,MAAA,IAAI,CAAClB,YAAY,CAACgV,YAAY,EAAE,UAAS/Y,IAAI,EAAE;QAC7C,IAAIgZ,YAAY,GAAG,IAAI,CAAC5L,eAAe,CAACpN,IAAI,CAAC,GAAG,CAAC;AACjD,QAAA,IAAIgZ,YAAY,EAAE;AAChB,UAAA,IAAI,CAAClY,GAAG,CAAC,wCAAwC,EAAEd,IAAI,CAAC;AAChE;AACM,QAAA,OAAOgZ,YAAY;AACzB,OAAK,CAAC;KACH;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACE7J,IAAAA,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAWnP,IAAI,EAAE;MACrC,IAAIA,IAAI,CAAC6I,OAAO,IAAI,IAAI,IAAI7I,IAAI,CAAC6I,OAAO,IAAI,IAAI,EAAE;AAChD,QAAA,OAAO,KAAK;AAClB;MACI,IAAIsB,OAAO,GAAG,IAAI,CAACR,aAAa,CAAC3J,IAAI,EAAE,KAAK,CAAC;MAC7C,IAAI,CAACc,GAAG,CAAC,kCAAkC,EAAEqJ,OAAO,EAAE,IAAI,CAACxM,aAAa,CAAC;MACzE,OAAO,IAAI,CAAC4P,eAAe,CAAC,IAAI,CAAC5P,aAAa,EAAEwM,OAAO,CAAC,GAAG,IAAI;KAChE;AAED2E,IAAAA,aAAa,EAAE,SAAfA,aAAaA,CAAWmK,IAAI,EAAE;AAC5B,MAAA,OAAO,CAAC,IAAI,CAACtZ,MAAM,GAAGsZ,IAAI,IAAI,CAAC;KAChC;AAED/G,IAAAA,WAAW,EAAE,SAAbA,WAAWA,CAAW+G,IAAI,EAAE;MAC1B,IAAI,CAACtZ,MAAM,GAAG,IAAI,CAACA,MAAM,GAAG,CAACsZ,IAAI;KAClC;AAED/J,IAAAA,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAWlP,IAAI,EAAE;AACrC;AACI,MAAA,OAAO,CAAC,CAACA,IAAI,CAACkZ,KAAK,IAAIlZ,IAAI,CAACkZ,KAAK,CAACC,OAAO,IAAI,MAAM,MAC7C,CAACnZ,IAAI,CAACkZ,KAAK,IAAIlZ,IAAI,CAACkZ,KAAK,CAACE,UAAU,IAAI,QAAQ,CAAA,IACjD,CAACpZ,IAAI,CAACyV,YAAY,CAAC,QAAQ;AACpC;AAAA,UACU,CAACzV,IAAI,CAACyV,YAAY,CAAC,aAAa,CAAC,IAAIzV,IAAI,CAACwG,YAAY,CAAC,aAAa,CAAC,IAAI,MAAM,IAAKxG,IAAI,CAACuG,SAAS,IAAIvG,IAAI,CAACuG,SAAS,CAACI,OAAO,IAAI3G,IAAI,CAACuG,SAAS,CAACI,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAE,CAAC;KACxL;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACE2M,IAAAA,KAAK,EAAE,SAAPA,KAAKA,GAAc;AACrB;AACI,MAAA,IAAI,IAAI,CAACpV,gBAAgB,GAAG,CAAC,EAAE;QAC7B,IAAImb,OAAO,GAAG,IAAI,CAAC9b,IAAI,CAAC8I,oBAAoB,CAAC,GAAG,CAAC,CAACjC,MAAM;AACxD,QAAA,IAAIiV,OAAO,GAAG,IAAI,CAACnb,gBAAgB,EAAE;UACnC,MAAM,IAAIZ,KAAK,CAAC,6BAA6B,GAAG+b,OAAO,GAAG,iBAAiB,CAAC;AACpF;AACA;;AAEA;AACI,MAAA,IAAI,CAACvE,qBAAqB,CAAC,IAAI,CAACvX,IAAI,CAAC;;AAEzC;AACI,MAAA,IAAI+b,MAAM,GAAG,IAAI,CAACja,cAAc,GAAG,EAAE,GAAG,IAAI,CAAC0T,UAAU,CAAC,IAAI,CAACxV,IAAI,CAAC;;AAEtE;AACI,MAAA,IAAI,CAACmY,cAAc,CAAC,IAAI,CAACnY,IAAI,CAAC;MAE9B,IAAI,CAACkN,aAAa,EAAE;AAEpB,MAAA,IAAIwI,QAAQ,GAAG,IAAI,CAACiB,mBAAmB,CAACoF,MAAM,CAAC;AAC/C,MAAA,IAAI,CAAC3b,aAAa,GAAGsV,QAAQ,CAACxJ,KAAK;AAEnC,MAAA,IAAI9F,cAAc,GAAG,IAAI,CAAC8K,YAAY,EAAE;AACxC,MAAA,IAAI,CAAC9K,cAAc,EACjB,OAAO,IAAI;MAEb,IAAI,CAAC7C,GAAG,CAAC,WAAW,GAAG6C,cAAc,CAACvE,SAAS,CAAC;AAEhD,MAAA,IAAI,CAACsE,mBAAmB,CAACC,cAAc,CAAC;;AAE5C;AACA;AACA;AACI,MAAA,IAAI,CAACsP,QAAQ,CAACY,OAAO,EAAE;AACrB,QAAA,IAAI0F,UAAU,GAAG5V,cAAc,CAAC0C,oBAAoB,CAAC,GAAG,CAAC;AACzD,QAAA,IAAIkT,UAAU,CAACnV,MAAM,GAAG,CAAC,EAAE;AACzB6O,UAAAA,QAAQ,CAACY,OAAO,GAAG0F,UAAU,CAAC,CAAC,CAAC,CAACnZ,WAAW,CAACsJ,IAAI,EAAE;AAC3D;AACA;AAEI,MAAA,IAAItJ,WAAW,GAAGuD,cAAc,CAACvD,WAAW;MAC5C,OAAO;QACLqJ,KAAK,EAAE,IAAI,CAAC9L,aAAa;AACzByE,QAAAA,MAAM,EAAE6Q,QAAQ,CAAC7Q,MAAM,IAAI,IAAI,CAACxE,cAAc;QAC9C4b,GAAG,EAAE,IAAI,CAAC3b,WAAW;QACrB4b,IAAI,EAAE,IAAI,CAACxK,YAAY;AACvBkE,QAAAA,OAAO,EAAE,IAAI,CAAClU,WAAW,CAAC0E,cAAc,CAAC;AACzCvD,QAAAA,WAAW,EAAEA,WAAW;QACxBgE,MAAM,EAAEhE,WAAW,CAACgE,MAAM;QAC1ByP,OAAO,EAAEZ,QAAQ,CAACY,OAAO;AACzBE,QAAAA,QAAQ,EAAEd,QAAQ,CAACc,QAAQ,IAAI,IAAI,CAACjW,gBAAgB;QACpD8W,aAAa,EAAE3B,QAAQ,CAAC2B;OACzB;AACL;GACC;EAE+B;AAChC;IACE8E,iBAAiBzc,WAAW;AAC9B;;;;;;;;;;;;;;;;;;;;;;ACzvEA;AACA;AACA;AACA;;AAEA,EAAA,IAAIwC,OAAO,GAAG;AACd;AACA;AACEsC,IAAAA,kBAAkB,EAAE,wPAAwP;AAC5QC,IAAAA,oBAAoB,EAAE;GACvB;EAED,SAAS2X,aAAaA,CAAC3Z,IAAI,EAAE;AAC7B;AACE,IAAA,OAAO,CAAC,CAACA,IAAI,CAACkZ,KAAK,IAAIlZ,IAAI,CAACkZ,KAAK,CAACC,OAAO,IAAI,MAAM,KAC9C,CAACnZ,IAAI,CAACyV,YAAY,CAAC,QAAQ;AAClC;AAAA,QACQ,CAACzV,IAAI,CAACyV,YAAY,CAAC,aAAa,CAAC,IAAIzV,IAAI,CAACwG,YAAY,CAAC,aAAa,CAAC,IAAI,MAAM,IAAKxG,IAAI,CAACuG,SAAS,IAAIvG,IAAI,CAACuG,SAAS,CAACI,OAAO,IAAI3G,IAAI,CAACuG,SAAS,CAACI,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAE,CAAC;AACzL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA,SAASiT,oBAAoBA,CAAC1c,GAAG,EAAgB;AAAA,IAAA,IAAdC,OAAO,GAAAE,SAAA,CAAA+G,MAAA,GAAA,CAAA,IAAA/G,SAAA,CAAA,CAAA,CAAA,KAAA4Q,SAAA,GAAA5Q,SAAA,CAAA,CAAA,CAAA,GAAG,EAAE;AAC/C;AACA;AACE,IAAA,IAAI,OAAOF,OAAO,IAAI,UAAU,EAAE;AAChCA,MAAAA,OAAO,GAAG;AAAE0c,QAAAA,iBAAiB,EAAE1c;OAAS;AAC5C;AAEE,IAAA,IAAI2c,cAAc,GAAG;AAAEC,MAAAA,QAAQ,EAAE,EAAE;AAAEC,MAAAA,gBAAgB,EAAE,GAAG;AAAEH,MAAAA,iBAAiB,EAAEF;KAAe;IAC9Fxc,OAAO,GAAG8c,MAAM,CAACC,MAAM,CAACJ,cAAc,EAAE3c,OAAO,CAAC;AAEhD,IAAA,IAAIgd,KAAK,GAAGjd,GAAG,CAACgJ,gBAAgB,CAAC,iBAAiB,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACE,IAAA,IAAIkU,OAAO,GAAGld,GAAG,CAACgJ,gBAAgB,CAAC,UAAU,CAAC;IAC9C,IAAIkU,OAAO,CAAChW,MAAM,EAAE;AAClB,MAAA,IAAIiW,GAAG,GAAG,IAAIjX,GAAG,CAAC+W,KAAK,CAAC;MACxB,EAAE,CAAC9U,OAAO,CAAC3D,IAAI,CAAC0Y,OAAO,EAAE,UAAUpa,IAAI,EAAE;AACvCqa,QAAAA,GAAG,CAACC,GAAG,CAACta,IAAI,CAACqE,UAAU,CAAC;AAC9B,OAAK,CAAC;AACF8V,MAAAA,KAAK,GAAG7Z,KAAK,CAACC,IAAI,CAAC8Z,GAAG,CAAC;AAC3B;IAEE,IAAIE,KAAK,GAAG,CAAC;AACf;AACA;IACE,OAAO,EAAE,CAAC9U,IAAI,CAAC/D,IAAI,CAACyY,KAAK,EAAE,UAAUna,IAAI,EAAE;AACzC,MAAA,IAAI,CAAC7C,OAAO,CAAC0c,iBAAiB,CAAC7Z,IAAI,CAAC,EAAE;AACpC,QAAA,OAAO,KAAK;AAClB;MAEI,IAAIsM,WAAW,GAAGtM,IAAI,CAACuG,SAAS,GAAG,GAAG,GAAGvG,IAAI,CAAC8I,EAAE;AAChD,MAAA,IAAIrJ,OAAO,CAACsC,kBAAkB,CAACgI,IAAI,CAACuC,WAAW,CAAC,IAC5C,CAAC7M,OAAO,CAACuC,oBAAoB,CAAC+H,IAAI,CAACuC,WAAW,CAAC,EAAE;AACnD,QAAA,OAAO,KAAK;AAClB;AAEI,MAAA,IAAItM,IAAI,CAAC2U,OAAO,CAAC,MAAM,CAAC,EAAE;AACxB,QAAA,OAAO,KAAK;AAClB;MAEI,IAAI6F,iBAAiB,GAAGxa,IAAI,CAACI,WAAW,CAACsJ,IAAI,EAAE,CAACtF,MAAM;AACtD,MAAA,IAAIoW,iBAAiB,GAAGrd,OAAO,CAAC6c,gBAAgB,EAAE;AAChD,QAAA,OAAO,KAAK;AAClB;MAEIO,KAAK,IAAI3K,IAAI,CAAC6K,IAAI,CAACD,iBAAiB,GAAGrd,OAAO,CAAC6c,gBAAgB,CAAC;AAEhE,MAAA,IAAIO,KAAK,GAAGpd,OAAO,CAAC4c,QAAQ,EAAE;AAC5B,QAAA,OAAO,IAAI;AACjB;AACI,MAAA,OAAO,KAAK;AAChB,KAAG,CAAC;AACJ;EAEgC;AAChC;IACEL,iBAAiBE,oBAAoB;AACvC;;;;;AC1GA,IAAI3c,WAAW,GAAGyd,kBAAwB;AAC1C,IAAId,oBAAoB,GAAGe,4BAAmC;AAE9D,IAAA/O,WAAc,GAAG;AACf3O,EAAAA,WAAW,EAAEA,WAAW;AACxB2c,EAAAA,oBAAoB,EAAEA;AACxB,CAAC;;;;;", "x_google_ignoreList": [0, 1, 2]}