/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{analytics as e,events as t}from"../../common/analytics.js";import{dcLocalStorage as o,dcSessionStorage as n}from"../../common/local-storage.js";import{loggingApi as a}from"../../common/loggingApi.js";import{common as s}from"../../sw_modules/common.js";import{EXPRESS as i}from"../../sw_modules/constant.js";import{Deferred as r}from"../../sw_modules/polyfills.js";import{PromiseTimeout as c}from"../../sw_modules/TimeoutPromise.js";import{util as l}from"../../sw_modules/util.js";import{verbNameToIntent as m,imageUrlToBase64 as d,getExpressSession as p}from"../../sw_modules/express.js";(async()=>{const g="expressPluginIframe";let E,u,h,f,w,_,T,D,S,x,b,y,I,A,v,R,U,L=["image/jpeg","image/png","image/webp"];function M(){const e=document.getElementById("loaderId");e&&e.parentNode&&e.parentNode.removeChild(e)}function C(){chrome.runtime.sendMessage({main_op:"removeSessionFromUnloadedExpressSessions",touchpoint:_?.touchpoint,domain:w})}function O(e){let t=chrome.runtime.getURL("browser/js/failToast.html");e&&(t+="?errorType="+e);var o=$("#expressAcrobatExtensionIframe");0===o.length?(o=$("<iframe>")).attr("id","expressAcrobatExtensionIframe").css({border:"0px","z-index":2147483647,position:"fixed",width:"525px",height:"50px",display:"block",margin:"auto",background:"transparent","color-scheme":"auto",bottom:"50px",right:"calc(50% - 250px)","border-radius":"4px"}).attr("src",t).appendTo("html"):"none"===o.css("display")&&o.css({display:"block"})}function P(o){o?.includes("Cannot access contents of url")&&(o="Error: Cannot access contents of url. Extension manifest must request permission to access this host."),C(),e.event(t.EXPRESS_EXECUTE_VERB_FAILED,{VERB:f,eventContext:o,expressTouchpoint:_.touchpoint,domain:w}),a.error({message:"Error received in express flow",error:o})}function B(e,t){const n=[{id:"extension_download",label:l.getTranslation("expressExportOptionsDownloadLabel"),action:{target:"publish",closeTargetOnExport:!1},style:{uiType:"button"}}],a={gmailNativeViewer:"gmail",whatsappPreview:"whatsapp",whatsappHover:"whatsapp_hover_cta",ContextMenu:"webpage",googleImagePreview:"google-image",gmailMessageView:"gmail_hover_cta"}[_.touchpoint]||"Unknown",r=t.sort();return{type:"LAUNCH_MODULE",data:{hostInfo:{clientId:s.getViewerIMSClientId(),appName:"Adobe Acrobat Extension"},intent:"edit-image",component:"MODULE",configParams:{locale:l.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale")),env:s.getEnv()},appConfig:{allowedFileTypes:["image/png"],metaData:{touStatus:o.getItem(i.TOU_ACCEPTED),workflowStartTimeStamp:T},publishModalTitle:l.getTranslation("expressPublishModalTitle"),inlineTOUConsent:!0,openPaywallInNewTab:!0,analyticsData:{hostAppTrigger:a,hostAnalyticsTestIds:r},convertUnsupportedImages:!0,appVersion:"2"},containerConfig:{showExpressIconWithText:!0,showDarkerBackgroundForLoader:!1},docConfig:e,exportConfig:n}}}function k(){const o=chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:"dc-cv-express-nba"}),n=chrome.runtime.sendMessage({main_op:"getExperimentCodes"});Promise.all([h,o,n]).then((([o,n,s])=>{let r=o.base64data;D=o.imageDownloadTime,S=r.length;const c={"data:image/jpeg;":["data:binary/octet-stream;","data:application/octet-stream;","data:image/jpg;"],"data:image/png;":["data:img/png;"]};for(let e of Object.keys(c))for(let t of c[e])r=r.replace(t,e);const l=r.substring(r.indexOf(":")+1,r.indexOf(";"));if(function(e){return L.includes(e)}(l)){const e={asset:{data:r,dataType:"base64",type:"image"}};f!==i.VERBS.EDIT_IMAGE&&(e.intent=m(f));let t=B(e,s);n&&(t.data.appConfig.showNextBestActionDialog=!0);const o=document.getElementById(g);x=Date.now()-T,o.contentWindow.postMessage(t,u.origin)}else C(),M(),O("unsupportedFileType"),e.event(t.EXPRESS_UNSUPPORTED_FILE_TYPE,{VERB:f,eventContext:l,domain:w,expressTouchpoint:_.touchpoint}),a.error({message:"Error received in express flow",error:"Unsupported image type",fileType:l})})).catch((e=>{M(),O(),P(e.toString())}))}try{await o.init(),await n.init(),function(){const e=o.getItem("env");s.reset(e),E=s.getExpressURLs().pluginUrl,u=new URL(E);const t=new URLSearchParams(window.location.search).get("sessionId");if(_=p(t),!_)throw new Error("invalid sessionId");h=d(_.imageURL),f=_.intent,w=_.pageDomain,T=_.clickTimeStamp}(),window.addEventListener("message",(n=>{try{if(n.origin===u.origin)switch(n.data?.type){case"EMBED_SDK_ON_LOAD_INIT":M(),document.getElementById(g).focus();break;case"EMBED_SDK_ON_LOAD":document.getElementById(g).focus();break;case"EMBED_SDK_EVENT":switch(n.data.data?.type){case"ASSET_LOADED":C();const s=Date.now()-T,r={loadTime:s,imageDownloadTime:D,timeRequiredToHandoverToExpress:x,expressPluginLoadingTime:y,imageSize:S,VERB:f,domain:w,expressTouchpoint:_.touchpoint},c=i.PERF_METRIC_LOG_MESSAGE;"true"===o.getItem("adobeInternal")&&(console.log("Express Plugin Loading Time "+y),console.log("Express Supported Image Types Fetching Time "+A),console.log("Image Download Time "+D),console.log("Time required to handover to express "+x),console.log(c+" "+s)),a.info({message:c,...r}),e.event(t.EXPRESS_EXECUTE_VERB_EDITOR_ASSET_LOADED,r);break;case"SET_TOU_STATE":o.setItem(i.TOU_ACCEPTED,JSON.stringify(n.data?.data?.data?.data))}break;case"EMBED_SDK_READY":R.resolve(),y=Date.now()-b,I=Date.now(),document.getElementById(g).contentWindow.postMessage({type:"REQUEST_MODULE_SUPPORTED_IMAGE_TYPES",data:{convertUnsupportedImages:!0}},u.origin),U=r(),v=c(U.promise(),1e3),v.catch((e=>{P("unsupported file types fetching flow "+e.toString()),k()}));break;case"MODULE_SUPPORTED_IMAGE_TYPES":U.resolve(),A=Date.now()-I,L=n.data.data??L,k();break;case"EMBED_SDK_PUBLISH":const s=n.data.data?.asset[0];if(s)try{const e="image."+s.fileType?.split("/")[1];!function(e,t,o){let n=e.split(",")[1],a=atob(n),s=new ArrayBuffer(a.length),i=new Uint8Array(s);for(let e=0;e<a.length;e++)i[e]=a.charCodeAt(e);let r=new Blob([s],{type:o}),c=URL.createObjectURL(r);chrome.downloads.download({url:c,filename:t,conflictAction:"uniquify"},(function(e){chrome.downloads.onChanged.addListener((function t(o){o.id===e&&o.state&&"complete"===o.state.current&&(c&&URL.revokeObjectURL(c),chrome.downloads.onChanged.removeListener(t))})),chrome.runtime.lastError&&P(chrome.runtime.lastError)}))}(s.data,e,s.fileType)}catch(e){P("Save Image error: "+e.toString())}else P("Save Image error: No asset data found while publishing");break;case"EMBED_SDK_CANCEL":chrome.runtime.sendMessage({main_op:"closeExpressApp"});break;case"EMBED_SDK_ERROR":M(),O(),P("EMBED_SDK_ERROR "+n.data.data?.message)}}catch(e){M(),O(),P("Express Message Listener "+e.toString())}})),function(){b=Date.now();const e=document.createElement("iframe");e.setAttribute("src",E),e.setAttribute("id",g),e.setAttribute("allowfullscreen","true"),e.setAttribute("style","position:fixed; top:0; left:0; bottom:0; right:0; width:100%; height:100%; border:none; margin:0; padding:0; overflow:hidden; z-index:999999;"),document.body.appendChild(e),R=r(),v=c(R.promise(),1e4),v.catch((e=>{M(),O(),P("embed sdk ready flow "+e.toString())}))}()}catch(e){M(),O(),P("Init Express Content Script "+e.toString())}})();