/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
const sendAnalytics=e=>{try{chrome.runtime.sendMessage({main_op:"analytics",analytics:e})}catch(e){}},formatDateForMonthlyAnalyticsEvent=e=>e instanceof Date?`${e.getUTCFullYear()}${(e.getUTCMonth()+1).toString().padStart(2,"0")}`:"",eventsSent=new Set,sendAnalyticsOnce=e=>{eventsSent?.has(e)||(eventsSent.add(e),sendAnalyticsEvent([e]))},analyticsEvents=new Map,sendAnalyticsOncePerMonth=async(e,t)=>{if(e)try{if(!analyticsEvents?.has(e)){const n=new Date,s=formatDateForMonthlyAnalyticsEvent(n);analyticsEvents.set(e,s);const r=await chrome.storage.local.get([e]),o=r?.[e]?.lastSentYearMonth;if(!o||s>o){sendAnalytics(t?[[e,t]]:[e]);const n={lastSentYearMonth:s};chrome.storage.local.set({[e]:n})}}}catch(e){}},getDefaultViewershipStatusEventParam=(e,t,n,s=!1)=>{if(s){let s=n+"DV-"+(t?"Treatment":"Control");return s+="-"+(e?"True":"False"),{experimentEnablementStatus:s}}return{}},getDVSessionCountStorageKey=e=>`${e}-pdf-default-viewership-session-count`,getDVStorageKey=e=>`${e}-pdf-default-viewership`,resetDVSessionCount=e=>{chrome.storage.local.set({[getDVSessionCountStorageKey(e)]:0})},incrementDVSessionCount=e=>{getDVSessionCount(e).then((t=>{chrome.storage.local.set({[getDVSessionCountStorageKey(e)]:t+1})}))},getDVSessionCount=e=>chrome.storage.local.get(getDVSessionCountStorageKey(e)).then((t=>t?.[getDVSessionCountStorageKey(e)]||0)),getDVSessionCountString=e=>getDVSessionCount(e).then((e=>e<5?e.toString():"5_or_more")),fetchDefaultViewershipConfig=async e=>(await chrome.storage.local.get(getDVStorageKey(e)))?.[getDVStorageKey(e)]||{},createAcrobatIconElement=(e,t)=>{const n=document.createElement("img"),s=chrome.runtime.getURL(t);return n.setAttribute("src",s),n.setAttribute("class",e),n},isAnalyticsSentInTheMonthOrSession=e=>analyticsEvents?.has(e),sendErrorLog=(e,t)=>{chrome.runtime.sendMessage({main_op:"log-error",log:{message:e,error:t}})},getParsedJSON=e=>{let t;try{t=JSON.parse(e)}catch(e){}return t},extractFileIdFromDriveUrl=e=>{let t="";if(!e)return t;try{const n=decodeURIComponent(e);if(n.startsWith("https://drive.google.com/")||n.startsWith("https://docs.google.com/file"))t=n.split("/")[5]||new URL(n).searchParams.get("id");else if(n.startsWith("https://www.google.com/url")){const e=new URL(n).searchParams.get("q");if(e){t=new URL(e).pathname.split("/")[3]}}}catch(e){sendErrorLog("Error in GSuite","Error in extracting file ID from URL")}return t},getElementListForSelectors=(e=[],t=null)=>{const n=new Set,s=t||document;for(const t of e){const e=s.getElementsByClassName(t);if(e?.length>0){const t=Array.from(e);t?.forEach((e=>n.add(e)))}}return Array.from(n)};export{createAcrobatIconElement,sendAnalytics,sendAnalyticsOnce,sendAnalyticsOncePerMonth,getDefaultViewershipStatusEventParam,isAnalyticsSentInTheMonthOrSession,sendErrorLog,getParsedJSON,extractFileIdFromDriveUrl,getDVSessionCountString,resetDVSessionCount,incrementDVSessionCount,fetchDefaultViewershipConfig,getElementListForSelectors};