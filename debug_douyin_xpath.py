#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试抖音XPath修复
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.platform_publishers import DouyinPublisher
from src.services.new_one_click_publisher import VideoInfo
from src.utils.logger import logger

def debug_douyin_publisher():
    """调试抖音发布器"""
    print("🔍 调试抖音发布器")
    print("=" * 50)
    
    try:
        # 创建发布器
        print("1. 创建抖音发布器...")
        publisher = DouyinPublisher()
        print("✅ 发布器创建成功")
        
        # 检查准备状态
        print("\n2. 准备发布环境...")
        prepare_result = publisher.prepare()
        print(f"   准备结果: {prepare_result}")
        
        if not prepare_result:
            print("❌ 发布环境准备失败")
            return False
        
        print("✅ 发布环境准备成功")
        
        # 等待用户确认登录
        print("\n3. 等待用户登录...")
        print("💡 请在Firefox浏览器中登录抖音创作者中心")
        print("💡 登录完成后按回车键继续...")
        input()
        
        # 检查登录状态
        print("\n4. 检查登录状态...")
        login_result = publisher._check_login()
        print(f"   登录检查结果: {login_result}")
        
        if not login_result:
            print("❌ 登录状态检查失败")
            return False
        
        print("✅ 登录状态检查通过")
        
        # 导航到上传页面
        print("\n5. 导航到上传页面...")
        try:
            upload_url = "https://creator.douyin.com/creator-micro/content/upload"
            publisher.browser.driver.get(upload_url)
            time.sleep(5)
            print(f"✅ 已导航到: {upload_url}")
        except Exception as e:
            print(f"❌ 导航失败: {e}")
            return False
        
        # 测试XPath选择器
        print("\n6. 测试修复后的XPath选择器...")
        upload_selectors = [
            '//input[@type="file"]',
            "//input[contains(@accept, 'video')]",
            '//input[contains(@class, "upload")]',
            '//input[contains(@accept, "mp4")]',
            '//input[contains(@accept, "mov")]'
        ]
        
        found_elements = 0
        for i, selector in enumerate(upload_selectors, 1):
            try:
                element = publisher.browser.find_element(selector, timeout=3)
                if element:
                    print(f"✅ 选择器 {i} 找到元素: {selector}")
                    found_elements += 1
                    
                    # 检查元素属性
                    try:
                        accept_attr = element.get_attribute('accept')
                        class_attr = element.get_attribute('class')
                        print(f"   - accept属性: {accept_attr}")
                        print(f"   - class属性: {class_attr}")
                    except:
                        pass
                else:
                    print(f"⚠️ 选择器 {i} 未找到元素: {selector}")
            except Exception as e:
                print(f"❌ 选择器 {i} 异常: {selector} - {e}")
        
        if found_elements > 0:
            print(f"\n🎉 成功找到 {found_elements} 个上传元素")
            print("✅ XPath修复成功！")
            return True
        else:
            print("\n❌ 未找到任何上传元素")
            print("💡 可能需要进一步调试页面结构")
            
            # 输出页面信息用于调试
            try:
                current_url = publisher.browser.driver.current_url
                page_title = publisher.browser.driver.title
                print(f"\n📋 调试信息:")
                print(f"   当前URL: {current_url}")
                print(f"   页面标题: {page_title}")
                
                # 查找所有input元素
                all_inputs = publisher.browser.driver.find_elements("xpath", "//input")
                print(f"   页面input元素数量: {len(all_inputs)}")
                
                for i, inp in enumerate(all_inputs[:5]):  # 只显示前5个
                    try:
                        inp_type = inp.get_attribute('type')
                        inp_accept = inp.get_attribute('accept')
                        inp_class = inp.get_attribute('class')
                        print(f"   input[{i}]: type={inp_type}, accept={inp_accept}, class={inp_class}")
                    except:
                        pass
                        
            except Exception as e:
                print(f"   调试信息获取失败: {e}")
            
            return False
        
    except Exception as e:
        print(f"❌ 调试过程中出现异常: {e}")
        logger.error(f"抖音发布器调试异常: {e}", exc_info=True)
        return False
    
    finally:
        # 清理资源
        try:
            if 'publisher' in locals():
                print("\n🧹 清理资源...")
                publisher.cleanup()
                print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理异常: {e}")

def main():
    """主函数"""
    print("🚀 抖音XPath修复调试工具")
    print("=" * 50)
    
    result = debug_douyin_publisher()
    
    if result:
        print("\n🎉 调试成功！XPath修复有效")
        print("💡 现在可以正常使用抖音发布功能了")
        return 0
    else:
        print("\n❌ 调试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    exit(main())
