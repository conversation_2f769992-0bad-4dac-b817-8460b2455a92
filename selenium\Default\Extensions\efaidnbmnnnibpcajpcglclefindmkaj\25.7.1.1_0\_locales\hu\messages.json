{"ConvertVerbDescription": {"message": "Dokumentumok átalakítása PDF-fájlból vagy PDF-fájllá."}, "Disable": {"message": "Kikapcsolás"}, "EditVerbDescription": {"message": "Oldalak átrendezése, elforgatása vagy törlése a PDF-ben."}, "Enable": {"message": "Engedélyezés"}, "LearnMoreURL": {"message": "https://helpx.adobe.com/hu/acrobat/kb/acrobat-pro-aip-for-chrome.html"}, "LearnMoreURLEdge": {"message": "https://helpx.adobe.com/hu/acrobat/kb/acrobat-pro-aip-for-edge.html"}, "acrobatGdriveTripleDotMenuTouchPointText": {"message": "Open with <PERSON><PERSON><PERSON><PERSON>"}, "acrobatGmailFteToolTipButton": {"message": "Meg<PERSON><PERSON>tte<PERSON>"}, "acrobatGmailFteToolTipDescription": {"message": "Edit, convert, comment & sign - right in Chrome with <PERSON><PERSON><PERSON>bat"}, "acrobatGmailFteToolTipTitle": {"message": "Open this PDF in Acrobat"}, "acrobatGsuiteFteToolTipButton": {"message": "Meg<PERSON><PERSON>tte<PERSON>"}, "acrobatGsuiteFteToolTipButtonDV": {"message": "OK"}, "acrobatGsuiteFteToolTipButtonNonDV": {"message": "OK"}, "acrobatGsuiteFteToolTipDescription": {"message": "Edit, convert, comment & sign - right in Chrome with <PERSON><PERSON><PERSON>bat"}, "acrobatGsuiteFteToolTipDescriptionDV": {"message": "Try PDF tools like edit, comment, convert, and e-sign from $surfaceName$ by making Acrobat your default PDF viewer.", "placeholders": {"surfaceName": {"content": "$1"}}}, "acrobatGsuiteFteToolTipDescriptionNonDV": {"message": "Try Acrobat PDF tools like edit, comment, convert, and e-sign — right from $surfaceName$.", "placeholders": {"surfaceName": {"content": "$1"}}}, "acrobatGsuiteFteToolTipTitle": {"message": "Open this PDF in Acrobat"}, "acrobatGsuiteFteToolTipTitleDV": {"message": "Open all PDFs in Adobe Acrobat"}, "acrobatGsuiteFteToolTipTitleNonDV": {"message": "Open this PDF in Adobe Acrobat"}, "acrobatGsuiteTouchPointPreferenceDescription": {"message": "Add Acrobat icons to Gmail and Google Drive to quickly open PDFs in Acrobat and access PDF tools."}, "acrobatGsuiteTouchPointPreferenceTitle": {"message": "Integrate Acrobat into Gmail and Google Drive"}, "acrobatTouchPointInOtherSitesPreferenceDescription": {"message": "Integrate Acrobat with websites, including Gmail and Google Drive, for easy access to PDF tools."}, "acrobatTouchPointInOtherSitesPreferenceTitle": {"message": "Show Open in Acrobat icon in other websites"}, "acrobatTouchPointInOtherSurfacesPreferenceDescription": {"message": "Integrate Acrobat with websites, including Gmail, Google Drive, and Outlook for easy access to PDF tools."}, "adobeYoloErrorMessage": {"message": "Sajnáljuk, ez a link érvénytelen"}, "adobeYoloMessage": {"message": "Bejelentkezés az Acrobatba..."}, "aiAcBody": {"message": "Quickly summarize webpages and get key insights using Acrobat AI Assistant."}, "aiAcHeader": {"message": "Do more with your Acrobat extension"}, "aiMarkerTouchpointPreferenceDescription": {"message": "Always display AI Assistant menu when hovering over PDFs on webpages."}, "aiMarkerTouchpointPreferenceTitle": {"message": "AI Assistant contextual menu"}, "analyticsOptinTitle": {"message": "Felhasználási adatok küldése"}, "appearancePrefDesc": {"message": "Váltás sötét és világos témák között"}, "appearancePrefOp1": {"message": "Rendszerbeállítás <PERSON>"}, "appearancePrefOp2": {"message": "Dark mode"}, "appearancePrefOp3": {"message": "Light mode"}, "appearancePrefTitle": {"message": "<PERSON><PERSON><PERSON><PERSON> mód be<PERSON>"}, "askAssistantBannerContentText": {"message": "Get fast summaries and key insights with AI Assistant on webpages."}, "askAssistantBannerTitle": {"message": "AI Assistant for webpages"}, "canQuesBriefOverview": {"message": "Provide a brief overview of this article"}, "canQuesCreateFAQ": {"message": "Create an FAQ list based on this info"}, "canQuesDraftEmail": {"message": "Draft an email summarizing the key points"}, "canQuesImpTopics": {"message": "List all important topics and keywords"}, "canQuesSummarise": {"message": "Summarize this article"}, "closeButton": {"message": "Bezárás"}, "cropImageContextMenu": {"message": "Crop Image"}, "dcWebBookmarkTitle": {"message": "Adobe Acrobat kezdőlap"}, "doNotShowButton": {"message": "Máskor ne jelenjen meg"}, "domainPreferenceTitle": {"message": "Websites where AI Assistant FAB is hidden"}, "downloadBannerContinue": {"message": "Turn on"}, "downloadBannerText": {"message": "Open all downloaded PDFs in Acrobat by setting the app as default PDF viewer in Chrome"}, "editImageContextMenu": {"message": "<PERSON><PERSON><PERSON>"}, "editMegaVerbText": {"message": "Szerkesztés"}, "effectsImageContextMenu": {"message": "Apply Effects"}, "embeddedPDFHideIconForNow": {"message": "Hide icon for now"}, "embeddedPDFOpenInAcrobatTooltip": {"message": "Open in Acrobat"}, "embeddedPDFOpenPDFInAcrobat": {"message": "Open PDF in Acrobat"}, "embeddedPDFTouchPointFTEBody": {"message": "Get instant access to your PDF tools like comment and e-sign by opening in Acrobat. Hide this shortcut anytime in Preferences."}, "embeddedPDFTouchPointFTEHeader": {"message": "Open in Acrobat"}, "enableGenAIFeaturesDescription": {"message": "Your file will be processed in the cloud to deliver a generative AI capability when you use a generative AI feature. Turning off this feature prevents your file from being processed and disables generative AI. You can reactivate it anytime."}, "enableGenAIFeaturesTitle": {"message": "Enable Generative AI features in Acrobat"}, "expressContextualFteCtaLabel": {"message": "Close"}, "expressContextualFteDescription": {"message": "Easily crop, remove the background, and more. Powered by Adobe Express."}, "expressContextualFteTitle": {"message": "Edit this image in $surfaceName$", "placeholders": {"surfaceName": {"content": "$1"}}}, "expressDropdownMenuTitle": {"message": "Adobe Acrobat"}, "expressDropdownMenuTooltip": {"message": "Easily crop, remove background and more. Powered by Adobe Express."}, "expressEditImageParentContextMenu": {"message": "Edit Image"}, "expressEditImagePoweredByExpressContextMenu": {"message": "Powered by Adobe Express"}, "expressExportOptionsDownloadLabel": {"message": "Letöltés"}, "expressExportOptionsSaveLabel": {"message": "Men<PERSON>s"}, "expressFTEDescription": {"message": "Right click to crop, remove background, or reimagine an image using AI."}, "expressFTEFooter": {"message": "Via <b>Adobe Acrobat</b>"}, "expressFTETitle": {"message": "Edit image with Adobe Express"}, "expressPopoverFTEDescription": {"message": "Right-click on any online image to quickly remove the background, crop, or reimagine it using AI with Adobe Express"}, "expressPopoverFTETitle": {"message": "Edit images right in your browser"}, "expressPublishModalTitle": {"message": "Downloading image..."}, "expressTouchpointPreferenceDescription": {"message": "Adobe Acrobat extension lets you edit images with Adobe Express. Turning off this feature will remove this capability. You can reactivate it any time."}, "expressTouchpointPreferenceTitle": {"message": "Enable Adobe Express actions in Acrobat"}, "extensionMenuTitle": {"message": "Acrobat bővítmény – Parancsikon az összes PDF-eszközhöz"}, "failToastText": {"message": "An unexpected error has occurred, please try again later."}, "fteOwnershipSideRedCardContent": {"message": "Megjegyzések ho<PERSON>ad<PERSON>a, űrlapok kitöltése, aláírás és egyéb lehetőségek közvetlenül a Chrome böngészőben, az Acrobat alapértelmezett PDF-megtekintőként való beállítását követően."}, "fteSideCardPrimaryHeader": {"message": "Acrobat <PERSON><PERSON><PERSON>"}, "fteSideCardSecondaryHeader": {"message": "közvetlenül a Chrome böngészőben"}, "gDriveDVPAppListCoachMarkContent": {"message": "Enable the checkbox 'Use by default' to directly open Google Drive PDFs in Acrobat from next time"}, "gDriveDVPCoachMarkTitle": {"message": "Set A<PERSON><PERSON>bat as default viewer"}, "gDriveDVPManageAppCoachMarkContent": {"message": "Kattintson az „Alkalmazások kezelése” lehetőségre a PDF-ek megtekintéséhez, szerkesztéséhez vagy megjegyzésekkel való ellátásához az összes Google Drive-PDF egyidőben történő megnyitásával az Acrobatban"}, "gSuiteDVPAddOnDefaultDesc": {"message": "Acrobat Add On is now set as default"}, "genAIFabPreferenceDescription": {"message": "Always display Al Assistant button at the edge of your browser window."}, "genAIFabPreferenceTitle": {"message": "Al Assistant button"}, "generativeFillImageContextMenu": {"message": "Generative Fill"}, "genericDVPCoachMarkContent": {"message": "Click on 'Manage Apps', then scroll down to Acrobat Add On and click on 'Use by default' checkbox"}, "getInsight": {"message": "Get insights with Acrobat AI Assistant"}, "getSummary": {"message": "Summarize PDF"}, "gmailPromptFTETooltip": {"message": "Open PDFs in Acrobat to use tools like Edit, Sign and more"}, "gsuiteEditWithAcrobat": {"message": "Edit with <PERSON><PERSON><PERSON><PERSON>"}, "gsuiteOpenWithAcrobat": {"message": "Megnyitás az Acrobatban"}, "hideFabForDomain": {"message": "Disable for this site"}, "hideForNow": {"message": "Hide for now"}, "hidePopup": {"message": "Hide for this session"}, "insertObjectImageContextMenu": {"message": "Insert Object"}, "localFileBlockingPageOpenInChrome": {"message": "Open this file in Chrome"}, "localFileBlockingPageTitle": {"message": "Enable Acrobat Extension"}, "localFileOptionDesc": {"message": "Biztosítsa a hozzáférést az Acrobat bővítmény számára, hogy hozzáférjen helyi fájljai URL-címeihez, és így minden PDF-jét az Acrobat programban, a Chrome böngészőben nyithassa meg."}, "localFileOptionTitle": {"message": "Helyi PDF-ek megnyitása az Acrobatban"}, "localFilePromptContinueButton": {"message": "Continue"}, "localFilePromptDescription": {"message": "Let A<PERSON><PERSON>bat access files from this device for immediate access to all your tools so you can edit PDFs, convert files, and e-sign in a snap."}, "localFilePromptHeader": {"message": "Open in Acrobat to edit, sign, and more"}, "localFteContinue": {"message": "Ugrás a beállításokra"}, "localFteDescription": {"message": "Hozza ki a legtöbbet az Acrobat bővítményből, és használja azt minden PDF megnyitására. Kapcsolja be a Hozzáférés engedélyezése a fájl URL-ekhez lehetőséget a bővítménybeállításokban."}, "localFteDontShowAgainText": {"message": "Máskor ne jelenjen meg"}, "localFteTitle": {"message": "Váltson Acrobatra a helyi fájlok megnyitásához"}, "neverDisplayFab": {"message": "Never display"}, "openInAcrobat": {"message": "Open In Acrobat"}, "openSettings": {"message": "Manage in settings"}, "optionsFailurePrompt": {"message": "<PERSON><PERSON> menteni a módosításokat. K<PERSON><PERSON><PERSON><PERSON>k, próbálja új<PERSON> később"}, "optionsSuccessPrompt": {"message": "Beállítása sikeresen mentve"}, "optionsSuccessPromptReloadRequired": {"message": "Preference would be reflected only after reload"}, "outlookErrorToastDescription": {"message": "Something went wrong. Please close the file and try again."}, "outlookErrorToastTitle": {"message": "Unable to open file"}, "outlookPDFTouchPointFTEBody": {"message": "Edit, summarize, and sign PDFs by opening them in Acrobat."}, "outlookPDFTouchPointFTEHeader": {"message": "Open this PDF in Adobe Acrobat"}, "pdfOwnerShipDmbConfirmBtn": {"message": "Folytatás"}, "pdfOwnerShipSideRedCardConfirmBtn": {"message": "Folytatás"}, "pdfOwnershipDefSideCardcontent": {"message": "Használja az Adobe Acrobat programot alapértelmezett PDF-megtekintőként a PDF-ek megtekintéséhez, megjegyzések hozzáfűzéséhez, űrlapok kitöltéséhez, aláíráshoz és egyebekhez."}, "pdfOwnershipExploreAcrobat": {"message": "Acrobat használata a Chrome-ban"}, "pdfOwnershipExploreAcrobatEdge": {"message": "Acrobat használata a Microsoft Edge-ben"}, "pdfOwnershipExploreOptionsDescription": {"message": "Make Acrobat the default PDF viewer for your browser for quick access to commenting, Fill & Sign, and other PDF tools."}, "pdfOwnershipExploreOptionsTitle": {"message": "Open all PDFs in Acrobat"}, "pdfOwnershipPromptCancel": {"message": "Most nem"}, "pdfOwnershipPromptContent": {"message": "<PERSON><PERSON><PERSON><PERSON> el tö<PERSON>t PDF-dokumentumaival az Adobe Acrobat alkalmazás alapértelmezett PDF-megtekintőként való használatával"}, "pdfOwnershipPromptContent2": {"message": "Állítsa be az Acrobatot alapértelmezett PDF-megtekintőként, így közvetlenül a Google Chromeban dolgozhat a PDF-ekkel. Megjegyzések hozzáadása, kit<PERSON>ltés, aláírás, valamint konvertálás és tömörítés."}, "pdfOwnershipPromptContentEdge": {"message": "<PERSON><PERSON><PERSON><PERSON> el tö<PERSON>t PDF-dokumentumaival a Microsoft Edge-ben, az Adobe Acrobat alkalmazás alapértelmezett PDF-megtekintőként való <PERSON>."}, "pdfOwnershipPromptDeny": {"message": "Nem, köszönöm"}, "pdfOwnershipPromptOk": {"message": "Beállítás alapértelmezettként"}, "pdfOwnershipPromptOkFullscreen": {"message": "Próbálja ki most"}, "popupNoConnection": {"message": "<PERSON><PERSON><PERSON>"}, "popupOfflineMessage": {"message": "A kapcsolat folytatásához forduljon a rendszergazdához"}, "preferences": {"message": "Preferences"}, "removeBackgroundImageContextMenu": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "removeObjectImageContextMenu": {"message": "Remove Object"}, "saveLocationOp1": {"message": "Mindig kérdezzen rá"}, "saveLocationOp2": {"message": "Adobe felhőalapú tárhely"}, "saveLocationOp3": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "saveLocationPreferenceDescription": {"message": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy helyet fájl<PERSON>i men<PERSON>"}, "saveLocationPreferenceTitle": {"message": "<PERSON><PERSON><PERSON><PERSON>"}, "sharepointPreferenceMessage": {"message": "Engedélyezi a SharePoint-fájlkivételt az Acrobat programban."}, "sidepanelLocationPreferenceButtonText": {"message": "Change in browser settings"}, "sidepanelLocationPreferenceDescription": {"message": "Place the side panel on the left or right side of the browser window. Side panel is only available in Google Chrome 11.4 or later."}, "sidepanelLocationPreferenceTitle": {"message": "AI Assistant panel location"}, "stripTextOpenAcrobat": {"message": "Megnyitás az Acrobatban"}, "stripTextOpenFillnSign": {"message": "Kitöltés és aláírás az Acrobatban"}, "successToastText": {"message": "PDF downloads will now open in Adobe Acrobat"}, "surfaceNameGdrive": {"message": "Google Drive"}, "surfaceNameGmail": {"message": "Gmail"}, "tooltipTextDisabled": {"message": "AI Assistant isn't available for this webpage"}, "tooltipTextEnabled": {"message": "Get summaries and key insights using AI Assistant"}, "tooltipTextSubText": {"message": "powered by Adobe Acrobat"}, "unsupportedFileTypeToastText": {"message": "This file type isn’t supported. You can try editing another file."}, "userLocale": {"message": "Felhaszná<PERSON>ó területi beállítása"}, "web2pdfAddButtonText": {"message": "Hozzáadás PDF-hez"}, "web2pdfAlwaysShow": {"message": "PDF-eknél automatikus megjelenítés"}, "web2pdfAppendLinkContextMenu": {"message": "Hivatkozási cél hozzáfűzése meglévő PDF-fájlhoz"}, "web2pdfAppendPageContextMenu": {"message": "Weboldal hozzáadása meglévő PDF-fájlhoz…"}, "web2pdfBadVersion": {"message": "A Chrome ezen verziója nem támogatja az Adobe Acrobatot. A szükséges legkisebb verziószám: $version$", "placeholders": {"version": {"content": "$1"}}}, "web2pdfBadVersionEdge": {"message": "A Edge ezen verziója nem támogatja az Adobe Acrobat alkalmazást. A szükséges legkisebb verziószám: $version$", "placeholders": {"version": {"content": "$1"}}}, "web2pdfClickToOpen": {"message": "Kattintson az alábbi hivatkozásra a PDF Acrobatban való megnyitásához."}, "web2pdfConvertButtonToolTip": {"message": "Acrobat Extension - Shortcut to all your PDF tools."}, "web2pdfConvertButtonToolTipERPReader": {"message": "Az Acrobat Reader bővítmény jelenleg nincs engedélyezve. Kérjük, forduljon a rendszergazdához"}, "web2pdfConvertButtonToolTipReader": {"message": "PDF megnyitása az Acrobat Readerben"}, "web2pdfConvertLinkContextMenu": {"message": "Hivatkozási cél konvertálása Adobe PDF formátumra"}, "web2pdfConvertPageContextMenu": {"message": "Weblap konvertálása Adobe PDF formátumúra..."}, "web2pdfCopyText": {"message": "Másolás az Acrobatba"}, "web2pdfDestinationText": {"message": "C<PERSON>lmappa:"}, "web2pdfDoneButtonText": {"message": "<PERSON><PERSON><PERSON>"}, "web2pdfExtnDescription": {"message": "Acrobat használata a böngészőben a PDF-ek megtekintéséhez, konvertálásá<PERSON>z, tömörítéséhez és aláírásához"}, "web2pdfExtnDescriptionBeta": {"message": "Acrobat használata a böngészőben a PDF-ek megtekintéséhez, konvertálásá<PERSON>z, tömörítéséhez és aláírásához"}, "web2pdfExtnDescriptionChrome": {"message": "Végezzen el még több feladatot az Adobe Acrobat PDF-eszközök segítségével a Google Chrome böngészőben – megtekintés, kitöltés, megjegyzés hozzáadása, aláírás, valamint konvertáló- és tömörítőeszközök."}, "web2pdfExtnDescriptionEdge": {"message": "Még több lehetőség a Microsoft Edge böngészővel és az Adobe Acrobat PDF-eszközökkel: megjegyzés, aláírás, szerkesztés, konvertálás."}, "web2pdfExtnName": {"message": "Adobe Acrobat: PDF-ek <PERSON>se, konvert<PERSON><PERSON>ása, aláírási eszközök"}, "web2pdfExtnNameBeta": {"message": "Adobe Acrobat Beta"}, "web2pdfExtnNameEdge": {"message": "Adobe Acrobat: PDF-ek <PERSON>se, konvert<PERSON><PERSON>ása, aláírási eszközök"}, "web2pdfExtnNameStage": {"message": "Adobe Acrobat Stage"}, "web2pdfFileLockedError": {"message": "Fájl mentése sikertelen: egy másik folyamat has<PERSON> a fájlt."}, "web2pdfFilename": {"message": "Fájlnév:"}, "web2pdfFrictionlessToggleDescription": {"message": "Az Adobe Acrobat bővítmény PDF-eszközöket javasol a feladat megoldásához az Ön keresései alapján. A folyamat az eszközön zajlik, a keresések nem kerülnek továbbításra az Adobe felé."}, "web2pdfFrictionlessUrl": {"message": "https://www.adobe.com/go/fl_chromeext_hu"}, "web2pdfFrictionlessUrlEdge": {"message": "https://www.adobe.com/hu/acrobat/online.html?x_api_client_id=edge_extension&x_api_client_location=trefoil&mv=other&mv2=edge_extension:trefoil&trackingid=HM85XB76"}, "web2pdfHTMLJSError": {"message": "Weboldalfeldolgozási hiba."}, "web2pdfHTMLTooLarge": {"message": "A HTML-tartalom túl nagy."}, "web2pdfInfoHoverPopup": {"message": "Az Adobe Acrobat bővítmény PDF-eszközöket javasol a feladat megoldásához az Ön keresései alapján. A folyamat az eszközön zajlik, a keresések nem kerülnek továbbításra az Adobe felé. A funkció letiltásához kattintson: <span class = \"tooltip-content-settings\">ide</span>."}, "web2pdfLoaderTitle": {"message": "Betöltés..."}, "web2pdfMissingMac": {"message": "<PERSON><PERSON> Acrobat for Chrome még nem elérhető Mac re<PERSON>re"}, "web2pdfMissingMacEdge": {"message": "Az Adobe Acrobat még nem érhető el a Microsoft Edge-hez <PERSON> rends<PERSON>re"}, "web2pdfNoFoldersText": {"message": "Lehetséges, hogy az Acrobat DC tárhelyen nincsenek mappák."}, "web2pdfNoWritePermissionOnSelectedFile": {"message": "Nem rendelkezik írási jogosultsággal a kiválasztott fájlhoz."}, "web2pdfOpenButtonText": {"message": "Megnyitás az Acrobatban"}, "web2pdfOpenButtonTextOlder": {"message": "Adobe PDF letöltése"}, "web2pdfOpenButtonTextReader": {"message": "Megnyitás Acrobat Readerben"}, "web2pdfOpenConvertedPDFDescription": {"message": "Konvertált PDF automatikus megnyitása az Acrobatban, <PERSON><PERSON><PERSON> be<PERSON>dött a weboldal konvertálása PDF-be."}, "web2pdfOpenConvertedPDFTitle": {"message": "Konvertált PDF-ek automatikus megnyitása az Acrobatban"}, "web2pdfOpenInDCButtonText": {"message": "PDF megnyitása Acrobatban"}, "web2pdfOptOut": {"message": "A termék funkcióinak fejlesztése érdekében ez a bővítmény felhasználással kapcsolatos adatokat küld az Adobe számára."}, "web2pdfOptOutDisabled": {"message": "<PERSON><PERSON><PERSON><PERSON> adata nem kerül megosztásra."}, "web2pdfOptions": {"message": "Beállítások"}, "web2pdfOptionsTitle": {"message": "Adobe Acrobat-beállítások"}, "web2pdfPDFOpenFailed": {"message": "PDF megnyitása sikertelen"}, "web2pdfPDFOpenFailedv2": {"message": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>"}, "web2pdfPDFOpened": {"message": "PDF megnyitva"}, "web2pdfPDFOpening": {"message": "PDF megnyitása"}, "web2pdfPreferencesButtonText": {"message": "Beállítások..."}, "web2pdfPreferencesNewText": {"message": "Konvertálási beállítások..."}, "web2pdfPrivacy": {"message": "Adobe Acrobat névjegye"}, "web2pdfReplaceWarning": {"message": "már l<PERSON>.\nSzeretné lecserélni?"}, "web2pdfSave": {"message": "Men<PERSON>s"}, "web2pdfSaveButton": {"message": "Beállítások mentése"}, "web2pdfShowPDFTools": {"message": "Megjelenítés a Google-keresési eredményekkel"}, "web2pdfShowPDFViewerOption": {"message": "Acrobat használata alapértelmezett PDF-megtekintőként"}, "web2pdfShowPersistentOpen": {"message": "„Megnyitás Acrobatban” lehetőség megjelenítése"}, "web2pdfShowPersistentOpenAlways": {"message": "„Megnyitás Acrobatban” lehetőség megjelenítése mindig"}, "web2pdfShowPersistentOpenDescription": {"message": "Minden alkalommal mutassa a PDF megnyitása az Acrobatban lehetőséget."}, "web2pdfStatusCancelled": {"message": "Konvertálás me<PERSON>zakítva"}, "web2pdfStatusComplete": {"message": "Sikeres konvertálás"}, "web2pdfStatusDownloading": {"message": "Letöltés a konvertáláshoz..."}, "web2pdfStatusDownloadingPDF": {"message": "PDF letöltése"}, "web2pdfStatusFailure": {"message": "Konvertálási hiba"}, "web2pdfStatusInProgress": {"message": "Konvertálás folyamatban..."}, "web2pdfStatusUnknownFailure": {"message": "Ismeretlen hiba"}, "web2pdfStatusWaiting": {"message": "Várakozás a konvertálásra..."}, "web2pdfTitle": {"message": "Adobe Acrobat"}, "web2pdfUnsupportedAcrobatVersion": {"message": "Nem <PERSON> az Acrobat támogatott verziója. Telepítse az <a target='_blank' href='https://acrobat.adobe.com/hu/hu/free-trial-download.html?trackingid=29NMCS7T&mv=other'>ingyenes, 7 napos próbaverziót</a>."}, "web2pdfUntitledFileName": {"message": "névtelen"}, "webpagePDFConversion": {"message": "Weboldal konvertálása PDF formátumba beállítás"}, "webpagePDFConversionDescription": {"message": "A weboldalak PDF-ként történő mentésekor beállíthatja a margókat, a betűtípusokat és egyéb, elrendez<PERSON><PERSON> kapcsolatos részleteket"}}