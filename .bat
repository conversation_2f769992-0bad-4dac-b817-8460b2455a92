@echo off
echo ========================================
echo Chrome调试模式启动脚本
echo ========================================

REM 检查是否已有Chrome调试进程在运行
echo 检查现有Chrome调试进程...
netstat -an | findstr ":9222" >nul
if %errorlevel% == 0 (
    echo ✅ Chrome调试模式已在运行 (端口9222)
    echo 📱 调试地址: http://127.0.0.1:9222
    echo.
    echo 您现在可以使用程序中的发布功能了
    pause
    exit /b 0
)

REM 查找Chrome可执行文件
echo 查找Chrome安装位置...
set CHROME_EXE=""
if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    set CHROME_EXE="C:\Program Files\Google\Chrome\Application\chrome.exe"
    echo 找到Chrome: C:\Program Files\Google\Chrome\Application\chrome.exe
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    set CHROME_EXE="C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
    echo 找到Chrome: C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
) else if exist "%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe" (
    set CHROME_EXE="%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe"
    echo 找到Chrome: %LOCALAPPDATA%\Google\Chrome\Application\chrome.exe
)

if %CHROME_EXE%=="" (
    echo ❌ 错误: 未找到Chrome安装
    echo.
    echo 请确保Chrome已正确安装在以下位置之一:
    echo - C:\Program Files\Google\Chrome\Application\chrome.exe
    echo - C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
    echo - %LOCALAPPDATA%\Google\Chrome\Application\chrome.exe
    pause
    exit /b 1
)

REM 关闭现有Chrome进程
echo 关闭现有Chrome进程...
taskkill /f /im chrome.exe >nul 2>&1
timeout /t 2 >nul

REM 创建用户数据目录
if not exist "selenium_chrome_data" (
    echo 创建用户数据目录...
    mkdir "selenium_chrome_data"
)

REM 启动Chrome调试模式
echo 启动Chrome调试模式...
echo 端口: 9222
echo 用户数据目录: selenium_chrome_data
echo.

start "" %CHROME_EXE% --remote-debugging-port=9222 --user-data-dir=selenium_chrome_data --no-first-run --no-default-browser-check --disable-extensions --disable-plugins

REM 等待Chrome启动
echo 等待Chrome启动...
timeout /t 3 >nul

REM 验证Chrome是否成功启动
netstat -an | findstr ":9222" >nul
if %errorlevel% == 0 (
    echo ✅ Chrome调试模式启动成功!
    echo 📱 调试地址: http://127.0.0.1:9222
    echo.
    echo 🎯 现在您可以:
    echo 1. 在Chrome中手动登录抖音等平台
    echo 2. 使用程序中的发布功能
    echo.
    echo 💡 提示: 保持此窗口打开，关闭会停止调试模式
) else (
    echo ❌ Chrome调试模式启动失败
    echo.
    echo 🔧 故障排除:
    echo 1. 确保端口9222未被占用
    echo 2. 尝试以管理员身份运行此脚本
    echo 3. 检查Chrome版本是否支持调试模式
)

echo.
pause
