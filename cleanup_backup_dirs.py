
import shutil
import os

# 项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))

# 要删除的目录列表（使用相对路径）
dirs_to_delete = [
    'backup',
    'backup_obsolete_1752643706',
    'backups'
]

print("开始清理旧的备份目录...")

for dir_name in dirs_to_delete:
    # 构造完整的绝对路径
    dir_path = os.path.join(project_root, dir_name)
    
    # 检查目录是否存在
    if os.path.exists(dir_path):
        try:
            # 使用 shutil.rmtree() 强制删除目录及其所有内容
            shutil.rmtree(dir_path)
            print(f"成功删除目录: {dir_path}")
        except Exception as e:
            print(f"删除目录 {dir_path} 时出错: {e}")
    else:
        print(f"目录不存在，跳过: {dir_path}")

print("清理完成。")
