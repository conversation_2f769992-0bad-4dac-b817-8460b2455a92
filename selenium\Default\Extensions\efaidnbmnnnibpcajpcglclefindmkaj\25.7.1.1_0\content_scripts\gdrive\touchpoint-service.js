/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{getSelectorsForCurrentPathAndSelectedView,getSelectedFiles,createUrlForAcrobatTouchPoint,createCustomClassNameBasedOnViewAndPath,getPdfFileDetails,setSelectedView,setDrivePath,getEligiblePdfFileElementList,isFileElementSelected,isEventTargetWithinElement,areFilesPdfs,buildClassSelectorString,areFileDetailsValid,isKeyboardEventOnFileElement}from"./util.js";import state from"./state.js";import{getElementListForSelectors,sendErrorLog,createAcrobatIconElement,sendAnalytics,sendAnalyticsOncePerMonth,isAnalyticsSentInTheMonthOrSession,sendAnalyticsOnce}from"../gsuite/util.js";import{acrobatTouchPointClicked,createFteTooltip,shouldShowFteTooltip,updateFteToolTipCoolDown}from"../gsuite/fte-utils.js";import{isDefaultViewer,sendAnalyticsWithGdriveDVFeatureStatus,openPdfInNewTabForDV}from"./default-viewership-service.js";const GDRIVE_TOUCH_POINT_CLASS="acrobat-gdrive-touch-point",GDRIVE_ICON_CLASS="acrobat-gdrive-icon",GDRIVE_TOUCH_POINT_TOOLTIP_CLASS="acrobat-gdrive-touch-point-tooltip",GDRIVE_TOUCH_POINT_TOOLTIP_ARROW_CLASS="acrobat-gdrive-touch-point-tooltip-arrow",GDRIVE_TOOLTIP_HOVERED="acrobat-gdrive-tooltip-hover",GDRIVE_PDF_ELEMENT_PROCESSED="acrobat-processed",GDRIVE_FTE_TOOLTIP_STORAGE_KEY="acrobat-gdrive-fte-state",GDRIVE_FTE_COACHMARK_ADDED="acrobat-gdrive-coachmark-added",TRIPLE_DOT_MENU_ANALYTICS_EVENT="DCBrowserExt:GDrive:TripleDotMenuClicked",TOP_MENU_SINGLE_SELECT_ANALYTICS_EVENT="DCBrowserExt:GDrive:TopFileBarMenuClicked:SingleSelect",TOP_MENU_MULTI_SELECT_ANALYTICS_EVENT="DCBrowserExt:GDrive:TopFileBarMenuClicked:MultiSelect",RIGHT_CLICK_SINGLE_SELECT_ANALYTICS_EVENT="DCBrowserExt:GDrive:RightClickMenuClicked:SingleSelect",RIGHT_CLICK_MULTI_SELECT_ANALYTICS_EVENT="DCBrowserExt:GDrive:RightClickMenuClicked:MultiSelect",GDRIVE_PDF_EVENT_LISTENER="acrobat-pdf-listener",GSUITE_FTE_TOOLTIP_CONTAINER_CLASS="acrobat-fte-tooltip-container",GSUITE_FTE_TOOLTIP_BUTTON="acrobat-fte-tooltip-button",GDRIVE_GRID_VIEW="GoogleDriveGridView",GDRIVE_LIST_VIEW="GoogleDriveListView",fteDismissEventType=["click","mousedown","contextmenu","keydown"],fteDismissEventKey=["Enter","ArrowLeft","ArrowRight"],createAcrobatTooltip=()=>{if(isDefaultViewer())return;if(!document.getElementById(GDRIVE_TOUCH_POINT_TOOLTIP_CLASS)){const e=document.createElement("div");e.setAttribute("class",GDRIVE_TOUCH_POINT_TOOLTIP_CLASS),e.id=GDRIVE_TOUCH_POINT_TOOLTIP_CLASS,e.innerText=state?.config?.acrobatPromptText||"Open in Acrobat";const t=document.createElement("div");t.setAttribute("class",GDRIVE_TOUCH_POINT_TOOLTIP_ARROW_CLASS),e.appendChild(t),document.body.appendChild(e)}},createAcrobatTouchPointForFileDiv=e=>{const t=createUrlForAcrobatTouchPoint(e,state?.selectedView),o=document.createElement("a");return o.href=t,o.target="_blank",o.appendChild(createAcrobatIconElement(GDRIVE_ICON_CLASS,state?.acrobatIconPath)),handleAcrobatTouchPointClick(o),handleAcrobatTouchPointTooltipBehaviour(o),o},addStylingToAcrobatTouchPoint=(e,t,o)=>{const i=createCustomClassNameBasedOnViewAndPath(GDRIVE_TOUCH_POINT_CLASS,state?.selectedView,state?.driveUrlPath,t,o);e.classList.add(GDRIVE_TOUCH_POINT_CLASS),e.classList.add(i),e.tabIndex=0},handleAcrobatTouchPointTooltipBehaviour=e=>{handleAcrobatTouchPointHover(e),handleAcrobatTouchPointMouseLeave(e),handleAcrobatTouchPointFocus(e),handleAcrobatTouchPointBlur(e)},handleAcrobatTouchPointClick=e=>{e?.addEventListener("pointerdown",(e=>{e.stopImmediatePropagation(),e.preventDefault(),acrobatTouchPointClicked("acrobat-gdrive-fte-state"),state.fteToolTip.touchPointClicked=!0,removeFteTooltip(),sendAnalyticsWithGdriveDVFeatureStatus(`DCBrowserExt:GDrive:${state?.selectedView}:Clicked`,{eventContext:state?.driveUrlPath})}),{signal:state?.eventControllerSignal})},handleAcrobatTouchPointKeyDown=e=>{if(("Enter"===e.key||32===e.keyCode)&&e?.target?.classList?.contains(GDRIVE_TOUCH_POINT_CLASS)){e.stopImmediatePropagation(),e.preventDefault(),acrobatTouchPointClicked("acrobat-gdrive-fte-state");const t=e?.target?.getAttribute("href");let o=`DCBrowserExt:GDrive:${state?.selectedView}:EnterKeyDown`;32===e.keyCode&&(o=`DCBrowserExt:GDrive:${state?.selectedView}:SpaceKeyDown`),t.length>0&&(window.open(t,"_blank"),sendAnalytics([[o,{eventContext:state?.driveUrlPath}]]))}},handleAcrobatTouchPointHover=e=>{e.addEventListener("mouseenter",(()=>{showAcrobatTooltip(e)}),{signal:state?.eventControllerSignal})},handleAcrobatTouchPointFocus=e=>{e.addEventListener("focus",(t=>{t.stopImmediatePropagation(),t.preventDefault(),showAcrobatTooltip(e)}),{signal:state?.eventControllerSignal})},showAcrobatTooltip=e=>{const t=document.getElementById(GDRIVE_TOUCH_POINT_TOOLTIP_CLASS),o=e?.closest(`[${GDRIVE_FTE_COACHMARK_ADDED}]`);let i=isFteEligibleInViewPort(o);if(t||i){const t=e?.parentElement?.getAttribute("data-tooltip");t&&(e.parentElement.setAttribute("prev-tooltip",t),e.parentElement.removeAttribute("data-tooltip"))}if(t&&!i){const o=e.getBoundingClientRect(),i=o.bottom+8,n=o.left+o.width/2-43;t.style.top=`${i}px`,t.style.left=`${n}px`,t.style.zIndex="9",setTimeout((()=>{t.classList.add(GDRIVE_TOOLTIP_HOVERED)}),500)}},handleAcrobatTouchPointMouseLeave=e=>{e.addEventListener("mouseleave",(()=>{hideAcrobatTooltip(e)}),{signal:state?.eventControllerSignal})},handleAcrobatTouchPointBlur=e=>{e.addEventListener("blur",(()=>{hideAcrobatTooltip(e)}),{signal:state?.eventControllerSignal})},hideAcrobatTooltip=e=>{const t=document.getElementById(GDRIVE_TOUCH_POINT_TOOLTIP_CLASS);if(t){const o=e?.parentElement?.getAttribute("prev-tooltip");o&&(e.parentElement.setAttribute("data-tooltip",o),e.parentElement.removeAttribute("prev-tooltip")),t.classList.remove(GDRIVE_TOOLTIP_HOVERED),t.style.zIndex="-1"}},getAcrobatTouchPointHost=(e,t)=>{if(state?.selectedView===GDRIVE_GRID_VIEW&&0===t?.touchpointContainer?.length)return e;const o=getElementListForSelectors(t?.touchpointContainer,e);if(0!==o?.length)return o[0];sendErrorLog("Error in GSuite",`Failure in adding touchpoint in GDrive : showAcrobatTouchPointInFileElement : Touchpoint host not found in ${state.selectedView} , for path ${state.driveUrlPath}`)},showAcrobatTouchPointInFileElement=(e,t,o)=>{if(""===o.id||""===o?.title)return void sendAnalyticsOncePerMonth(`DCBrowserExt:GDrive:${state?.selectedView}:PdfFileNameNotSupported`);const i=getAcrobatTouchPointHost(e,t);if(!i)return;const n=createAcrobatTouchPointForFileDiv(o);addStylingToAcrobatTouchPoint(n,e,i),i.appendChild(n),checkIfAcrobatTouchPointShouldBeVisible(e,n),handlePdfFileElementMouseHover(e,n),handlePdfFileElementMouseLeave(e,n),handlePdfFileElementBlur(e,n)},checkIfAcrobatTouchPointShouldBeVisible=(e,t)=>{(e.matches(":hover")||isFileElementSelected(e)||e.hasAttribute(GDRIVE_FTE_COACHMARK_ADDED))&&(t.style.display="flex"),e.matches(":hover")&&processForFteTouchPoint(e,t)},handlePdfFileElementBlur=(e,t)=>{e.addEventListener("blur",(()=>{t.style.display="none",hideAcrobatTooltip(t)}),{signal:state?.eventControllerSignal})},handlePdfFileElementMouseHover=(e,t)=>{e.addEventListener("mouseenter",(()=>{t.style.display="flex",sendAnalyticsOncePerMonth(`DCBrowserExt:GDrive:${state?.selectedView}:Shown`),processForFteTouchPoint(e,t)}),{signal:state?.eventControllerSignal})},processForFteTouchPoint=(e,t)=>{e?.getAttribute(GDRIVE_FTE_COACHMARK_ADDED)||shouldShowFteTooltip(state?.config?.fteConfig?.tooltip,state?.fteToolTip,state?.config?.enableFteToolTipForListGridView).then((o=>{isFteEligible(o,e)&&addFteTooltipToAcrobatTouchPoint(e,t)}))},addFteTooltipToAcrobatTouchPoint=(e,t)=>{const o=createFteTooltip(state?.config?.fteToolTipStrings,state?.selectedView);showFteTooltip(o,t),document.body.appendChild(o),handlePostFteTooltipCreation(e,o)},handlePostFteTooltipCreation=(e,t)=>{handleFteButtonClick(t),state.fteToolTip.lastFtePath=state.driveUrlPath,state.fteToolTip.lastFteView=state.selectedView,updateFteToolTipCoolDown(state?.config?.fteConfig?.tooltip,"acrobat-gdrive-fte-state").then((e=>{state.fteToolTip={...state?.fteToolTip,...e}})),e.setAttribute(GDRIVE_FTE_COACHMARK_ADDED,"true"),sendAnalyticsWithGdriveDVFeatureStatus(`DCBrowserExt:GDriveFTE:${state?.selectedView}:Shown`)},handleFteButtonClick=e=>{e.querySelector(`.${GSUITE_FTE_TOOLTIP_BUTTON}`).addEventListener("click",(()=>{removeFteTooltip(),sendAnalytics([[`DCBrowserExt:GDriveFTE:${state?.selectedView}:Clicked`]])}),{signal:state?.eventControllerSignal})},showFteTooltip=(e,t)=>{const{fteTooltipTop:o,fteTooltipLeft:i}=getStylingForFteTooltip(e,t);e.style.top=`${o}px`,e.style.left=`${i}px`,e.style.zIndex="4",e.style.position="absolute",setTimeout((()=>{e.style.display="flex"}),500)},getStylingForFteTooltip=(e,t)=>{const o=t?.getBoundingClientRect();return{fteTooltipTop:o.bottom+12,fteTooltipLeft:o.left+o.width/2-44}},isFteEligible=(e,t)=>{if(!e)return!1;return!(document.body.getElementsByClassName("acrobat-fte-tooltip-container").length>0)&&(!!isFteEligibleInViewPort(t)&&e)},isFteEligibleInViewPort=e=>{const t=window.innerWidth,o=window.innerHeight;if(t<730||!e)return!1;const i=e.getBoundingClientRect(),n=i.bottom,r=i.width,l=i.right;return!(n+190>o)&&(l+300<t||r>400)},handleFteTooltipDismissal=e=>{const t=document.getElementById("acrobat-fte-tooltip-container");t&&t?.classList?.contains(`acrobat-fte-tooltip-container-${state?.selectedView}`)&&("domUpdate"===e||"resize"===e.type||"scroll"===e.type||(fteDismissEventType.includes(e.type)||fteDismissEventKey.includes(e.key))&&!t.contains(e.target))&&(removeFteTooltip(),sendAnalytics([[`DCBrowserExt:GDriveFTE:${state?.selectedView}:Dismissed`]]))},removeFteTooltip=()=>{const e=document.getElementById("acrobat-fte-tooltip-container");e?.remove(),state?.fteToolTip&&(state.fteToolTip.lastFtePath=null,state.fteToolTip.lastFteView=null);const t=document.querySelector(`[${GDRIVE_FTE_COACHMARK_ADDED}]`);if(t){t.removeAttribute(GDRIVE_FTE_COACHMARK_ADDED);const e=t.querySelector(`.${GDRIVE_TOUCH_POINT_CLASS}`);!e||isFileElementSelected(t)||t.matches(":hover")||(e.style.display="none")}},handlePdfFileElementMouseLeave=(e,t)=>{e.addEventListener("mouseleave",(()=>{e?.hasAttribute(GDRIVE_FTE_COACHMARK_ADDED)||(t.style.display="none"),hideAcrobatTooltip(t)}),{signal:state?.eventControllerSignal})},listAndGridViewEligible=()=>(state?.config?.enableGDriveListViewTouchPoint||state?.config?.enableGDriveGridViewTouchPoint)&&!1===state?.addOnStatus?.isAddOnDefault,shouldSkipDVKeyboardEvent=(e,t,o)=>!t.some((t=>isEventTargetWithinElement(e,t)))||1!==o.length||!o[0]?.hasAttribute("acrobat-processed")||!isKeyboardEventOnFileElement(e),handleDVKeyboardEvents=e=>{if("Enter"===e.key){const t=getSelectorsForCurrentPathAndSelectedView(),o=getElementListForSelectors(t?.fileElement),i=getSelectedFiles(o);if(shouldSkipDVKeyboardEvent(e,o,i))return;if(sendAnalyticsWithGdriveDVFeatureStatus(`DCBrowserExt:GDrive:${state?.selectedView}:EnterKeyDown`,{eventContext:state?.driveUrlPath}),isDefaultViewer()){const o=getPdfFileDetails(i[0],t);areFileDetailsValid(o)&&openPdfInNewTabForDV(e,o,state?.selectedView)}}},handleDoubleClickForPdfFile=e=>{if(sendAnalyticsWithGdriveDVFeatureStatus(`DCBrowserExt:GDrive:${state?.selectedView}:DoubleClick`,{eventContext:state?.driveUrlPath}),isDefaultViewer()){const t=getSelectorsForCurrentPathAndSelectedView(),o=e?.target?.closest("[acrobat-processed]"),i=getPdfFileDetails(o,t);openPdfInNewTabForDV(e,i,state?.selectedView)}},addClickListenerToFileElement=e=>{e.hasAttribute("acrobat-pdf-listener")||(e.addEventListener("dblclick",handleDoubleClickForPdfFile,{signal:state?.eventControllerSignal}),e.setAttribute("acrobat-pdf-listener","true"))},processFileElementList=(e,t)=>{try{const o=getEligiblePdfFileElementList(e,state?.config?.selectors[state?.selectedView]?.pdfSvgPath,t);for(const e of o){const o=getPdfFileDetails(e,t);areFileDetailsValid(o)&&(isDefaultViewer()?hideAcrobatTooltip():showAcrobatTouchPointInFileElement(e,t,o),addClickListenerToFileElement(e))}createAcrobatTooltip()}catch(e){sendErrorLog("Error in GSuite",`Failure in adding touchpoint in GDrive : processFileElementList : Processing Error While Adding TouchPoint in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},getTripleDotMenuElement=()=>{const e=buildClassSelectorString(state?.config?.selectors[state?.selectedView]?.menu);return e?document.querySelector(e):null},sendAnalyticsForTripleDotMenu=e=>{try{const t=getSelectorsForCurrentPathAndSelectedView(),o=buildClassSelectorString(t?.fileElement);if(!o)return;const i=e?.target?.closest(o);if(areFilesPdfs(i)){const o=buildClassSelectorString(t?.tripleDotMenu?.tripleDotMenuBtn);if(!o)return;const n=i.querySelectorAll(o);if(n.length>0){const t=n[n.length-1];isEventTargetWithinElement(e,t)&&sendAnalyticsOncePerMonth(TRIPLE_DOT_MENU_ANALYTICS_EVENT)}}}catch(e){sendErrorLog("Error in GSuite",`Failure in GDrive Tripe Dot Menu : sendAnalyticsForTripleDotMenu : Processing Error when triple dot menu clicked in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},handleAnalyticsForTripleDotMenu=e=>{isAnalyticsSentInTheMonthOrSession(TRIPLE_DOT_MENU_ANALYTICS_EVENT)||setTimeout((()=>{getTripleDotMenuElement()&&sendAnalyticsForTripleDotMenu(e)}),1e3)},sendAnalyticsForTopMenu=e=>{try{const t=getSelectorsForCurrentPathAndSelectedView(),o=buildClassSelectorString(t?.tripleDotMenu?.topMenuBar);if(!o)return;const i=document.querySelector(o)?.lastElementChild;if(isEventTargetWithinElement(e,i)){const e=getElementListForSelectors(t?.fileElement),o=getSelectedFiles(e);if(areFilesPdfs(o)){const e=1===o.length?TOP_MENU_SINGLE_SELECT_ANALYTICS_EVENT:TOP_MENU_MULTI_SELECT_ANALYTICS_EVENT;sendAnalyticsOncePerMonth(e)}}}catch(e){sendErrorLog("Error in GSuite",`Failure in GDrive Tripe Dot Menu : sendAnalyticsForTopMenu : Processing Error when top file bar menu clicked in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},handleAnalyticsForTopMenu=e=>{isAnalyticsSentInTheMonthOrSession(TOP_MENU_SINGLE_SELECT_ANALYTICS_EVENT)&&isAnalyticsSentInTheMonthOrSession(TOP_MENU_MULTI_SELECT_ANALYTICS_EVENT)||setTimeout((()=>{getTripleDotMenuElement()&&sendAnalyticsForTopMenu(e)}),1e3)},sendAnalyticsForRightClickMenu=e=>{try{const t=getSelectorsForCurrentPathAndSelectedView(),o=buildClassSelectorString(t?.fileElement);if(!o)return;const i=e?.target?.closest(o);if(areFilesPdfs(i)){const e=getElementListForSelectors(t?.fileElement),o=getSelectedFiles(e);if(areFilesPdfs(o)){const e=1===o.length?RIGHT_CLICK_SINGLE_SELECT_ANALYTICS_EVENT:RIGHT_CLICK_MULTI_SELECT_ANALYTICS_EVENT;sendAnalyticsOncePerMonth(e)}}}catch(e){sendErrorLog("Error in GSuite",`Failure in GDrive Tripe Dot Menu : sendAnalyticsForRightClickMenu : Processing Error when right clicking in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},handleAnalyticsForRightClickMenu=e=>{isAnalyticsSentInTheMonthOrSession(RIGHT_CLICK_SINGLE_SELECT_ANALYTICS_EVENT)&&isAnalyticsSentInTheMonthOrSession(RIGHT_CLICK_MULTI_SELECT_ANALYTICS_EVENT)||setTimeout((()=>{getTripleDotMenuElement()&&sendAnalyticsForRightClickMenu(e)}),1e3)},processForFileSelection=e=>{try{if(isDefaultViewer())return;const t=getSelectedFiles(e),o=1===t.length,i=Array.from(t).filter((e=>e.hasAttribute("acrobat-processed")));for(const e of i){const t=e.querySelector(`.${GDRIVE_TOUCH_POINT_CLASS}`);t&&(o?showAcrobatTouchPointForSelectedPdfFile(e,t):hideAcrobatTouchPointForMultiSelectedPdfFile(e,t))}}catch(e){sendErrorLog("Error in GSuite",`Failure in adding touchpoint in GDrive : processForFileSelection : Processing Error While Adding TouchPoint for Multi Selected File in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},hideAcrobatTouchPointForMultiSelectedPdfFile=(e,t)=>{e?.matches(":hover")||e?.hasAttribute(GDRIVE_FTE_COACHMARK_ADDED)||(t.style.display="none")},showAcrobatTouchPointForSelectedPdfFile=(e,t)=>{sendAnalyticsOncePerMonth(`DCBrowserExt:GDrive:${state?.selectedView}:SelectedAndShown`,{eventContext:state?.driveUrlPath}),t.style.display="flex"},processForGridAndListView=()=>{try{const e=getSelectorsForCurrentPathAndSelectedView(),t=getElementListForSelectors(e?.fileElement);processFileElementList(t,e),processForFileSelection(t),addCommonEventListeners(),dismissFteTooltipOnContentUpdate()}catch(e){sendErrorLog("Error in GSuite",`Failure in adding touchpoint in GDrive : processForGridAndListView : Processing Error While Adding TouchPoint in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},dismissFteTooltipOnContentUpdate=()=>{const e=document.getElementById("acrobat-fte-tooltip-container");if(!e)return;const t=document.querySelector(`[${GDRIVE_FTE_COACHMARK_ADDED}]`);if(!t)return void handleFteTooltipDismissal("domUpdate");const o=t.getElementsByClassName(GDRIVE_TOUCH_POINT_CLASS);o?.length>0&&(isFteEligibleInViewPort(t)?showFteTooltip(e,o[0]):handleFteTooltipDismissal("domUpdate"))},addCommonEventListeners=()=>{state?.eventListenerAdded||(document.addEventListener("scroll",(e=>{hideAcrobatTooltip(),handleFteTooltipDismissal(e)}),{signal:state?.eventControllerSignal,capture:!0}),document.addEventListener("keydown",(e=>{handleAcrobatTouchPointKeyDown(e),handleFteTooltipDismissal(e),handleDVKeyboardEvents(e)}),{signal:state?.eventControllerSignal,capture:!0}),document.addEventListener("click",(e=>{handleFteTooltipDismissal(e)}),{signal:state?.eventControllerSignal,capture:!0}),document.addEventListener("mousedown",(e=>{handleFteTooltipDismissal(e)}),{signal:state?.eventControllerSignal,capture:!0}),document.addEventListener("contextmenu",(e=>{handleFteTooltipDismissal(e)}),{signal:state?.eventControllerSignal,capture:!0}),window.addEventListener("resize",(e=>{handleFteTooltipDismissal(e)}),{signal:state?.eventControllerSignal}),state.eventListenerAdded=!0)},processForTouchPoint=()=>{try{if(setDrivePath(),setSelectedView(),shouldNotProcessForTouchPoint())return;listAndGridViewEligible()&&(processForGridAndListView(),sendAnalyticsOncePerMonth(`DCBrowserExt:GDrive:${state?.selectedView}:Eligible`))}catch(e){sendErrorLog("Error in GSuite",`Failure in adding touchpoint in GDrive : processForTouchPoint : Error While Adding TouchPoint in view ${state.selectedView} , for path ${state.driveUrlPath}. Error : ${e}`)}},shouldNotProcessForTouchPoint=()=>""===state?.selectedView||""===state?.driveUrlPath||state?.config?.selectors[state?.selectedView]?.excludePath.includes(state?.driveUrlPath),removeListAndGridViewTouchPoints=()=>{const e=document.getElementsByClassName(GDRIVE_TOUCH_POINT_CLASS);Array.from(e).forEach((e=>{e.remove()}))},removeAllTouchPoints=()=>{removeListAndGridViewTouchPoints(),removeFteTooltip()};export{processForTouchPoint,removeAllTouchPoints,handleAnalyticsForTripleDotMenu,handleAnalyticsForRightClickMenu,handleAnalyticsForTopMenu};