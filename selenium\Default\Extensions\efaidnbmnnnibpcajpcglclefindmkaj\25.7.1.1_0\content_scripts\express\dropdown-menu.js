/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import expressFteUtils from"./express-fte-utils.js";class ExpressDropdownMenu{DROPDOWN_MENU_CLASS="cc440d50ba-express-dropdown-menu";DROPDOWN_MENU_SHOW_CLASS="cc440d50ba-express-dropdown-menu-show";ENTRY_POINT_BUTTON_CLASS="cc440d50ba-express-entrypoint-button";ENTRY_POINT_BUTTON_ICON_CLASS="cc440d50ba-express-entrypoint-button-icon";ENTRY_POINT_BUTTON_ICON_IMG_CLASS="cc440d50ba-express-entrypoint-button-icon-img";HEADER_TITLE_CLASS="cc440d50ba-express-dropdown-menu-content-header-title-row-title";HEADER_TITLE_ROW_ICON_CLASS="cc440d50ba-express-dropdown-menu-content-header-title-row-icon-img";HEADER_SUBTITLE_CLASS="cc440d50ba-express-dropdown-menu-content-header-title-row-subtitle";MENU_ITEM_ICON_IMG_CLASS="cc440d50ba-express-dropdown-menu-content-item-icon-img";MENU_ITEM_TEXT_CLASS="cc440d50ba-express-dropdown-menu-content-item-text";VERBS={EDIT_IMAGE:{intent:"editImage",divClass:"cc440d50ba-express-item-edit-image",iconPath:chrome.runtime.getURL("browser/images/express_edit.svg")},REMOVE_BACKGROUND_IMAGE:{intent:"removeBackgroundImage",divClass:"cc440d50ba-express-item-remove-background",iconPath:chrome.runtime.getURL("browser/images/express_remove_background.svg")},CROP_IMAGE:{intent:"cropImage",divClass:"cc440d50ba-express-item-crop-image",iconPath:chrome.runtime.getURL("browser/images/express_crop_image.svg")}};constructor(){this.init()}async init(){this.htmlDataPromise=fetch(chrome.runtime.getURL("resources/express/expressDropdownMenu.html")).then((e=>e.text())).then((e=>{this.htmlData=e})),expressFteUtils.addFontToDocument()}toggleDropdown(e,t){return function(s){s.stopPropagation(),chrome?.runtime?.id||t?.remove();const n=document.getElementsByClassName(this.DROPDOWN_MENU_CLASS)[0];n?.classList.toggle(this.DROPDOWN_MENU_SHOW_CLASS);const o=new URL(window.location.href).hostname;expressFteUtils.sendAnalyticsEvent([["DCBrowserExt:Express:DropdownMenu:Shown",{domain:o,expressTouchpoint:e}]])}}addPencilIconToEntryPointButton(e){if(!this.expressPencilSVG)return;const t=(new DOMParser).parseFromString(this.expressPencilSVG,"image/svg+xml").documentElement;e.appendChild(t)}async renderMenuButton(e,t){await this.htmlDataPromise;const s=document.createElement("div");s.innerHTML=this.htmlData,s.className=t;const n=s.getElementsByClassName(this.ENTRY_POINT_BUTTON_CLASS)[0];n.onclick=this.toggleDropdown(t,s).bind(this);n.getElementsByClassName(this.ENTRY_POINT_BUTTON_ICON_IMG_CLASS)[0];const o=n.getElementsByClassName(this.ENTRY_POINT_BUTTON_ICON_CLASS)[0];this.expressPencilSVG?this.addPencilIconToEntryPointButton(o):fetch(chrome.runtime.getURL("browser/images/express_edit.svg")).then((e=>e.text())).then((e=>{this.expressPencilSVG=e,this.addPencilIconToEntryPointButton(o)}));return s.getElementsByClassName(this.HEADER_TITLE_ROW_ICON_CLASS)[0].src=chrome.runtime.getURL("browser/images/acrobat_dc_appicon_128.png"),Object.values(this.VERBS).forEach((n=>{const o=s.getElementsByClassName(n.divClass)[0];if(!o)return;o.getElementsByClassName(this.MENU_ITEM_ICON_IMG_CLASS)[0].src=n.iconPath,o.onclick=o=>{o.stopPropagation(),chrome?.runtime?.id||s.remove();s.getElementsByClassName(this.DROPDOWN_MENU_CLASS)[0].classList.remove(this.DROPDOWN_MENU_SHOW_CLASS);const r=new URL(window.location.href).hostname;expressFteUtils.sendAnalyticsEvent([["DCBrowserExt:Express:DropdownMenu:VERB:Clicked",{domain:r,VERB:n.intent,expressTouchpoint:t}]]),e(n.intent)}})),util.translateElements(".translate",s),document.body.onclick=e=>{if(e.target.closest(`.${this.DROPDOWN_MENU_CLASS}`))return;const t=document.getElementsByClassName(this.DROPDOWN_MENU_CLASS);for(const e of t)e.classList.remove(this.DROPDOWN_MENU_SHOW_CLASS)},s}}const expressDropdownMenu=new ExpressDropdownMenu;export default expressDropdownMenu;