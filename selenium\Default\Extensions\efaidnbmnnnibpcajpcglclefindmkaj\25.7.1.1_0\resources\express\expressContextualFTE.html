<!--
/*************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2025 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and may be covered by U.S. and Foreign Patents,
* patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
-->
<style>
    .ae1eed7c-express-contextual-fte {
      z-index: 10001;
      display: flex;
      flex-direction: column;
      height: auto;
      row-gap: 10px;
      position: absolute;
      background: #3972E0;
      color: #fff;
      border-radius: 4px;
      padding: 16px;
      height: 77px;
      width: 192px;
      margin-top: 12px;
      font-family: "Adobe Clean", adobe-clean, "AdobeClean-Regular", sans-serif !important;
      box-shadow: 0 2px 12px rgba(0,0,0,0.08);
    }
    .ae1eed7c-express-contextual-fte::before {
      content: '';
      position: absolute;
      top: -8px;
      left: 50%;
      border-width: 0 6.5px 9px 6.5px;
      border-style: solid;
      border-color: transparent transparent #3972E0 transparent;
      width: 0;
      height: 0;
    }
    .ae1eed7c-express-contextual-fte-content {
      display: flex;
      flex-direction: column;
      height: auto;
      row-gap: 4px;
      height: 77px;
      width: 192px;
    }
    .ae1eed7c-express-contextual-fte-title {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
    }
    .ae1eed7c-express-contextual-fte-desc {
      font-size: 12px;
      font-weight: 400;
      line-height: 15px;
    }
    .ae1eed7c-express-contextual-fte-cta {
        font-family: 'AdobeClean-Regular';
        font-weight: 700;
        background: none;
        border: none;
        color: inherit;
        font-size: 14px;
        line-height: 6px;
        cursor: pointer;
        padding: 4px 0 0 0;
        display: flex;
        margin: 6px 0;
        margin-left: auto;
    }
</style>
<div class="ae1eed7c-express-contextual-fte">
    <div class="ae1eed7c-express-contextual-fte-content">
        <div id="expressContextualFteTitle" class="ae1eed7c-express-contextual-fte-title translate"></div>
        <div id="expressContextualFteDescription" class="ae1eed7c-express-contextual-fte-desc translate"></div>
        <button id="expressContextualFteCta" class="ae1eed7c-express-contextual-fte-cta translate"></button>
    </div>
</div>

