/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{sendAnalytics,sendAnalyticsOncePerMonth,getParsedJSON}from"../gsuite/util.js";import{getUserId,updateDrivePDFUrl}from"./util.js";import{processForListView}from"./list-view-touch-point-service.js";import state from"./state.js";const PDF_MIME_TYPE="application/pdf",getAttachmentURL=e=>{let t=e.replace("&disp=safe","")+"&disp=inline";return getUserId()&&(t=t.replace("/mail/?",`/mail/u/${getUserId()}/?`)),t},acrobatMailDataHandler=e=>{let t;e?.detail?.url?.includes("/fd")?t=parseFDResponse(e?.detail?.responseData):e?.detail?.url?.includes("/i/s")?t=parseISResponse(e?.detail?.responseData):e?.detail?.url?.includes("/bv")?t=parseBVResponse(e?.detail?.responseData):e?.detail?.url?.includes("gmSetData")&&(t=parseGMSetData(e?.detail?.responseData,e?.detail?.key)),updateExistingThreadData(t,!0),processForListView()},addMailDataEventListener=()=>{document.addEventListener("acrobat-mail-data",(e=>{setTimeout((()=>{acrobatMailDataHandler(e)}),0)}),{signal:state?.eventControllerSignal})},injectResponseListenerScript=()=>{if(state?.gmailResponseListenerAdded)return;const e=document.createElement("script");e.setAttribute("id","acrobat-response-interceptor"),e.src=chrome.runtime.getURL("content_scripts/gmail/gmail-inject.js"),e.onload=function(){this.remove()},(document.head||document.documentElement).appendChild(e),state.gmailResponseListenerAdded=!0},updateExistingThreadData=(e,t)=>{e&&Object.keys(e).forEach((a=>{const r=e[a].messages,s={id:a,messages:{...state.getMessagesForThreadId(a)||{},...r}};t?state.setDataForThread(s):e[e]=s}))},parseISAttachments=e=>{try{const t=[];if(Array.isArray(e))for(let a of e)if(a[0]===PDF_MIME_TYPE){const e=getAttachmentURL(a[5]);t.push({name:a[1],url:e})}else sendAnalyticsIfImageMimeTye(a[0]);return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:ISAParsingFailed"]]),null}},updatePdfUrlAndAddToList=(e,t,a)=>{if(t?.toLowerCase().endsWith(".pdf")){const r=updateDrivePDFUrl(e);r&&a.push({name:t,url:r,isDriveAsset:!0})}},parseISDriveAttachments=e=>{try{const t=[],a=e[12]?.[28];if(Array.isArray(a))for(let e of a)e[4]===PDF_MIME_TYPE&&updatePdfUrlAndAddToList(e[3],e[1],t);return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:ISDAParsingFailed"]]),null}},IMAGE_MIME_TYPE=["image/png","image/jpeg","image/webp"],sendAnalyticsIfImageMimeTye=e=>{!state?.analyticsForImageInAPIResponse&&IMAGE_MIME_TYPE.includes(e)&&(sendAnalyticsOncePerMonth("DCBrowserExt:Gmail:Image"),state.analyticsForImageInAPIResponse=!0)},parseFDAttachments=e=>{try{const t=[];if(Array.isArray(e))for(let a of e){const e=a[0]?.[3]||"";if(e?.length>0)if(e[3]===PDF_MIME_TYPE){const a=getAttachmentURL(e[1]);t.push({name:e[2],url:a})}else sendAnalyticsIfImageMimeTye(e[3])}return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:FDAParsingFailed"]]),null}},parseISEmailAndUpdateThreadData=(e,t,a)=>{if(e?.length>0)try{for(let r of e){const e=r[0],s=r[6],n=parseISAttachments(r[11]),i=parseISDriveAttachments(r);if(n?.length>0||i?.length>0){const l=r[71]&&9===r[71][0];t[a]||(t[a]={messages:{}}),t[a].messages[e]={id:e,timestamp:s,deleted:l,attachments:n,driveAttachments:i}}}}catch(e){sendAnalytics([["DCBrowserExt:Gmail:ISEmailsParsingFailed"]])}},parseGMSetData=(e,t)=>{try{const a=getParsedJSON(e);if(!Array.isArray(a))return null;const r="Cl6csf"===t?a[1]:a[0]?.[0];if(!r||!Array.isArray(r))return null;const s={};for(let e of r)if("Cl6csf"===t){const t=e[1]?.[3],a=e[1]?.[4];parseISEmailAndUpdateThreadData(a,s,t)}else{const t=e[1],a=e[4]?.[4];parseGMSetDataA6JDVEmails(a,s,t)}return s}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:GMResponseParsingFailed"]]),null}},parseBVResponse=e=>{try{const t=getParsedJSON(e);if(!Array.isArray(t))return null;const a={},r=t[2];if(!r||!Array.isArray(r))return null;for(let e of r){const t=e[0][3],r=e[0]?.[4];parseISEmailAndUpdateThreadData(r,a,t)}return a}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:BVResponseParsingFailed"]]),null}},parseISResponse=e=>{try{const t=getParsedJSON(e);if(!Array.isArray(t))return null;const a=t[1]?.[5];if(!a||!Array.isArray(a))return null;const r={};for(let e of a){const t=e[0]?.[0],a=e[0]?.[2]?.[6]?.[0]?.[4];parseISEmailAndUpdateThreadData(a,r,t)}return r}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:ISResponseParsingFailed"]]),null}},domParser="undefined"!=typeof DOMParser&&new DOMParser,parseFDDriveAttachments=e=>{try{const t=[],a=e[1]?.[5]?.[1]?.[0]?.[2]?.[1];if(a&&a.includes("drive.google.com")){const e=domParser?.parseFromString(a,"text/html"),r=e?.querySelectorAll(".gmail_chip.gmail_drive_chip>a");if(r?.length>0)for(let e of r){const a=e.getAttribute("href"),r=e.querySelector("span")?.textContent;updatePdfUrlAndAddToList(a,r,t)}}return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:FDDAParsingFailed"]]),null}},parseA6JDVDriveAttachments=e=>{try{let t=[];const a=e[12]?.[28];if(Array.isArray(a))for(let e of a)e[4]===PDF_MIME_TYPE?updatePdfUrlAndAddToList(e[3],e[1],t):sendAnalyticsIfImageMimeTye(e[4]);return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:A6JDVDriveAttParsingFailed"]]),null}},parseA6JDVAttachments=e=>{try{let t=[];if(Array.isArray(e[11]))for(let a of e[11])if(a[0]===PDF_MIME_TYPE){let e=getAttachmentURL(a[5]);t.push({name:a[1],url:e})}else sendAnalyticsIfImageMimeTye(a[0]);return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:A6JDVAttParsingFailed"]]),null}},parseGMSetDataA6JDVEmails=(e,t,a)=>{if(e&&Array.isArray(e))try{for(let r of e){const e=r[0],s=r[6],n=parseA6JDVAttachments(r),i=parseA6JDVDriveAttachments(r);if(n?.length>0||i?.length>0){const l=9===r[71]?.[0];t[a]||(t[a]={messages:{}}),t[a].messages[e]={id:e,attachments:n,driveAttachments:i,timestamp:s,deleted:l}}}}catch(e){sendAnalytics([["DCBrowserExt:Gmail:GMSetDataA6JDV"]])}},parseFDEmailAndUpdateThreadData=(e,t,a)=>{if(Array.isArray(e))try{for(let r of e){const e=r[0],s=parseFDAttachments(r[1]?.[13]),n=parseFDDriveAttachments(r);if(s?.length>0||n?.length>0){const i=r[1]&&r[1][38]&&9===r[1][38][0];t[a]||(t[a]={messages:{}}),t[a].messages[e]={id:e,attachments:s,driveAttachments:n,timestamp:r[1]?.[16],deleted:i}}}}catch(e){sendAnalytics([["DCBrowserExt:Gmail:FDEmailsParsingFailed"]])}},parseFDResponse=e=>{try{const t={},a=getParsedJSON(e);if(!Array.isArray(a))return null;const r=a[1];if(!r||!Array.isArray(r))return null;for(let e of r){const a=e[0],r=e[2];parseFDEmailAndUpdateThreadData(r,t,a)}return t}catch(e){return sendAnalytics([["DCBrowserExt:Gmail:FDResponseParsingFailed"]]),null}},init=()=>{state?.gmailConfig.enableListViewPromptInGmail&&(injectResponseListenerScript(),addMailDataEventListener())};export{init};