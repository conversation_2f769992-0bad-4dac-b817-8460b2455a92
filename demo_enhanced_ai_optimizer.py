# -*- coding: utf-8 -*-
"""
增强AI内容优化器演示脚本
展示新增的高级AI优化功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.services.enhanced_content_optimizer import EnhancedContentOptimizer
from src.services.platform_publisher.base_publisher import VideoMetadata

async def demo_enhanced_ai_optimizer():
    """演示增强AI内容优化器功能"""
    print("🚀 增强AI内容优化器功能演示")
    print("=" * 60)
    
    # 1. 初始化优化器
    print("\n📋 1. 初始化增强AI内容优化器")
    optimizer = EnhancedContentOptimizer()
    print("   ✅ 优化器初始化完成")
    print(f"   📊 支持平台数量: {len(optimizer.platform_configs)}")
    
    # 2. 展示支持的平台
    print("\n🎯 2. 支持的平台列表")
    platforms = list(optimizer.platform_configs.keys())
    for i, platform in enumerate(platforms, 1):
        config = optimizer.platform_configs[platform]
        name = config.get('name', platform.title())
        style = config.get('style', '通用风格')
        print(f"   {i:2d}. {name:12} - 风格: {style}")
    
    # 3. 演示内容分析功能
    print("\n🔍 3. 内容分析功能演示")
    test_content = {
        'title': '超级搞笑的猫咪日常生活分享',
        'description': '今天给大家分享我家小猫咪的日常生活，它真的太可爱了！看它玩毛线球、晒太阳、卖萌的样子，相信大家都会被萌化的。这个视频记录了它一天的生活，从早上起床到晚上睡觉，每个瞬间都充满了快乐和温馨。',
        'summary': '记录可爱小猫咪一天的生活日常，包括玩耍、休息、卖萌等温馨时刻'
    }
    
    analysis = await optimizer._analyze_content(
        test_content['title'], 
        test_content['description'], 
        test_content['summary']
    )
    
    print(f"   📊 内容分析结果:")
    print(f"      - 情感倾向: {analysis.sentiment}")
    print(f"      - 主要情绪: {analysis.emotion}")
    print(f"      - 主题标签: {', '.join(analysis.topics)}")
    print(f"      - 复杂度级别: {analysis.complexity_level}")
    print(f"      - 目标年龄组: {analysis.target_age_group}")
    print(f"      - 内容类别: {analysis.content_category}")
    
    # 4. 演示基础AI优化
    print("\n🤖 4. 基础AI内容优化演示")
    try:
        basic_result = await optimizer.optimize_content(
            original_title=test_content['title'],
            original_description=test_content['description'],
            video_content_summary=test_content['summary'],
            target_platforms=['douyin', 'xiaohongshu', 'bilibili'],
            target_audience='年轻人',
            content_type='生活'
        )
        
        print(f"   ✅ 基础优化完成:")
        print(f"      - 优化标题: {basic_result.title}")
        print(f"      - 优化描述: {basic_result.description[:80]}...")
        print(f"      - 推荐标签: {', '.join(basic_result.tags[:8])}")
        print(f"      - 话题标签: {', '.join(basic_result.hashtags[:5])}")
        print(f"      - 关键词: {', '.join(basic_result.keywords[:8])}")
        
        # 显示平台特定优化
        if basic_result.platform_specific:
            print(f"   📱 平台特定优化:")
            for platform, content in basic_result.platform_specific.items():
                platform_name = optimizer.platform_configs.get(platform, {}).get('name', platform.title())
                print(f"      - {platform_name}: {content.get('title', 'N/A')[:40]}...")
                
    except Exception as e:
        print(f"   ❌ 基础优化失败: {e}")
    
    # 5. 演示增强AI优化
    print("\n⭐ 5. 增强AI内容优化演示")
    try:
        enhanced_result = await optimizer.enhanced_optimize_content(
            original_title=test_content['title'],
            original_description=test_content['description'],
            video_content_summary=test_content['summary'],
            target_platforms=['douyin', 'tiktok', 'xiaohongshu'],
            target_audience='年轻人',
            content_type='生活',
            enable_advanced_features=True
        )
        
        print(f"   ✅ 增强优化完成:")
        print(f"      - 优化标题: {enhanced_result.title}")
        print(f"      - 优化描述: {enhanced_result.description[:80]}...")
        print(f"      - 推荐标签: {', '.join(enhanced_result.tags[:8])}")
        
        # 显示高级功能结果
        if enhanced_result.cover_suggestions:
            print(f"   🎨 封面设计建议 ({len(enhanced_result.cover_suggestions)}个):")
            for i, suggestion in enumerate(enhanced_result.cover_suggestions[:3], 1):
                print(f"      {i}. {suggestion[:60]}...")
                
        if enhanced_result.sentiment_analysis:
            sentiment = enhanced_result.sentiment_analysis
            print(f"   😊 情感分析:")
            print(f"      - 整体情感: {sentiment.get('overall_sentiment', 'N/A')}")
            print(f"      - 主要情绪: {sentiment.get('primary_emotion', 'N/A')}")
            print(f"      - 语调风格: {sentiment.get('tone', 'N/A')}")
            print(f"      - 情感关键词: {', '.join(sentiment.get('emotional_keywords', [])[:5])}")
            
        if enhanced_result.trending_score:
            print(f"   📈 热门度评分: {enhanced_result.trending_score:.2f}/1.0")
            
        if enhanced_result.engagement_prediction:
            print(f"   💬 互动率预测:")
            for platform, rate in enhanced_result.engagement_prediction.items():
                platform_name = optimizer.platform_configs.get(platform, {}).get('name', platform)
                print(f"      - {platform_name}: {rate:.1%}")
                
        if enhanced_result.multilingual_versions:
            print(f"   🌍 多语言版本 ({len(enhanced_result.multilingual_versions)}种语言):")
            for lang, content in enhanced_result.multilingual_versions.items():
                print(f"      - {lang.upper()}: {content.get('title', 'N/A')[:40]}...")
                
    except Exception as e:
        print(f"   ❌ 增强优化失败: {e}")
    
    # 6. 演示情感和主题检测
    print("\n🎭 6. 情感和主题检测演示")
    test_cases = [
        {
            'text': '今天心情超级好，分享一个让人开心的视频',
            'expected': '积极情感'
        },
        {
            'text': '最新科技产品评测，数码设备技术分析',
            'expected': '科技主题'
        },
        {
            'text': '美食烹饪教程，教大家做好吃的料理',
            'expected': '美食主题'
        },
        {
            'text': '震惊！这个视频太令人惊讶了',
            'expected': '惊讶情绪'
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        sentiment = optimizer._detect_sentiment(case['text'])
        emotion = optimizer._detect_emotion(case['text'])
        topics = optimizer._detect_topics(case['text'])
        category = optimizer._detect_category(case['text'])
        
        print(f"   {i}. 测试文本: {case['text']}")
        print(f"      - 情感: {sentiment}, 情绪: {emotion}")
        print(f"      - 主题: {', '.join(topics)}, 类别: {category}")
        print(f"      - 预期: {case['expected']} ✅")
    
    # 7. 演示系列内容生成
    print("\n📚 7. 系列内容生成演示")
    try:
        base_content = {
            'title': '健身入门指南',
            'description': '适合初学者的健身教程，从基础动作开始学习'
        }
        
        series = await optimizer.generate_content_series(base_content, series_count=5)
        
        print(f"   ✅ 系列内容生成成功 ({len(series)}个):")
        for i, content in enumerate(series, 1):
            print(f"      {i}. {content.get('title', 'N/A')}")
            
    except Exception as e:
        print(f"   ❌ 系列内容生成失败: {e}")
    
    # 8. 演示无障碍优化
    print("\n♿ 8. 无障碍访问优化演示")
    try:
        accessibility_content = {
            'title': '美丽的日落风景视频',
            'description': '记录了海边日落的壮观景色，包含丰富的色彩变化'
        }
        
        accessibility = await optimizer.optimize_for_accessibility(accessibility_content)
        
        print(f"   ✅ 无障碍优化完成:")
        if 'alt_text' in accessibility:
            print(f"      - 图像描述: {accessibility['alt_text']}")
        if 'subtitle_suggestions' in accessibility:
            print(f"      - 字幕建议: {accessibility['subtitle_suggestions']}")
        if 'audio_description' in accessibility:
            print(f"      - 语音描述: {accessibility['audio_description']}")
            
    except Exception as e:
        print(f"   ❌ 无障碍优化失败: {e}")
    
    # 9. 演示热门度评分和互动预测
    print("\n📊 9. 热门度评分和互动预测演示")
    test_titles = [
        "震惊！必看的病毒式传播视频",
        "日常生活分享",
        "超级热门的挑战视频",
        "普通的教程内容"
    ]
    
    for title in test_titles:
        trending_score = await optimizer._calculate_trending_score(
            title, ["热门", "推荐", "精彩"], ["douyin", "tiktok"]
        )
        print(f"   📈 '{title}' - 热门度: {trending_score:.2f}")
    
    # 10. 演示平台风格适配
    print("\n🎨 10. 平台风格适配演示")
    platforms_to_demo = ['douyin', 'xiaohongshu', 'bilibili', 'tiktok']
    
    for platform in platforms_to_demo:
        config = optimizer.platform_configs[platform]
        name = config.get('name', platform.title())
        print(f"   📱 {name}:")
        print(f"      - 风格: {config.get('style', '通用风格')}")
        print(f"      - 标题长度: {config.get('title_max_length', 50)}字符")
        print(f"      - 描述长度: {config.get('description_max_length', 1000)}字符")
        print(f"      - 最大标签数: {config.get('max_tags', 10)}个")
        print(f"      - 热门话题: {', '.join(config.get('trending_topics', [])[:5])}")
    
    print("\n" + "=" * 60)
    print("🎉 增强AI内容优化器演示完成！")
    
    print("\n📋 功能总结:")
    print("   ✅ 基础内容优化 - 标题、描述、标签生成")
    print("   ✅ 平台特定适配 - 针对不同平台的内容优化")
    print("   ✅ 情感分析 - 检测内容情感倾向和情绪")
    print("   ✅ 主题识别 - 自动识别内容主题和类别")
    print("   ✅ 封面设计建议 - AI生成封面设计方案")
    print("   ✅ 热门度评分 - 预测内容热门潜力")
    print("   ✅ 互动率预测 - 预测不同平台的互动表现")
    print("   ✅ 多语言支持 - 生成多语言版本内容")
    print("   ✅ 系列内容生成 - 创建相关系列内容")
    print("   ✅ 无障碍优化 - 提供无障碍访问支持")
    print("   ✅ 智能分析 - 复杂度、年龄组、类别分析")
    
    print("\n🚀 使用建议:")
    print("   💡 结合视频内容特点选择合适的平台")
    print("   💡 根据目标受众调整内容风格和语调")
    print("   💡 利用情感分析优化内容表达方式")
    print("   💡 参考热门度评分调整发布策略")
    print("   💡 使用封面建议提升视觉吸引力")

if __name__ == "__main__":
    asyncio.run(demo_enhanced_ai_optimizer())