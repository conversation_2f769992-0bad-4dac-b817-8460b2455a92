/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{fileUtil as e}from"../viewer/fileUtil.js";import{getActiveTasks as t,setCloseRequested as n,wrapTask as r}from"./offscreenTaskManager.js";import{offscreenConfig as a}from"./offscrenUtil.js";let o;const i=new URLSearchParams(window.location.search).get("env")||"prod",s="iframe#user-subscription-iframe",d="iframe#floodgate-activity-iframe",c="iframe#ajs-worker-iframe";let f;function m(e){const t=window.document.querySelector(e);t?.remove()}function u(e,t="ajs-worker-iframe"){const n=window.document.getElementById(t),r=a[i].acrobat_viewer_origin;n&&n.contentWindow.postMessage(e,r)}function l(e){if(!window.document.querySelector(s)&&e.cdnURL){const t=window.document;if(e.isCDNSignInEnabled){const t=new URL(e.signInURL);o=`${t}?main_op=fus`}else o=`${e.cdnURL}#/susi/fetchUserSubscription`;const n=t.createElement("iframe");n.setAttribute("src",o),n.setAttribute("id","user-subscription-iframe");t.getElementById("cdn-dnr-iframe").appendChild(n)}}function p(e){return new Promise(((t,n)=>{const r=window.document.querySelector(d);if(r){if("callFloodgateAPI"!==e.main_op)return void t(r);m(d)}if(e.iframeURL){const n=window.document,r=new URL(e.iframeURL);r.searchParams.append("anonUserUUID",e.anonUserUUID),o=r;const a=n.createElement("iframe");a.setAttribute("src",o),a.setAttribute("id","floodgate-activity-iframe"),a.onload=()=>{t(a)},a.onerror=e=>{t(null)};n.getElementById("cdn-dnr-iframe").appendChild(a)}else n(new Error("signInURL is not provided"))}))}function g(){0==t()?chrome.runtime.sendMessage({main_op:"closeOffscreenDocument",target:"background",tab:{id:""}}):setTimeout(g,100)}chrome.runtime.onMessage.addListener((function(e,t,n){if("offscreen"!==e.target)return!1;const d={getUserSubscriptions:r("getUserSubscriptions",l),getFileBuffer:r("getFileBuffer",(async e=>{const t=await fetch(e.fileBufferBlob),n=await t.blob(),r=await new Response(n).arrayBuffer();u({main_op:"getFileBuffer",tabId:e.tabId,fileInfo:{fileBuffer:r,docLastOpenState:e.docLastOpenState}})})),closeIframeOfUserSubscription:r("closeIframeOfUserSubscription",(()=>{m(s)})),getLinearizedRendition:r("getLinearizedRendition",(e=>{u({main_op:"getLinearizedRendition",tabId:e.tabId,pdfURL:e.pdfURL,pdfSize:e.pdfSize,docLastOpenState:e.docLastOpenState})})),getFileSize:r("getFileSize",u),rrvLayerRemoved:r("rrvLayerRemoved",(e=>{u({main_op:"rrvLayerRemoved",tabId:e.tabId})})),fgResponseFromCDN:r("fgResponseFromCDN",(async e=>{e.main_op="handleFgResponseFromCDN";await p(e)&&u({main_op:"fgResponseFromCDN",response:e.response},"floodgate-activity-iframe")})),callFloodgateAPI:r("callFloodgateAPI",(async e=>{e.main_op="callFloodgateAPI";await p(e)&&(f=function(e=5e3){let t,n;const r=new Promise(((e,r)=>{t=e,n=r}));return setTimeout((()=>{n(new Error("Operation timed out"))}),e),{promise:r,resolve:t,reject:n}}(),u({...e,main_op:"callFloodgateAPI"},"floodgate-activity-iframe"),f.promise.then((e=>{n({response:e})})).catch((e=>{n({response:void 0})})))})),createIframeToLoadAjsWorker:r("createIframeToLoadAjsWorker",(async e=>{const t=await(r=e,new Promise((e=>{const t=r.env||i;o=a[t].ajs_worker_uri+"?callingApp="+window.location.host;const n=window.document.querySelector(c);if(n)return void e(n);const s=r.rrvEnabled,d=window.document;if(s){const t=d.createElement("iframe");t.setAttribute("src",o),t.setAttribute("id","ajs-worker-iframe"),t.onload=()=>{e(t)},t.onerror=()=>{e(null)},d.getElementById("cdn-dnr-iframe").appendChild(t)}else e(null)})));var r;n({iframeLoaded:!!t})}))};if(!d[e.main_op])return console.warn(`Unexpected message type received: '${e.main_op}'.`),!1;(async()=>{await d[e.main_op](e)})();return!0})),window.addEventListener("message",(function(t){if(t.origin!==new URL(o).origin)return;const a=t.data;"lastUserGuid"===a.type&&(a.main_op="updateSignInStatus",delete a.type);const i={userSubscriptions:r("userSubscriptions",(()=>{chrome.runtime.sendMessage({...a,target:"background",tab:{id:""}})})),updateSignInStatus:r("updateSignInStatus",(()=>{chrome.runtime.sendMessage({...a,target:"background",tab:{id:""}})})),closeOffscreenDocument:()=>{n(!0),g()},getFileBufferRange:r("getFileBufferRange",(async()=>{try{const t=await e.getFileBufferRange({url:a.pdfURL},a.range),n=await new Response(t.buffer).arrayBuffer();u({main_op:"acceptRangesBuffer",tabId:a.tabId,fileBuffer:n,rangeKey:`${a.range.start}-${a.range.end}`,fileSize:t.fileSize})}catch{u({main_op:"acceptRangesBuffer",tabId:a.tabId,fileBuffer:-1,rangeKey:`${a.range.start}-${a.range.end}`,fileSize:-1})}})),rapidRenditionResponse:r("rapidRenditionResponse",(()=>{chrome.runtime.sendMessage({...a,target:"background",tab:{id:a.tabId}})})),rapidRenditionError:r("rapidRenditionError",(()=>{chrome.runtime.sendMessage({...a,target:"background",tab:{id:a.tabId}})})),callFloodgateAPIResponse:r("callFloodgateAPIResponse",(e=>{f.resolve(e.data)}))};i[a?.main_op]&&i[a.main_op](t)}));