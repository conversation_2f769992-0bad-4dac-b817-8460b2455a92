<!DOCTYPE html>
<!--
/*************************************************************************
*
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2024 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and may be covered by U.S. and Foreign Patents,
* patents in process, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
-->
<html lang="en">
<head>
    <link href="../css/core.css" type="text/css" rel="stylesheet" />
    <link href="../css/download-banner.css" type="text/css" rel="stylesheet" />
    <script src="../../libs/jquery-3.1.1.min.js"></script>
    <script type="module" src="./download-banner.js"></script>
</head>
<body id="download-banner">
    <div id="adobe-dc-view-downloadBanner" class="adobe-dc-view-downloadBanner">
        <div class="download-banner-main">
            <img src="../images/acrobat_reader2020_32.png" class="accordian-header-img"/>
            <span id="downloadBannerText" class="banner-info translate"></span>
            <button id="turnOnButton" class="type-spectrum-Button type-spectrum-Button--cta button">
                <span id="downloadBannerContinue" class="type-spectrum-Button-label translate"></span>
            </button>
        </div>
        <input type="image" id="tripleDotMenu" src="../images/SDC_More_18_N.svg" class="tripleDotMenu" />
        <div id="menuList" class="spectrum-Menu menuList">
            <div class="spectrum-Menu-item menuItem translate" id="doNotShowButton"></div>
            <div class="spectrum-Menu-item menuItem translate" id="closeButton"></div>
        </div>
    </div>
</body>
</html>