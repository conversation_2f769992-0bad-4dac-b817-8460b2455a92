/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{signInUtil as e}from"../../browser/js/viewer/signInUtils.js";import{dcLocalStorage as a}from"../../common/local-storage.js";import{upsellService as s}from"./upsellService.js";import{getContentLocale as t}from"./sidePanelUtil.js";import{util as n}from"../../browser/js/content-util.js";import{loggingApi as i}from"../../common/loggingApi.js";import{getGenAIServiceVariant as o}from"../../common/util.js";const r=Date.now();await a.init();const d=a.getItem("touchpoint");a.removeItem("touchpoint");const m=e=>{const a=new URLSearchParams(window.location.search),s=parseInt(a.get("tabId"));e&&chrome.runtime.sendMessage({main_op:"analytics",analytics:[e],tab:{id:s}})};m(`DCBrowserExt:SidePanel:Opened:${d||"Unspecified"}`);const c=e=>{a.setItem("touchpoint",e),window.location.reload()};window.onbeforeunload=()=>{chrome.tabs.sendMessage(l.tabId,{main_op:"removeHighlights"})},window.addEventListener("message",(async t=>{if(t.origin===l.origin)switch(t.data.main_op){case"preferences":chrome.runtime.openOptionsPage();break;case"highlightText":const n=await chrome.tabs.sendMessage(l.tabId,{...t.data});l.sendMessage({status:n,messageId:t.data.messageId});break;case"canHighlightText":const i=await chrome.tabs.sendMessage(l.tabId,{...t.data});l.sendMessage({status:i,messageId:t.data.messageId});break;case"sanitiseSources":const o=await chrome.tabs.sendMessage(l.tabId,{...t.data});l.sendMessage({status:o,messageId:t.data.messageId});break;case"removeHighlights":chrome.tabs.sendMessage(l.tabId,{...t.data});break;case"cdnReady":l.isCdnReadyResolver();break;case"reloadAll":chrome.runtime.sendMessage({main_op:"reload",touchpoint:t.data.touchpoint}),c(t.data.touchpoint);break;case"reload":c(t.data.touchpoint);break;case"analytics":m(t.data.event);break;case"signIn":e.sidePanelSignIn(l.tabId);break;case"subscribeNow":a.removeItem("upsellFromAnon"),s.clickSubscribeNow(),a.setItem("upsellFromAnon",t.data.isAnonUpsell);break;case"saveVisitorID":if(t.data.visitorID){const e=a.getItem("viewerVisitorID");a.setItem("viewerVisitorID",t.data.visitorID),e&&e!==t.data.visitorID&&m("DCBrowserExt:Analytics:viewerVisitorID:MCMID:Changed")}break;case"adminStatus":a.setItem("isAdminUser",t.data.isAdmin)}})),chrome.runtime.onMessage.addListener(((e,s,t)=>{if("reload"===e.main_op&&c(e.touchpoint),"reloadTab"===e.main_op&&e.tabId==l.tabId&&c(e.touchpoint),"post_upsell"===e.main_op&&e.tabId==l.tabId)return t({success:!0,message:"Upsell process completed successfully."}),l.sendMessage({type:"upsellComplete"}),a.removeItem("upsellFromAnon"),!0;"post_upsell_anon"===e.main_op&&e.tabId===l.tabId&&(a.removeItem("upsellFromAnon"),l.sendMessage({type:"upsellAnonTransition"})),"page_switched"===e.main_op&&e.tabId===l.tabId&&(e.isScrolled&&a.getItem("enableURLChangeOnScrollLogging")&&i.info({message:"URL changed due to page scroll when sidepanel is opened",isSidePanelOpened:!0,url:e.url}),l.sendMessage({type:"sidepanelPageSwitch",disqualified:!e.qualified})),"chrome_viewer_opened"===e.main_op&&e.activeTabId==l.tabId&&window.close()}));const l=new class{urlParams=new URLSearchParams(window.location.search);tabId=parseInt(this.urlParams.get("tabId"));iframeElement=document.getElementById("sidepanel");constructor(){const e=new URL(a.getItem("sidepanelUrl")),s="true"===a.getItem("adobeInternal"),t="false"!==a.getItem("logAnalytics"),n="false"!==a.getItem("ANALYTICS_OPT_IN_ADMIN"),c=a.getItem("appLocale")||chrome.i18n.getMessage("@@ui_locale"),l=a.getItem("sidePanelUUID");e.searchParams.append("la",t&&n),e.searchParams.append("ca",chrome.runtime.id),e.searchParams.append("cluster",o()),e.searchParams.append("locale",c),e.searchParams.append("uuid",l),e.searchParams.append("adi",s),e.searchParams.append("its",r),e.searchParams.append("ev",this.urlParams.get("version")),e.searchParams.append("ecid",a.getItem("ECID")),this.iframeElement.onload=()=>{i.info({message:"Sidepanel iframe loaded",url:this.iframeElement.src,touchpoint:d}),m(`DCBrowserExt:SidePanel:IframeLoaded:${d}`)},this.iframeElement.onerror=e=>{i.error({message:"Error in loading sidepanel iframe",error:e.toString(),url:this.iframeElement.src,touchpoint:d}),m(`DCBrowserExt:SidePanel:IframeLoadError:${d}`)},this.iframeElement.src=e.href,this.origin=e.origin,this.port=chrome.runtime.connect({name:`sidepanel_${this.tabId}_${r}`}),setInterval((()=>{this.port.postMessage({action:"keep_alive"})}),2e4)}isCdnReady=new Promise(((e,a)=>{const s=setTimeout((()=>{a(new Error("Hosted sidepanel ready event timeout"))}),1e4);this.isCdnReadyResolver=()=>{clearTimeout(s),e()}}));supportedOrigin=e=>{try{return!![/^https:\/\/([a-zA-Z\d-]+\.){0,}(adobe|acrobat)\.com(:[0-9]*)?$/].find((a=>a.test(e)))}catch(e){return!1}};sendMessage=e=>{this.iframeElement&&this.supportedOrigin(this.origin)&&this.isCdnReady.then((()=>this.iframeElement.contentWindow.postMessage(e,this.origin))).catch((e=>{i.error({message:"Error in sending message to sidepanel",error:e.toString()})}))}},p=await chrome.tabs.sendMessage(l.tabId,{main_op:"getHtmlContent"});n.consoleLog("HTML: Response from content script: ",p);let g=p?.htmlContent;const h=await t(p.textContent);l.sendMessage({type:"sidepanelHtmlContent",htmlContent:g,initialQuestion:p.initialQuestion,disqualified:p.disqualified,pageLanguage:h.language,touchpoint:d});