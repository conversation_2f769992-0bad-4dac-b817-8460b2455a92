# -*- coding: utf-8 -*-
"""
全新的一键发布系统
简单、稳定、实用的视频发布解决方案
"""

import os
import time
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum

from src.utils.logger import logger


class PublishStatus(Enum):
    """发布状态枚举"""
    PENDING = "pending"          # 等待中
    PREPARING = "preparing"      # 准备中
    UPLOADING = "uploading"      # 上传中
    PROCESSING = "processing"    # 处理中
    SUCCESS = "success"          # 成功
    FAILED = "failed"           # 失败
    CANCELLED = "cancelled"      # 已取消


@dataclass
class VideoInfo:
    """视频信息"""
    file_path: str
    title: str
    description: str = ""
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class PublishTask:
    """发布任务"""
    id: str
    video_info: VideoInfo
    target_platforms: List[str]
    status: PublishStatus = PublishStatus.PENDING
    progress: float = 0.0
    message: str = ""
    results: Dict[str, Any] = None
    created_at: float = None
    
    def __post_init__(self):
        if self.results is None:
            self.results = {}
        if self.created_at is None:
            self.created_at = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['status'] = self.status.value
        return data


class PlatformPublisher:
    """平台发布器基类"""
    
    def __init__(self, platform_name: str):
        self.platform_name = platform_name
        self.is_ready = False
    
    def prepare(self) -> bool:
        """准备发布环境"""
        try:
            logger.info(f"准备{self.platform_name}发布环境...")
            # 子类实现具体的准备逻辑
            self.is_ready = True
            return True
        except Exception as e:
            logger.error(f"{self.platform_name}环境准备失败: {e}")
            return False
    
    def publish(self, video_info: VideoInfo, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """发布视频"""
        if not self.is_ready:
            return {
                'success': False,
                'error': f'{self.platform_name}发布环境未准备好'
            }
        
        try:
            logger.info(f"开始发布到{self.platform_name}...")
            
            # 基础发布流程
            if progress_callback:
                progress_callback(0.1, f"连接到{self.platform_name}...")
            
            # 检查登录状态
            if not self._check_login():
                return {
                    'success': False,
                    'error': f'请先登录{self.platform_name}'
                }

            # 登录成功后保存登录状态（仅对抖音）
            if hasattr(self, '_save_login_state'):
                try:
                    self._save_login_state()
                    logger.info("✅ 登录状态已保存")
                except Exception as e:
                    logger.warning(f"保存登录状态失败: {e}")
            
            if progress_callback:
                progress_callback(0.3, "上传视频文件...")
            
            # 上传视频
            upload_result = self._upload_video(video_info.file_path)
            if not upload_result['success']:
                return upload_result
            
            if progress_callback:
                progress_callback(0.6, "填写视频信息...")
            
            # 填写信息
            info_result = self._fill_video_info(video_info)
            if not info_result['success']:
                return info_result
            
            if progress_callback:
                progress_callback(0.9, "提交发布...")
            
            # 提交发布
            publish_result = self._submit_publish()
            
            if progress_callback:
                progress_callback(1.0, "发布完成")
            
            return publish_result
            
        except Exception as e:
            logger.error(f"{self.platform_name}发布失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _check_login(self) -> bool:
        """检查登录状态 - 子类实现"""
        return True
    
    def _upload_video(self, video_path: str) -> Dict[str, Any]:
        """上传视频 - 子类实现"""
        return {'success': True}
    
    def _fill_video_info(self, video_info: VideoInfo) -> Dict[str, Any]:
        """填写视频信息 - 子类实现"""
        return {'success': True}
    
    def _submit_publish(self) -> Dict[str, Any]:
        """提交发布 - 子类实现"""
        return {'success': True, 'video_id': 'test_id'}
    
    def cleanup(self):
        """清理资源"""
        self.is_ready = False


class NewOneClickPublisher:
    """新的一键发布器"""
    
    def __init__(self):
        self.publishers: Dict[str, PlatformPublisher] = {}
        self.current_task: Optional[PublishTask] = None
        self.is_publishing = False
        
        # 支持的平台
        self.supported_platforms = {
            'douyin': '抖音',
            'kuaishou': '快手',
            'xiaohongshu': '小红书',
            'bilibili': 'B站'
        }
        
        logger.info("新一键发布器初始化完成")
    
    def get_supported_platforms(self) -> Dict[str, str]:
        """获取支持的平台列表"""
        return self.supported_platforms.copy()
    
    def register_publisher(self, platform: str, publisher: PlatformPublisher):
        """注册平台发布器"""
        self.publishers[platform] = publisher
        logger.info(f"注册{platform}发布器成功")
    
    def create_task(self, video_info: VideoInfo, target_platforms: List[str]) -> PublishTask:
        """创建发布任务"""
        import uuid
        
        task_id = str(uuid.uuid4())
        task = PublishTask(
            id=task_id,
            video_info=video_info,
            target_platforms=target_platforms
        )
        
        logger.info(f"创建发布任务: {task_id}")
        return task
    
    def validate_task(self, task: PublishTask) -> List[str]:
        """验证发布任务"""
        errors = []
        
        # 检查视频文件
        if not os.path.exists(task.video_info.file_path):
            errors.append(f"视频文件不存在: {task.video_info.file_path}")
        
        # 检查标题
        if not task.video_info.title.strip():
            errors.append("视频标题不能为空")
        
        # 检查平台
        for platform in task.target_platforms:
            if platform not in self.supported_platforms:
                errors.append(f"不支持的平台: {platform}")
        
        return errors
    
    def publish_task(self, task: PublishTask, progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """执行发布任务"""
        if self.is_publishing:
            return {
                'success': False,
                'error': '已有发布任务在进行中'
            }
        
        # 验证任务
        errors = self.validate_task(task)
        if errors:
            return {
                'success': False,
                'error': '任务验证失败: ' + '; '.join(errors)
            }
        
        self.current_task = task
        self.is_publishing = True
        
        try:
            task.status = PublishStatus.PREPARING
            if progress_callback:
                progress_callback(0.0, "准备发布...")
            
            # 准备发布器
            prepared_publishers = {}
            for platform in task.target_platforms:
                if platform in self.publishers:
                    publisher = self.publishers[platform]
                    if publisher.prepare():
                        prepared_publishers[platform] = publisher
                    else:
                        task.results[platform] = {
                            'success': False,
                            'error': f'{platform}发布器准备失败'
                        }
                else:
                    task.results[platform] = {
                        'success': False,
                        'error': f'{platform}发布器未注册'
                    }
            
            if not prepared_publishers:
                task.status = PublishStatus.FAILED
                return {
                    'success': False,
                    'error': '没有可用的发布器'
                }
            
            # 执行发布
            task.status = PublishStatus.UPLOADING
            total_platforms = len(prepared_publishers)
            completed_platforms = 0
            
            for platform, publisher in prepared_publishers.items():
                platform_name = self.supported_platforms[platform]
                
                def platform_progress(progress: float, message: str):
                    overall_progress = (completed_platforms + progress) / total_platforms
                    task.progress = overall_progress
                    if progress_callback:
                        progress_callback(overall_progress, f"{platform_name}: {message}")
                
                # 发布到单个平台
                result = publisher.publish(task.video_info, platform_progress)
                task.results[platform] = result
                
                completed_platforms += 1
                
                # 平台间等待
                if completed_platforms < total_platforms:
                    time.sleep(2)
            
            # 统计结果
            success_count = sum(1 for result in task.results.values() if result.get('success', False))
            total_count = len(task.target_platforms)
            
            if success_count > 0:
                task.status = PublishStatus.SUCCESS
                task.message = f"发布完成: {success_count}/{total_count} 个平台成功"
            else:
                task.status = PublishStatus.FAILED
                task.message = "所有平台发布失败"
            
            task.progress = 1.0
            
            return {
                'success': success_count > 0,
                'message': task.message,
                'results': task.results,
                'success_count': success_count,
                'total_count': total_count
            }
            
        except Exception as e:
            logger.error(f"发布任务执行失败: {e}")
            task.status = PublishStatus.FAILED
            task.message = f"发布失败: {e}"
            
            return {
                'success': False,
                'error': str(e)
            }
        
        finally:
            self.is_publishing = False
            # 清理发布器
            for publisher in self.publishers.values():
                try:
                    publisher.cleanup()
                except:
                    pass
    
    def cancel_task(self):
        """取消当前任务"""
        if self.current_task:
            self.current_task.status = PublishStatus.CANCELLED
            self.current_task.message = "任务已取消"
        
        self.is_publishing = False
        logger.info("发布任务已取消")
    
    def get_task_status(self) -> Optional[Dict[str, Any]]:
        """获取当前任务状态"""
        if self.current_task:
            return self.current_task.to_dict()
        return None


# 全局实例
_new_publisher = None

def get_new_one_click_publisher() -> NewOneClickPublisher:
    """获取新一键发布器实例"""
    global _new_publisher
    if _new_publisher is None:
        _new_publisher = NewOneClickPublisher()
    return _new_publisher
