/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{analytics as e,events as t}from"../common/analytics.js";import{dcLocalStorage as n,dcSessionStorage as s}from"../common/local-storage.js";import{loggingApi as r}from"../common/loggingApi.js";import{CACHE_PURGE_SCHEME as a,EXPRESS as o,EXPRESS_CONTEXT_MENU_ENABLED_VERBS as c}from"./constant.js";import{floodgate as i}from"./floodgate.js";import{util as l}from"./util.js";import{viewerModuleUtils as p}from"./viewer-module-utils.js";import{common as m}from"./common.js";import{expressIndexedDBScript as E}from"../common/expressindexedDB.js";import{removeExperimentCodeForAnalytics as g,setExperimentCodeForAnalytics as d}from"../common/experimentUtils.js";let u=!1;const S={},x=10,f=720,I="expressMenu",h={"dc-cv-express-standalone":"EL","dc-cv-express-standalone-control":"ELC","dc-cv-express-whatsapp-hover":"EWH","dc-cv-express-whatsapp-hover-control":"EWHC","dc-cv-express-gmail-message-view":"EGM","dc-cv-express-gmail-message-view-control":"EGMC","dc-cv-express-whatsapp-fte":"EWF","dc-cv-express-whatsapp-fte-control":"EWFC"},v="cleanUpExpressAssetMemoryMap",b="cleanUpExpressIndexedDB",_=864e5,w=6e5,A={beta:["memepcobodlebmohdlfnjiaalggfcpic","hgednelcgbmkdebjhejlmbgigmkcbemg"],release:["efaidnbmnnnibpcajpcglclefindmkaj","elhekieabhbkpmcefcoobjddigjcaadp"]},R={[o.VERBS.EFFECTS_IMAGE]:"add-effects",[o.VERBS.CROP_IMAGE]:"crop-image",[o.VERBS.REMOVE_BACKGROUND_IMAGE]:"remove-background",[o.VERBS.INSERT_OBJECT]:"insert-object",[o.VERBS.REMOVE_OBJECT]:"remove-object"};async function O(e){const t=Date.now(),n=await fetch(e);if(!n.ok)throw new Error(`Response not in 2xx range. Status: ${n.status}`);const s=await n.blob();return new Promise(((e,n)=>{const r=new FileReader;r.readAsDataURL(s),r.onloadend=()=>{const n=r.result,s=Date.now()-t;e({base64data:n,imageDownloadTime:s})},r.onerror=n}))}function T(e){const t=["en-US","en-GB"],n=l.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale"));switch(e){case"dc-cv-express-whatsapp-hover":case"dc-cv-express-whatsapp-hover-control":case"dc-cv-express-gmail-message-view":case"dc-cv-express-gmail-message-view-control":return t.includes(n);default:return!0}}async function M(e){if(!await i.hasFlag(e))return!1;const t=i.getFeatureMeta(e);if(t)try{const e=JSON.parse(t);if(e.minExtVer&&l.verCmp(e.minExtVer,chrome.runtime.getManifest().version)>0)return!1}catch(e){}return!0}function P(e){return R[e]??"edit-image"}function L(e){return(s.getItem(o.EXPRESS_SESSIONS)||{})[e]}async function C(e,t){const n=Date.now(),r=new URL(t.url).hostname,a=e.srcUrl,c=t.id,i=e.intent,p=e.touchpoint,m=await async function(){return await M("dc-cv-express-standalone")?o.VARIANT.LOE:o.VARIANT.MODAL}(),E=l.uuid(),g=s.getItem(o.EXPRESS_SESSIONS)||{};return g[E]={pageDomain:r,imageURL:a,tabId:c,intent:i,touchpoint:p,variant:m,clickTimeStamp:n},s.setItem(o.EXPRESS_SESSIONS,g),E}async function D(e,t){const n=await C(e,t);await y(n)}function N(e){const t=s.getItem(o.EXPRESS_SESSIONS)||{};return Object.keys(t).find((n=>{const r=t[n];return r.tabId===e&&!r.fetchedFromContentScript&&r.variant===o.VARIANT.MODAL&&(t[n].fetchedFromContentScript=!0,s.setItem(o.EXPRESS_SESSIONS,t),!0)}))}async function y(a){const c=L(a);let i;const p=c?.pageDomain;try{if(i=c.intent,c.variant===o.VARIANT.LOE){const e=c.clickTimeStamp,t=e+_;E.storeExpressAssetInfoInIndexedDB(a,c.imageURL,t,e),chrome.alarms.get(b,(e=>{e||chrome.alarms.create(b,{periodInMinutes:f})})),S[a]={bufferPromise:O(c.imageURL),ttl:Date.now()+w,clickTimeStamp:e,domain:p},chrome.alarms.get(v,(e=>{e||chrome.alarms.create(v,{periodInMinutes:x})}));const s=new URL(m.getExpressURLs().webAppUrl);s.searchParams.append("expressSessionId",a),s.searchParams.append("referrer",o.REFERRER);const r=P(c.intent);s.searchParams.append("editAction",r),s.searchParams.append("extId",function(){const e=Object.keys(A).filter((e=>A[e].includes(chrome.runtime.id)));return e.length>0?e[0]:"release"}());const i=l.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale"));s.searchParams.append("locale",i),"true"===n.getItem("adobeInternal")&&s.searchParams.append("setting-consoleLogLevel-performance","info"),chrome.tabs.create({url:s.href,active:!0})}else!function(e,t,n){let r=s.getItem(o.SESSIONS_WHERE_MODAL_LOADING)||[];r.push({tabId:e,touchpoint:t,domain:n}),s.setItem(o.SESSIONS_WHERE_MODAL_LOADING,r)}(c.tabId,c?.touchpoint,p),await chrome.scripting.executeScript({target:{tabId:c.tabId},files:["content_scripts/express-content-script.js"]})}catch(n){X(c?.tabId,c?.touchpoint,p),e.event(t.EXPRESS_EXECUTE_VERB_FAILED,{VERB:i,eventContext:n.toString(),expressTouchpoint:c?.touchpoint,domain:p}),r.error({message:"Error executing express verb",error:"Initiate execute verb from service worker: "+n.toString()})}}async function G(s){if(!S[s])return async function(n){let s,a,c;const i=L(n),l=await E.getExpressAssetInfoFromIndexedDB(n);if(!l)return r.error({message:"Error executing express verb",error:`Asset transfer to LOE failed: ${o.EXTERNAL_MESSAGING_ERROR_CODES.INVALID_REQUEST}`}),{status:o.RESPONSE_STATUS.FAILED,errorCode:o.EXTERNAL_MESSAGING_ERROR_CODES.INVALID_REQUEST};s=l.url,a=l.clickTimeStamp;try{c=(await O(s)).base64data}catch(n){return e.event(t.EXPRESS_EXECUTE_VERB_FAILED,{eventContext:n.toString(),domain:i?.pageDomain,expressTouchpoint:i?.touchpoint}),r.error({message:"Error executing express verb",error:`Asset transfer to LOE failed: ${n.toString()}`}),{status:o.RESPONSE_STATUS.FAILED,errorCode:o.EXTERNAL_MESSAGING_ERROR_CODES.IMAGE_FETCH_FAILED,clickTimeStamp:a}}return{status:o.RESPONSE_STATUS.SUCCESS,buffer:c,clickTimeStamp:a}}(s);{const a=L(s)?.touchpoint,c=S[s]?.domain,i=S[s].clickTimeStamp,l=Date.now()-i;try{const{base64data:e,imageDownloadTime:t}=await S[s].bufferPromise,a=Date.now()-i;"true"===n.getItem("adobeInternal")&&(console.log("Time required to receive request from express webapp "+l),console.log("Image Download Time "+t),console.log("Time required to handover to express "+a));const p={imageDownloadTime:t,timeRequiredToHandoverToExpress:a,expressCommunicationReceivedTime:l,imageSize:e.length};return r.info({message:o.PERF_METRIC_LOG_MESSAGE,...p}),{status:o.RESPONSE_STATUS.SUCCESS,buffer:e,clickTimeStamp:i,domain:c}}catch(n){e.event(t.EXPRESS_EXECUTE_VERB_FAILED,{eventContext:n.toString(),domain:c,expressTouchpoint:a}),r.error({message:"Error executing express verb",error:`Asset transfer to LOE failed: ${n.toString()}`});const s=o.EXTERNAL_MESSAGING_ERROR_CODES.IMAGE_FETCH_FAILED;return{status:o.RESPONSE_STATUS.FAILED,errorCode:s,clickTimeStamp:i,domain:c}}}}function F(e){chrome.scripting.executeScript({target:{tabId:e},func:()=>{!function(e){const t=document.querySelector(`#${e}`);t?.parentElement?.removeChild(t),document.body.style.overflow="auto"}("expressAcrobatExtension"),dialogToBeReopened&&(dialogToBeReopened.showModal(),dialogToBeReopened=void 0)}})}function U(){const e="true"===n.getItem("adobeInternal"),t=n.getItem("installSource");return e||"admin"!==t}async function B(){const e=U();for(const t of Object.keys(h)){const n=h[t];e&&await M(t)&&T(t)?d(n):g(n)}g("Exp_Mod"),g("Exp_Loe"),g("EC"),g("EWP"),g("EWPC"),g("EGN"),g("EGNC"),g("ECS"),g("ECSC"),g("EI2"),g("EI2C"),g("EGI"),g("EGIC"),g("EWSC"),g("EWS")}async function V(e){const t=["http://*/*","https://*/*"];if((await j(e)).enableExpressContextMenu){if(!u){u=!0;const e=k(I,l.getTranslation("expressEditImageParentContextMenu"),["image"],t);chrome.contextMenus.create({id:"poweredByAdobeExpress",parentId:e,title:l.getTranslation("expressEditImagePoweredByExpressContextMenu"),contexts:["image"],enabled:!1,documentUrlPatterns:t}),chrome.contextMenus.create({id:"separatorForExpressMenu",parentId:e,type:"separator",contexts:["image"],documentUrlPatterns:t}),c.forEach((n=>{const s=n+"ContextMenu";k(s,l.getTranslation(s),["image"],t,e)}))}const e=await i.hasFlag("dc-cv-enable-splunk-logging",a.NO_CALL);p.enableSplunk(e)}else!async function(){u&&(u=!1,chrome.contextMenus.remove("poweredByAdobeExpress"),chrome.contextMenus.remove("separatorForExpressMenu"),Object.keys(c).forEach((e=>{const t=c[e]+"ContextMenu";chrome.contextMenus.remove(t)})),chrome.contextMenus.remove(I))}()}async function j(e){let t={enableExpressContextMenu:!1,enableExpressOptionsPagePreference:!1,enableExpressTooltip:!1};const s=await i.hasFlag("dc-cv-express-context-menu")||await M("dc-cv-express-standalone"),r=await i.hasFlag("dc-cv-express-context-menu-tooltip");if(null!=e&&""!==e||null!=(e=n.getItem("express-touch-points"))&&""!==e||(e=!0),await B(),s&&U()&&(t.enableExpressOptionsPagePreference=!0,e&&"false"!==e)){t.enableExpressContextMenu=!0;const e="true"===n.getItem(o.CONTEXT_MENU_INTERACTION_DONE);t.enableExpressTooltip=!e&&r}return n.removeItem("express-context-menu-fg-enabled-analytics-logged"),n.removeItem("express-image-right-click-fg-enabled-analytics-logged"),t}function k(e,t,n,s,r){return chrome.contextMenus.create({id:e,parentId:r,title:t,contexts:n,documentUrlPatterns:s})}function W(n){const a=function(e){let t=[],n=s.getItem(o.SESSIONS_WHERE_MODAL_LOADING)||[];n=n.filter((n=>n.tabId!==e||(t.push(n),!1))),t.length>0&&s.setItem(o.SESSIONS_WHERE_MODAL_LOADING,n);return t}(n);a.forEach((n=>{const s="Tab closed before express modal opened";r.error({message:"Error executing express verb",error:s}),e.event(t.EXPRESS_TAB_CLOSED_BEFORE_EXPRESS_LOADED,{eventContext:s,expressTouchpoint:n.touchpoint,domain:n.domain})}))}function X(e,t,n){let r=s.getItem(o.SESSIONS_WHERE_MODAL_LOADING)||[];const a=r.findIndex((s=>s.tabId===e&&s.touchpoint===t&&s.domain===n));a>-1&&(r.splice(a,1),s.setItem(o.SESSIONS_WHERE_MODAL_LOADING,r))}async function H(){const e={enableWhatsappPreviewExpressMenu:!1,enableExpressOptionsPagePreference:!1},t=await i.hasFlag("dc-cv-express-whatsapp-preview");let s=n.getItem("express-touch-points");return s=""===s||s,await B(),t&&U()&&(e.enableExpressOptionsPagePreference=!0,s&&"false"!==s&&(e.enableWhatsappPreviewExpressMenu=!0)),n.removeItem("express-whatsapp-preview-fg-enabled-analytics-logged"),n.removeItem("express-whatsapp-preview-fg-control-enabled-analytics-logged"),e}async function q(){const s={enableWhatsappHoverExpressMenu:!1,enableExpressOptionsPagePreference:!1},r=l.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale"));if(!["en-US","en-GB"].includes(r))return s;const a=await i.hasFlag("dc-cv-express-whatsapp-hover"),o=await i.hasFlag("dc-cv-express-whatsapp-hover-control");let c=n.getItem("express-touch-points");return c=""===c||c,await B(),a&&U()?(s.enableExpressOptionsPagePreference=!0,"true"!==n.getItem("express-whatsapp-hover-fg-enabled-analytics-logged")&&(n.setItem("express-whatsapp-hover-fg-enabled-analytics-logged","true"),e.event(t.EXPRESS_WHATSAPP_HOVER_MENU_FG_ENABLED)),c&&"false"!==c&&(s.enableWhatsappHoverExpressMenu=!0)):o&&U()&&"true"!==n.getItem("express-whatsapp-hover-fg-control-enabled-analytics-logged")&&(n.setItem("express-whatsapp-hover-fg-control-enabled-analytics-logged","true"),e.event(t.EXPRESS_WHATSAPP_HOVER_MENU_FG_CONTROL_ENABLED)),s}async function $(){const e={enableGmailNVExpressMenu:!1,enableExpressOptionsPagePreference:!1},t=await i.hasFlag("dc-cv-express-gmail-native-viewer");let s=n.getItem("express-touch-points");return s=""===s||s,await B(),t&&U()&&(e.enableExpressOptionsPagePreference=!0,s&&"false"!==s&&(e.enableGmailNVExpressMenu=!0)),n.removeItem("express-gmail-native-viewer-fg-enabled-analytics-logged"),n.removeItem("express-gmail-native-viewer-fg-control-enabled-analytics-logged"),e}async function J(){const s={enableGmailMessageViewCTA:!1,enableExpressOptionsPagePreference:!1},r=l.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale"));if(!["en-US","en-GB"].includes(r))return s;const a=await i.hasFlag("dc-cv-express-gmail-message-view"),o=await i.hasFlag("dc-cv-express-gmail-message-view-control");let c=n.getItem("express-touch-points");return c=""===c||c,await B(),a&&U()?("true"!==n.getItem("express-gmail-message-view-fg-enabled-analytics-logged")&&(n.setItem("express-gmail-message-view-fg-enabled-analytics-logged","true"),e.event(t.EXPRESS_GMAIL_MESSAGE_VIEW_FG_ENABLED)),s.enableExpressOptionsPagePreference=!0,c&&"false"!==c&&(s.enableGmailMessageViewCTA=!0)):o&&U()&&"true"!==n.getItem("express-gmail-message-view-fg-control-enabled-analytics-logged")&&(n.setItem("express-gmail-message-view-fg-control-enabled-analytics-logged","true"),e.event(t.EXPRESS_GMAIL_MESSAGE_VIEW_FG_CONTROL_ENABLED)),s}async function Q(){const e={enableGoogleImagePreviewExpressMenu:!1,enableExpressOptionsPagePreference:!1},t=await i.hasFlag("dc-cv-express-google-image-preview");let s=n.getItem("express-touch-points");return s=""===s||s,await B(),t&&U()&&(e.enableExpressOptionsPagePreference=!0,s&&"false"!==s&&(e.enableGoogleImagePreviewExpressMenu=!0)),n.removeItem("express-google-image-preview-fg-enabled-analytics-logged"),n.removeItem("express-google-image-preview-fg-control-enabled-analytics-logged"),e}chrome.alarms.onAlarm.addListener((function(e){e&&e.name===v&&(Object.keys(S).forEach((e=>{S[e]?.ttl<Date.now()&&delete S[e]})),0===Object.keys(S).length&&chrome.alarms.clear(v))})),chrome.alarms.onAlarm.addListener((function(e){e&&e.name===b&&E.removeExpressAssetInfoFromIndexedDB().then((()=>{E.getExpressAssetCount().then((e=>{0===e&&chrome.alarms.clear(b)}))}))}));export{y as executeExpressVerb,F as closeExpress,V as toggleExpressTouchpoints,j as isExpressContextMenuEnabled,G as getExpressAssetResponse,O as imageUrlToBase64,P as verbNameToIntent,X as removeSessionFromUnloadedExpressSessions,W as expressModalTabCloseListener,H as isWhatsappPreviewExpressMenuEnabled,q as isWhatsappHoverExpressMenuEnabled,$ as isGmailNativeViewerExpressMenuEnabled,Q as isGoogleImagePreviewExpressMenuEnabled,J as isGmailMessageViewExpressMenuEnabled,N as getModalSessionId,L as getExpressSession,D as launchExpress};