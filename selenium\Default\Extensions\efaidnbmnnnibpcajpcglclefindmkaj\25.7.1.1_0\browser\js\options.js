/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{analytics as e,events as t}from"../../common/analytics.js";import{dcLocalStorage as n}from"../../common/local-storage.js";import{util as s}from"./content-util.js";import{privateApi as a}from"./content-privateApi.js";import{LOCALES as i,LOCAL_FILE_PERMISSION_URL as o,OptionPageActions as r}from"../../common/constant.js";import{getGenAIServiceVariant as c}from"../../common/util.js";import l from"./highlight-overlay.js";await n.init(),$((function(){const p=10,h={AnalyticsOptinTitle:"analyticsOptinTitle",AnalyticsOptinDescription:"web2pdfOptOut",AnalyticsOptinDisabledDescription:"web2pdfOptOutDisabled",AnalyticsLearnMoreLink:"LearnMoreURL",AnalyticsLearnMoreTitle:"web2pdfPrivacy",ShowPDFToolsTitle:"web2pdfShowPDFTools",ShowPDFToolsDescription:"web2pdfFrictionlessToggleDescription",ViewerOwnershipTitle:"pdfOwnershipExploreOptionsTitle",ViewerOwnershipDescription:"pdfOwnershipExploreOptionsDescription",ViewConvertedPDFInAcrobatTitle:"web2pdfOpenConvertedPDFTitle",ViewConvertedPDFInAcrobatDescription:"web2pdfOpenConvertedPDFDescription",PersistentOpenInAcrobatTitle:"web2pdfShowPersistentOpen",PersistentOpenInAcrobatDescription:"web2pdfShowPersistentOpenDescription",WebpageConversionTitle:"webpagePDFConversion",WebpageConversionDescription:"webpagePDFConversionDescription",EnableGenAIFeaturesTitle:"enableGenAIFeaturesTitle",EnableGenAIFeaturesDescription:"enableGenAIFeaturesDescription",PromptSuccessMessage:"optionsSuccessPrompt",PromptSuccessReloadRequiredMessage:"optionsSuccessPromptReloadRequired",PromptFailureMessage:"optionsFailurePrompt",AppearancePrefTitle:"appearancePrefTitle",AppearancePrefDesc:"appearancePrefDesc",AppearancePrefOp1:"appearancePrefOp1",AppearancePrefOp2:"appearancePrefOp2",AppearancePrefOp3:"appearancePrefOp3",SaveLocationTitle:"saveLocationPreferenceTitle",SaveLocationDescription:"saveLocationPreferenceDescription",SaveLocationOp1:"saveLocationOp1",SaveLocationOp2:"saveLocationOp2",SaveLocationOp3:"saveLocationOp3",userLocale:"userLocale",acrobatGsuiteTouchPointPreferenceTitle:"acrobatGsuiteTouchPointPreferenceTitle",acrobatGsuiteTouchPointPreferenceDescription:"acrobatGsuiteTouchPointPreferenceDescription",acrobatTouchPointPreferenceTitle:"acrobatTouchPointInOtherSitesPreferenceTitle",acrobatTouchPointPreferenceDescription:"acrobatTouchPointInOtherSitesPreferenceDescription",acrobatTouchPointInOtherSurfacesPreferenceDescription:"acrobatTouchPointInOtherSurfacesPreferenceDescription",expressTouchpointPreferenceTitle:"expressTouchpointPreferenceTitle",expressTouchpointPreferenceDescription:"expressTouchpointPreferenceDescription",AIMarkerTouchpointPreferenceTitle:"aiMarkerTouchpointPreferenceTitle",AIMarkerTouchpointPreferenceDescription:"aiMarkerTouchpointPreferenceDescription",localFileOptionTitle:"localFileOptionTitle",localFileOptionDescription:"localFileOptionDesc",askAssistantBannerTitle:"askAssistantBannerTitle",askAssistantBannerContentText:"askAssistantBannerContentText"};let d=[],u={},g=null,T=null;class m{constructor(){this.showEligibleSaveButton(),this.ID="#web2pdfSaveButton",this.closePromptID="#prompt-close",this.enableState=!1,this.optionStates=new Set,this.timeoutId=null,this.reloadRequiredForPreference=!1,$(this.ID).click((e=>this.SavePreferences(e))),$(this.closePromptID).click((e=>this.HideSavedPrompt(e)))}showEligibleSaveButton(){let e,t;[e,t]=[$("#saveButtonHeader"),$("#saveButtonFooter")],e.removeClass("hidden"),t.remove()}Reset(){this.enableState=!1,this.optionStates.clear(),$(this.ID).prop("disabled",!0)}setEnabled(e){this.optionStates.add(e),$(this.ID).prop("disabled",!1),this.enableState=!0}setDisabled(e){this.optionStates.has(e)&&(this.optionStates.delete(e),0===this.optionStates.size&&($(this.ID).prop("disabled",!0),this.enableState=!1))}GetState(){return this.enableState}SavePreferences(){const e={analytics:[]};for(const t in u)u[t].hasUpdated&&(e[t]=u[t].GetCurrentState());const t=d.find((e=>"pdfViewer"===e.storageKey&&e.hasUpdated));if(t){let e;!0===t.currentState||"true"===t.currentState?(n.setItem("viewer-enabled-source","ownership-consent"),e="true"):e="false",s.isEdge()&&s.messageToMain({main_op:"pdfViewerChanged",value:e})}u.environment&&u.environment.hasUpdated&&(s.messageToMain({main_op:"init-floodgate"}),s.messageToMain({main_op:"closeOffscreenDocument"})),d.filter((e=>e.hasUpdated)).map((e=>e.SavePreferences())).filter((e=>e)).forEach((t=>e.analytics.push([t,{optionsPageSource:T}]))),function(e,t){s.messageToMain({main_op:"save-preferences",...e},t)}(e,(e=>{e.success?this.ShowSavedPrompt(!0):this.ShowSavedPrompt(!1),this.Reset(),x((async e=>{L(e.settings),await R(e),C()}))}))}ShowSavedPrompt(e){const t=$("#savePrompt"),n=$(".prompt"),a=$("#optionsSuccessPrompt");e?(this.reloadRequiredForPreference?a.text(s.getTranslation(h.PromptSuccessReloadRequiredMessage)):a.text(s.getTranslation(h.PromptSuccessMessage)),this.reloadRequiredForPreference=!1,n.removeClass("failure")):(a.text(s.getTranslation(h.PromptFailureMessage)),n.addClass("failure")),t.show(),this.timeoutId&&clearTimeout(this.timeoutId),this.timeoutId=setTimeout((e=>this.HideSavedPrompt()),5e3)}HideSavedPrompt(){$("#savePrompt").hide()}}class f{constructor(e,t,s,a){this.titleId=e,this.descriptionId=t,this.storageKey=a,this.defaultState=n.getItem(this.storageKey)||s,this.currentState=this.defaultState,this.hasUpdated=!1,this.savePreferencesButton=null}AttachEventHandler(e){this.savePreferencesButton=e}RenderInputItem(){return $("<div/>")}Render(e=""){const t=$("<div/>",{class:"option "+e}),n=$("<label/>",{class:"label",for:this.titleId}),a=$("<span/>",{class:"label-title",text:s.getTranslation(this.titleId)||this.titleId,id:this.titleId}),i=$("<span/>",{class:"label-description",text:s.getTranslation(this.descriptionId)||this.descriptionId,id:this.descriptionId});n.append(a),n.append(i),t.append(n);const o=this.RenderInputItem();return t.append(o),t}GetCurrentState(){return this.currentState}SavePreferences(){this.hasUpdated&&(this.storageKey&&(s.consoleLog("Updating Preference for ",this.storageKey,"from",n.getItem(this.storageKey,this.defaultState),"to",this.currentState),"pdfViewer"===this.storageKey&&(n.getWithTTL("ownership-upgrade")&&!1===this.currentState&&(s.messageToMain({main_op:"analytics",analytics:[[t.USE_ACROBAT_VIEWER_DISABLED_DEFAULT_OWNERSHIP_EXPERIMENT,{SOURCE:"OptionsPage",EXPERIMENT:n.getItem("experiment-ownership")}],[t.USE_ACROBAT_VIEWER_DISABLED_DEFAULT_OWNERSHIP,{SOURCE:"OptionsPage"}]]}),n.removeItem("ownership-upgrade"),n.removeItem("defaultOwnerShipExperiment")),a.setViewerState(!0===this.currentState?"enabled":"disabled"),g.reloadRequiredForPreference=!0,this.publishDVStatusUpdate("gmail"),this.publishDVStatusUpdate("gdrive")),"acrobat-touch-points-in-other-surfaces"!==this.storageKey&&"acrobat-gsuite-touch-points"!==this.storageKey||(g.reloadRequiredForPreference=!0,this.sendAcrobatTouchPointsAlert(this.currentState)),"express-touch-points"==this.storageKey&&(g.reloadRequiredForPreference=!0,chrome.runtime.sendMessage({main_op:"toggle-express-touch-points",allowed:this.currentState})),n.setItem(this.storageKey,String(this.currentState))),this.hasUpdated=!1,this.defaultState=this.currentState)}sendAcrobatTouchPointsAlert(e){let t;t=e?"acrobatTouchPointsEnabled":"acrobatTouchPointsDisabled",chrome.tabs.query({url:["https://mail.google.com/*","https://drive.google.com/*","https://outlook.office365.com/*","https://outlook.office.com/*","https://outlook.live.com/*"]},(function(e){e?.forEach((function(e){chrome.tabs.sendMessage(e.id,{content_op:t})}))}))}publishDVStatusUpdate=e=>{chrome.runtime?.sendMessage({main_op:"changeDefaultViewershipForSurface",surfaceId:e,isDefault:this.currentState,source:"extensionPreferencePage"})}}class S extends f{constructor(e,t,s,a,i,o){super(e,t,s,a),this.defaultState=function(e,t=!1){const s=n.getItem(e);return s&&""!==s?"true"===s:t}(this.storageKey,s),this.currentState=this.defaultState,this.analyticsId={enabled:i,disabled:o}}onToggleChange(e){const t=$(e.currentTarget);this.currentState=!t.hasClass("checked"),this.hasUpdated=!this.hasUpdated,t.toggleClass("checked"),this.savePreferencesButton&&this.currentState===this.defaultState?this.savePreferencesButton.setDisabled(this.titleId):this.savePreferencesButton.setEnabled(this.titleId),M.checkAndRemoveIfHighlightedSettingChanged(t[0])}RenderDisabledInputItem(){const e=$("<span/>"),t=$("<button/>",{class:"option-toggle-disabled"});return e.append(t),e}RenderInputItem(){const e=$("<span/>"),t=$("<button/>",{class:"option-toggle"});return t.click((e=>this.onToggleChange(e))),this.currentState&&t.addClass("checked"),e.append(t),e}GetAnalytics(){return this.currentState?this.analyticsId.enabled:this.analyticsId.disabled}SavePreferences(){return super.SavePreferences(),this.GetAnalytics()}}class I extends f{constructor(e,t,s,a,i,o,r){super(e,t,a,i),this.valuesMap=s,this.onSavePreferences=o,r&&!n.getItem(i)&&n.setItem(i,String(a))}onSelectionChange(e){this.currentState=$(e.currentTarget).val(),this.currentState===this.defaultState?(this.hasUpdated=!1,this.savePreferencesButton&&this.savePreferencesButton.setDisabled(this.titleId)):(this.hasUpdated=!0,this.savePreferencesButton&&this.savePreferencesButton.setEnabled(this.titleId)),M.checkAndRemoveIfHighlightedSettingChanged(e.currentTarget)}RenderInputItem(){const e=$("<span/>"),t=$("<select/>",{class:"option-select"});for(const e in this.valuesMap){const n=$("<option/>",{value:e,text:this.valuesMap[e]});t.append(n)}return t.change((e=>this.onSelectionChange(e))),this.currentState&&t.val(this.currentState),e.append(t),e}SavePreferences(){const e=this.defaultState;super.SavePreferences(),this.onSavePreferences&&this.onSavePreferences(this.currentState,e)}}class P extends S{constructor(){let e="false"===n.getItem("ANALYTICS_OPT_IN_ADMIN")?h.AnalyticsOptinDisabledDescription:h.AnalyticsOptinDescription;super(h.AnalyticsOptinTitle,e,!0,"logAnalytics",t.OPTIONS_ENABLED_ANALYTICS,t.OPTIONS_DISABLED_ANALYTICS),this.learnMoreLink=h.AnalyticsLearnMoreLink,this.learnMoreTitle=h.AnalyticsLearnMoreTitle}Render(){const e=$("<div/>",{class:"option"}),a=$("<label/>",{class:"label",for:this.titleId}),i=$("<span/>",{class:"label-title",text:s.getTranslation(this.titleId),id:this.titleId}),o=$("<span/>",{class:"label-description",text:s.getTranslation(this.descriptionId)+" ",id:this.descriptionId}),r=$("<a/>",{class:"learn-more",href:s.getTranslation(this.learnMoreLink),text:s.getTranslation(this.learnMoreTitle),target:"_blank"});let c;return r.click((function(){s.messageToMain({main_op:"analytics",analytics:[[t.OPTIONS_ABOUT_ADOBE_ACROBAT_CLICKED]]})})),a.append(i),a.append(o),e.append(a),"false"===n.getItem("ANALYTICS_OPT_IN_ADMIN")?c=this.RenderDisabledInputItem():(c=this.RenderInputItem(),o.append(r)),e.append(c),e}SavePreferences(){if(this.hasUpdated)return this.hasUpdated=!1,this.defaultState=this.currentState,this.GetAnalytics()}}class E extends S{constructor(e,t,n,s,a,i){super(e,t,n,s,a,i)}Render(){return $("<div/>",{class:"ai-contextual-menu-toggle"}).append(super.Render())}}class O extends f{constructor(e,t,s){super(e,t,[],s),this.currentState=n.getItem(this.storageKey)||[]}RenderInputItem(){const e=$("<div/>",{class:"domain-container"}),t=this.renderTags(this.currentState);return $("<button/>",{id:"show-more-btn",text:"Show all"}).click((()=>{})),e.append(t),e}renderTags(e){const t=$("<div/>",{class:"tag-list",id:"tag-list"});for(let n of e){const e=$("<div/>",{class:"tag"});e.html(`${n} <span class="close-btn">×</span>`),t.append(e)}return t.on("click",".close-btn",(e=>{const t=e.currentTarget.parentElement,n=t.textContent.trim();this.removeDomain(n.slice(0,-2)),t.remove()})),t}Render(){if(this.currentState?.length)return super.Render("domain-option")}removeDomain(e){this.currentState=this.currentState.filter((t=>t!==e)),this.hasUpdated=!0,this.savePreferencesButton?.setEnabled(this.titleId)}SavePreferences(){this.hasUpdated&&(this.storageKey&&(s.consoleLog("Updating domains for",this.storageKey,"to",this.currentState),n.setItem(this.storageKey,this.currentState)),this.hasUpdated=!1,this.defaultState=this.currentState)}}class v extends f{constructor(e,t){super(e,t,null,null)}onButtonClick(n){chrome.tabs.create({url:o,active:!0}),chrome.extension.isAllowedFileSchemeAccess().then((n=>{e.event(t.LOCAL_FILE_OPTIONS_CLICKED,{STATE:n?"Disable":"Enable"})}))}RenderInputItem(){const e=$("<span/>"),t=$("<button/>",{class:"option-localfile"}),n=$("<span/>",{class:"option-localfile-text"});return chrome.extension.isAllowedFileSchemeAccess().then((e=>{n.text(s.getTranslation(e?"Disable":"Enable")),t.append(n),t.append($("<img>",{class:"option-localfile-icon"}).attr("src","../images/SDC_LinkOut_13_N.svg"))})),t.click((e=>this.onButtonClick(e))),e.append(t),e}SavePreferences(){}}class _ extends f{constructor(e,t){super(e,t,null,null)}handleClick(){s.messageToMain({main_op:"analytics",analytics:[t.TREFOIL_HTML_PREFERENCES_CLICK]}),s.messageToMain({main_op:"showConversionSettingsDialog"})}RenderInputItem(){const e=$("<span/>"),t=$("<button/>",{class:"option-webpage-conversion"}),n=$("<span/>",{class:"option-webpage-conversion-text"});return n.text(s.getTranslation("editMegaVerbText")),t.append(n),t.click(this.handleClick),e.append(t),e}}class b extends f{constructor(e,t,n,s){super(e),this.optionClassOverride=s,this.contentTextId=t,this.contentImagePath=n}Render(){const e=$("<div/>",{class:"option "+this.optionClassOverride}),t=$("<label/>",{class:"label",for:this.titleId}),n=$("<span/>",{class:"label-title",id:this.titleId});n.append(document.createTextNode(s.getTranslation(this.titleId)||this.titleId),$("<span/>",{text:"Beta",class:"beta-label"}));const a=$("<span/>",{class:"label-description",text:s.getTranslation(this.descriptionId)||this.descriptionId,id:this.descriptionId}),i=$("<div/>",{class:"preference-item-banner-content-wrapper"}),o=$("<div/>",{class:"banner-content-text-wrapper"}),r=$("<span/>",{class:"banner-content-text",text:s.getTranslation(this.contentTextId)||this.contentTextId});if(o.append(r),i.append(o),this.contentImagePath){const e=$("<div/>",{class:"banner-content-image-wrapper"}),t=$("<img>",{class:"banner-content-image"}).attr("src",this.contentImagePath);e.append(t),i.append(e)}return t.append(n),t.append(a),e.append(t),e.append(i),e}}class A extends f{constructor(e,t){super(),this.optionClassOverride=e,this.contentImagePath=t}Render(){const e=$("<div/>",{class:`${this.optionClassOverride}`}),t=$("<div/>",{class:`option ${this.optionClassOverride}`}).append($("<img>",{class:"banner-image",src:this.contentImagePath,id:"aiMarkerTouchpointBanner"}));return e.append(t),e}}function D(e){return e.version>13}function w(e){return 13==e.version}function N(){const e=n.getItem("fteDenied")||"0",t="false"===n.getItem("pdfViewer");return SETTINGS.VIEWER_SHOW_OPEN_IN_ACRO&&(parseInt(e)===p||t)}function L(e){if(e&&e.settings){for(const t in e.settings)SETTINGS[t]=e.settings[t];(function(e){return null===e.locale})(e)&&(SETTINGS.FRICTIONLESS_ENABLED=!1)}}function C(){null==g&&(g=new m);const e=$("#toggles");$("#progress").remove(),e.empty(),d.forEach((t=>{t.AttachEventHandler(g);const n=t.Render();e.append(n)}))}async function R(e){d=[],u={};const o=new P,r=new S(h.ShowPDFToolsTitle,h.ShowPDFToolsDescription,!0,"always-show-pdftools",t.FRICTIONLESS_AUTO_SUGGESTION_ENABLED,t.FRICTIONLESS_AUTO_SUGGESTION_DISABLED),l=new E(h.AIMarkerTouchpointPreferenceTitle,h.AIMarkerTouchpointPreferenceDescription,!0,"aiMarkers",t.OPTIONS_ENABLE_AI_MARKERS,t.OPTIONS_DISABLE_AI_MARKERS),p=new S(h.ViewerOwnershipTitle,h.ViewerOwnershipDescription,!1,"pdfViewer",t.USE_ACROBAT_IN_CHROME_ENABLED,t.USE_ACROBAT_IN_CHROME_DISABLED),g=new S(h.ViewConvertedPDFInAcrobatTitle,h.ViewConvertedPDFInAcrobatDescription,!0,"ViewResultsPref",t.TREFOIL_HTML_OPENPDF_PREF_ON,t.TREFOIL_HTML_OPENPDF_PREF_OFF),T=new S(h.PersistentOpenInAcrobatTitle,h.PersistentOpenInAcrobatDescription,!0,"always-show-pdf-menu",t.OPTIONS_ENABLED_OPEN_IN_ACROBAT,t.OPTIONS_DISABLED_OPEN_IN_ACROBAT),m=new S(h.EnableGenAIFeaturesTitle,h.EnableGenAIFeaturesDescription,!0,"egaf",t.OPTIONS_ENABLE_GENAI_FEATURES,t.OPTIONS_DISABLE_GENAI_FEATURES),L=new v(h.localFileOptionTitle,h.localFileOptionDescription),C=new _(h.WebpageConversionTitle,h.WebpageConversionDescription);D(e)?(d.push(g),SETTINGS.VIEWER_ENABLED&&SETTINGS.VIEWER_ENABLED_FOR_ACROBAT?(d.push(p),N()&&d.push(T)):d.push(T)):w(e)?await async function(){const e=await async function(){return!(s.isEdge()&&await a.isMimeHandlerAvailable()&&n.getItem("openInAcrobatEnable")&&"admin"!==n.getItem("installSource"))}();SETTINGS.VIEWER_ENABLED?(d.push(p),N()&&e&&d.push(T)):e&&d.push(T)}():SETTINGS.VIEWER_ENABLED&&d.push(p);const R=[],x=["dc-cv-gmail-attachment-card-prompt","dc-cv-gmail-list-view-prompt","dc-cv-gdrive-prompt","dc-cv-embedded-pdf-touch-point","dc-cv-outlook-pdf-touch-point"];for(const ae of x)R.push(chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:ae}));const[B,F,M,y,k]=await Promise.all(R);if(B||F||M||y||k){let ie;const oe=n.getItem("locale");if("en-US"===oe||"en-GB"===oe){const re=k?h.acrobatTouchPointInOtherSurfacesPreferenceDescription:h.acrobatTouchPointPreferenceDescription;ie=new S(h.acrobatTouchPointPreferenceTitle,re,!0,"acrobat-touch-points-in-other-surfaces",t.OPTIONS_ACROBAT_TOUCH_POINT_ENABLED,t.OPTIONS_ACROBAT_TOUCH_POINT_DISABLED)}else ie=new S(h.acrobatGsuiteTouchPointPreferenceTitle,h.acrobatGsuiteTouchPointPreferenceDescription,!0,"acrobat-touch-points-in-other-surfaces",t.OPTIONS_ACROBAT_TOUCH_POINT_ENABLED,t.OPTIONS_ACROBAT_TOUCH_POINT_DISABLED);d.push(ie)}const G=await chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:"dc-cv-genai-disable-pref"}),U="true"===n.getItem("genAIEligible");U&&G&&d.push(m),SETTINGS.FRICTIONLESS_ENABLED&&d.push(r);const H=await chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:"dc-cv-local-file-fte"}),V=await chrome.extension.isAllowedFileSchemeAccess();!H||s.isEdge()||V||d.push(L);const K=s.getTranslation(h.AppearancePrefOp1),W=s.getTranslation(h.AppearancePrefOp2),q=s.getTranslation(h.AppearancePrefOp3);const j=new I(h.AppearancePrefTitle,h.AppearancePrefDesc,{auto:K,dark:W,light:q},"auto","theme",(function(e,n){s.messageToMain({main_op:"themeChange",analytics:[[t.OPTIONS_APPEARENCE_THEME_CHANGED,{OLDTHEME:n,NEWTHEME:e}]]})}),!0);if(d.push(j),!0===n.getItem("isSaveLocationPrefEnabled")){const ce=s.getTranslation(h.SaveLocationOp1),le=s.getTranslation(h.SaveLocationOp2),pe=s.getTranslation(h.SaveLocationOp3);function he(e,a){s.messageToMain({main_op:"analytics",analytics:[[t.SAVE_LOCATION_PREFERENCE_CHANGED,{OLDPREF:a,NEWPREF:e}]]}),n.setItem("selectedSaveLocationPreference",!0)}const de=new I(h.SaveLocationTitle,h.SaveLocationDescription,{ask:ce,cloud:le,local:pe},"ask","saveLocation",he,!0);d.push(de)}const X=new I(h.userLocale,null,i,n.getItem("viewer-locale")||s.getFrictionlessLocale(chrome.i18n.getMessage("@@ui_locale")),"appLocale",(async()=>{}),!0);d.push(X);const Y=[chrome.runtime.sendMessage({main_op:"express-init"}),chrome.runtime.sendMessage({main_op:"whatsapp-express-init"}),chrome.runtime.sendMessage({main_op:"gmail-express-init"}),chrome.runtime.sendMessage({main_op:"google-image-preview-express-init"})],[z,J,Q,Z]=await Promise.all(Y);if(z.shouldEnableExpressTouchpointsPreference||J.enableExpressOptionsPagePreference||Q.enableExpressOptionsPagePreference||Z.enableExpressOptionsPagePreference){const ue=new S(h.expressTouchpointPreferenceTitle,h.expressTouchpointPreferenceDescription,!0,"express-touch-points",t.OPTIONS_EXPRESS_TOUCHPOINT_ENABLED,t.OPTIONS_EXPRESS_TOUCHPOINT_DISABLED);d.push(ue)}d.push(o),u.analytics_on=o,function(e){function t(e){s.messageToMain({panel_op:"common",requestType:"update_env",env:e})}const a=new I("Development Environment","Select the target environment for the extension.",{prod:"Production",stage:"Stage",test:"Test",dev:"Dev","local-dev":"Local Dev"},e.environment||"prod","env",t),i=new I("GenAI Cluster","Select the target cluster for the extension.",{develop:"Develop",beta:"Beta",release:"Release"},c(),"cluster"),o=new I("Development Environment","Select the target environment for the extension.",{prod:"Production",stage:"Stage"},e.environment||"prod","env",t),r=D(e)?"acrobat":w(e)?"reader":"no-app",l=new I("Connected Application","Select the target connected application for the extension functionalities. This settings only mock the views, the functionality may be broken.",{acrobat:"Acrobat",reader:"Reader","no-app":"No App"},r||"acrobat"),p="true"===n.getItem("adobeInternal");SETTINGS.DEBUG_MODE?(d.push(a,i,l),u.environment=a,u.product=l):p&&(d.push(o),u.environment=o)}(e),D(e)&&d.push(C);const ee=new b(h.askAssistantBannerTitle,h.askAssistantBannerContentText,"../images/ai_assistant_web_page.svg","ai-assistant-banner-header-wrapper"),te=new S("genAIFabPreferenceTitle","genAIFabPreferenceDescription",!1,"enableGenAIFab",t.OPTIONS_ENABLE_GEN_AI_FAB,t.OPTIONS_DISABLE_GEN_AI_FAB),ne=(new class extends f{constructor(e,t,n,a){super(e,t,null,null),this.url=n,this.buttonText=s.getTranslation(a)}onButtonClick(e){chrome.tabs.create({url:this.url,active:!0})}RenderInputItem(){const e=$("<span/>"),t=$("<button/>",{class:"option-localfile"}),n=$("<span/>",{class:"option-localfile-text"});return n.text(this.buttonText),t.append(n),t.append($("<img>",{class:"option-localfile-icon"}).attr("src","../images/SDC_LinkOut_13_N.svg")),t.click((e=>this.onButtonClick(e))),e.append(t),e}SavePreferences(){}}("sidepanelLocationPreferenceTitle","sidepanelLocationPreferenceDescription","chrome://settings/appearance#:~:text=Side%20panel%20position","sidepanelLocationPreferenceButtonText"),new O("domainPreferenceTitle","","hideFabDomainList"));n.getItem("sidePanelRegistered")&&(d.push(ee),d.push(te),d.push(ne));const se=await chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:"dc-cv-genai-markers"});if(U&&G&&se){const ge=new A("ai-contextual-menu-section","../images/SummarizeBanner.svg");d.push(ge),d.push(l)}}function x(e){s.messageToMain({main_op:"fetch-preferences"},e)}function B(e){const t=d.filter((t=>t.titleId===e));return t.length>0?t[0]:null}function F(e,t,n){if(SETTINGS.TEST_MODE)return;const s=e.toggleId,a=B(s),i=e.requestType;let o,c;switch(i){case r.OPTIONS_UPDATE_TOGGLE:a?(a.currentState=e.toggleVal,a.savePreferencesButton.SavePreferences(),o=a.currentState):c="Toggle not visible.",n&&n({requestType:i,toggleId:s,response:o,error:c});case r.OPTIONS_HIGHLIGHT_SECTION:e.sectionClassNames&&function(e){M.remove();const t=[];if(e.forEach((e=>{if(!e)return;$(`.${e}`).each((function(){t.push($(this))}))})),0===t.length)return;let n=1/0,s=1/0,a=-1/0,i=-1/0;t.forEach((e=>{const t=e.offset(),o=e.outerWidth(),r=e.outerHeight();n=Math.min(n,t.top),s=Math.min(s,t.left),a=Math.max(a,t.top+r),i=Math.max(i,t.left+o)}));const o=0,r=38;n-=o,s-=r,a+=o,i+=r;const c={top:n,left:s,width:i-s,height:a-n};M.create(c,e);const l=n+(a-n)/2,p=$("<div/>").css({position:"absolute",top:l+"px",left:"0px",height:"1px",width:"1px"});$("body").append(p),p[0].scrollIntoView({behavior:"smooth",block:"center"}),p.remove()}(e.sectionClassNames);break;case r.OPTIONS_SET_OPTIONS_PAGE_SOURCE:T=e?.optionsPageSource}}let M=new l;$(document).ready((function(){M.remove(),T=null,s.translateElements(".translate"),x((async e=>{L(e),await R(e),C(),s.addMainListener(F),function(){if(!SETTINGS.TEST_MODE)return;const e="check_toggle_state",t="update_toggle_state";chrome.runtime.onMessage.addListener((function(n,s,a){if(!n.panel_op||"test"!==n.panel_op)return;const i=n.test_extension,o=n.toggleId,r=B(o);let c,l;switch(i){case e:r?c=r.currentState:l="Toggle not visible.";break;case t:r?(r.currentState=n.toggleVal,r.hasUpdated=!0,r.savePreferencesButton.SavePreferences(),c=r.currentState):l="Toggle not visible.";break;default:l="Invalid request type."}a({requestType:i,toggleId:o,response:c,error:l})}))}();await a.isMimeHandlerAvailable()||chrome.runtime.sendMessage({main_op:"updateLoadedTabsInfo"})}))})),$(document).on("visibilitychange",(()=>{document.hidden&&(M.remove(),T=null)}))}));