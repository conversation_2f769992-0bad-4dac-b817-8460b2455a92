/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
export const allowedLogs={};function e(e,r){Object.values(allowedLogs).includes(r)?console.log(`value ${r} already exists.`):allowedLogs[e]=r}e("Error in Highlighting Attribution","0"),e("DCBrowserExt:Viewer:ExtnViewerPdfOpened","1"),e("DCBrowserExt:Viewer:PDFOpenedinMimeViewer","2"),e("DCBrowserExt:Viewer:Processing:LocalPDFFile","3"),e("DCBrowserExt:Viewer:Error:FallbackToNative:Preview:Failed","4"),e("DCBrowserExt:Viewer:Iframe:Creation:Failed","5"),e("DCBrowserExt:Viewer:Error:FallbackToNative:FileDownload:Failed","6"),e("DCBrowserExt:Viewer:Error:Handshake:TimedOut","7"),e("DCBrowserExt:Viewer:FallbackToNative:Failed","8"),e("Viewer Iframe created","9"),e("indexeddb could not be opened","10"),e("Error in transaction","11"),e("Error in updating buffer","12"),e("Error in getting buffer","13"),e("Error in deleting buffer","14"),e("DCEdgeExt:Viewer:ExtnViewerPdfOpened","15"),e("DCEdgeExt:Viewer:PDFOpenedinMimeViewer","16"),e("DCEdgeExt:Viewer:Processing:LocalPDFFile","17"),e("DCEdgeExt:Viewer:Error:FallbackToNative:Preview:Failed","18"),e("DCEdgeExt:Viewer:Iframe:Creation:Failed","19"),e("DCEdgeExt:Viewer:Error:Linearization:InitialBuffer:Failed","20"),e("DCEdgeExt:Viewer:Error:FallbackToNative:FileDownload:Failed","21"),e("DCEdgeExt:Viewer:Error:Handshake:TimedOut","22"),e("DCEdgeExt:Viewer:FallbackToNative:Failed","23"),e("DCBrowserExt:Extension:Installed:Admin:Op","24"),e("DCEdgeExt:Extension:Installed:Admin:Op","25"),e("Error in reopening tab","26"),e("reloadInNativeViewer","27"),e("DCBrowserExt:Viewer:User:Error:NonMatchingCsrfToken:FailedToLogin","28"),e("DCEdgeExt:Viewer:User:Error:NonMatchingCsrfToken:FailedToLogin","29"),e("Floodgate API call stacktrace","30"),e("Error received in express flow","31"),e("Error executing express verb","32"),e("Express asset loaded time","34"),e("[Attribution Info]: Matching percentage of X path content and bound content","35"),e("Error in calculating matching percentage of X path content and bound content","36"),e("Error executing express in whatsapp","37"),e("User tried opening blob file after expiry","38"),e("Error in GSuite","39"),e("URL changed due to page scroll irrespective of sidepanel being opened or closed","40"),e("Iframe blocked due to CORS, CSP, or X-Frame-Options","41"),e("Successfully launched an iframe on URL change","42"),e("Iframe loaded but access denied (Possible CORS restriction)","43"),e("Failed to load iframe URL (possible network error)","44"),e("File downloaded","45"),e("Error executing express in google image preview","46"),e("FAB clicked","47"),e("Webpage is readable after x seconds","48"),e("Sidepanel iframe loaded","49"),e("Error in loading sidepanel iframe","50"),e("Sidepanel.js file initialized","51"),e("Opening sidepanel on current tab","52"),e("FAB dragged","53"),e("Rendering Gen AI sidepanel FAB for the webpage","54"),e("URL changed due to page scroll when sidepanel is opened","55"),e("FAB hidden for domain","56");