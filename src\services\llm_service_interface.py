# -*- coding: utf-8 -*-
"""
LLM服务统一接口
集成现有的LLM模型调用功能，提供统一的异步接口
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
import aiohttp
import logging

from src.utils.config_manager import ConfigManager
from src.utils.logger import logger


@dataclass
class LLMResponse:
    """LLM响应数据模型"""
    content: str
    model: str
    usage: Dict[str, Any] = None
    finish_reason: str = "stop"
    response_time: float = 0.0
    success: bool = True
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'content': self.content,
            'model': self.model,
            'usage': self.usage or {},
            'finish_reason': self.finish_reason,
            'response_time': self.response_time,
            'success': self.success,
            'error': self.error
        }


@dataclass
class LLMRequest:
    """LLM请求数据模型"""
    prompt: str
    max_tokens: int = 1000
    temperature: float = 0.7
    top_p: float = 0.9
    model: Optional[str] = None
    system_prompt: Optional[str] = None
    response_format: Optional[str] = None  # "json" for structured output
    
    def to_dict(self) -> Dict[str, Any]:
        data = {
            'prompt': self.prompt,
            'max_tokens': self.max_tokens,
            'temperature': self.temperature,
            'top_p': self.top_p
        }
        if self.model:
            data['model'] = self.model
        if self.system_prompt:
            data['system_prompt'] = self.system_prompt
        if self.response_format:
            data['response_format'] = self.response_format
        return data


class LLMServiceInterface(ABC):
    """LLM服务统一接口"""
    
    @abstractmethod
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本内容"""
        pass
    
    @abstractmethod
    async def generate_structured_content(self, request: LLMRequest, schema: Dict) -> LLMResponse:
        """生成结构化内容"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查服务是否可用"""
        pass


class ZhipuLLMService(LLMServiceInterface):
    """智谱AI LLM服务"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('key', '')
        self.base_url = config.get('url', 'https://open.bigmodel.cn/api/paas/v4/chat/completions')
        self.model_name = config.get('model', 'glm-4-flash')
        self.timeout = 30
        self.max_retries = 3
        
        logger.info(f"智谱AI LLM服务初始化: {self.model_name}")
    
    def is_available(self) -> bool:
        """检查服务是否可用"""
        return bool(self.api_key and not self.api_key.startswith('YOUR_'))
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本内容"""
        if not self.is_available():
            return LLMResponse(
                content="",
                model=self.model_name,
                success=False,
                error="智谱AI API密钥未配置"
            )
        
        start_time = time.time()
        
        # 构建请求数据
        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})
        
        payload = {
            "model": request.model or self.model_name,
            "messages": messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "top_p": request.top_p,
            "stream": False
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 执行请求
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(self.base_url, json=payload, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # 解析响应
                            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                            usage = data.get('usage', {})
                            finish_reason = data.get('choices', [{}])[0].get('finish_reason', 'stop')
                            
                            response_time = time.time() - start_time
                            
                            return LLMResponse(
                                content=content,
                                model=self.model_name,
                                usage=usage,
                                finish_reason=finish_reason,
                                response_time=response_time,
                                success=True
                            )
                        else:
                            error_text = await response.text()
                            logger.warning(f"智谱AI API请求失败 (尝试 {attempt + 1}/{self.max_retries}): {response.status} - {error_text}")
                            
                            if attempt == self.max_retries - 1:
                                return LLMResponse(
                                    content="",
                                    model=self.model_name,
                                    success=False,
                                    error=f"API请求失败: {response.status} - {error_text}",
                                    response_time=time.time() - start_time
                                )
                            
                            # 等待后重试
                            await asyncio.sleep(2 ** attempt)
                            
            except asyncio.TimeoutError:
                logger.warning(f"智谱AI API请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt == self.max_retries - 1:
                    return LLMResponse(
                        content="",
                        model=self.model_name,
                        success=False,
                        error="请求超时",
                        response_time=time.time() - start_time
                    )
                await asyncio.sleep(2 ** attempt)
                
            except Exception as e:
                logger.error(f"智谱AI API请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    return LLMResponse(
                        content="",
                        model=self.model_name,
                        success=False,
                        error=str(e),
                        response_time=time.time() - start_time
                    )
                await asyncio.sleep(2 ** attempt)
        
        return LLMResponse(
            content="",
            model=self.model_name,
            success=False,
            error="所有重试均失败",
            response_time=time.time() - start_time
        )
    
    async def generate_structured_content(self, request: LLMRequest, schema: Dict) -> LLMResponse:
        """生成结构化内容"""
        # 修改提示词以要求JSON格式输出
        structured_prompt = f"""{request.prompt}

请严格按照以下JSON格式返回结果：
{json.dumps(schema, ensure_ascii=False, indent=2)}

只返回JSON数据，不要包含其他文字说明。"""
        
        structured_request = LLMRequest(
            prompt=structured_prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            model=request.model,
            system_prompt=request.system_prompt
        )
        
        response = await self.generate_text(structured_request)
        
        if response.success:
            try:
                # 尝试解析JSON
                json.loads(response.content)
            except json.JSONDecodeError:
                # 如果不是有效JSON，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', response.content, re.DOTALL)
                if json_match:
                    try:
                        json.loads(json_match.group())
                        response.content = json_match.group()
                    except json.JSONDecodeError:
                        response.success = False
                        response.error = "生成的内容不是有效的JSON格式"
                else:
                    response.success = False
                    response.error = "未找到JSON格式的内容"
        
        return response


class TongyiLLMService(LLMServiceInterface):
    """通义千问 LLM服务"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config.get('key', '')
        self.base_url = config.get('url', 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions')
        self.model_name = config.get('model', 'qwen-turbo')
        self.timeout = 30
        self.max_retries = 3
        
        logger.info(f"通义千问 LLM服务初始化: {self.model_name}")
    
    def is_available(self) -> bool:
        return bool(self.api_key and not self.api_key.startswith('YOUR_'))
    
    async def generate_text(self, request: LLMRequest) -> LLMResponse:
        """生成文本内容 - 与智谱AI类似的实现"""
        if not self.is_available():
            return LLMResponse(
                content="",
                model=self.model_name,
                success=False,
                error="通义千问API密钥未配置"
            )
        
        start_time = time.time()
        
        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})
        
        payload = {
            "model": request.model or self.model_name,
            "messages": messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "top_p": request.top_p
        }
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        for attempt in range(self.max_retries):
            try:
                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                    async with session.post(self.base_url, json=payload, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                            usage = data.get('usage', {})
                            
                            return LLMResponse(
                                content=content,
                                model=self.model_name,
                                usage=usage,
                                response_time=time.time() - start_time,
                                success=True
                            )
                        else:
                            error_text = await response.text()
                            if attempt == self.max_retries - 1:
                                return LLMResponse(
                                    content="",
                                    model=self.model_name,
                                    success=False,
                                    error=f"API请求失败: {response.status}",
                                    response_time=time.time() - start_time
                                )
                            await asyncio.sleep(2 ** attempt)
                            
            except Exception as e:
                if attempt == self.max_retries - 1:
                    return LLMResponse(
                        content="",
                        model=self.model_name,
                        success=False,
                        error=str(e),
                        response_time=time.time() - start_time
                    )
                await asyncio.sleep(2 ** attempt)
        
        return LLMResponse(
            content="",
            model=self.model_name,
            success=False,
            error="所有重试均失败",
            response_time=time.time() - start_time
        )
    
    async def generate_structured_content(self, request: LLMRequest, schema: Dict) -> LLMResponse:
        """生成结构化内容"""
        structured_prompt = f"""{request.prompt}

请严格按照以下JSON格式返回结果：
{json.dumps(schema, ensure_ascii=False, indent=2)}

只返回JSON数据，不要包含其他文字说明。"""
        
        structured_request = LLMRequest(
            prompt=structured_prompt,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
            top_p=request.top_p,
            model=request.model,
            system_prompt=request.system_prompt
        )
        
        return await self.generate_text(structured_request)


class LLMServiceManager:
    """LLM服务管理器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.services: Dict[str, LLMServiceInterface] = {}
        self.default_service: Optional[LLMServiceInterface] = None
        self._initialize_services()
        
        logger.info("LLM服务管理器初始化完成")
    
    def _initialize_services(self):
        """初始化LLM服务"""
        try:
            models = self.config_manager.get_models()
            
            for model_config in models:
                model_type = model_config.get('type', '').lower()
                model_name = model_config.get('name', '')
                
                if model_type == 'zhipu':
                    service = ZhipuLLMService(model_config)
                    if service.is_available():
                        self.services[model_name] = service
                        if not self.default_service:
                            self.default_service = service
                        logger.info(f"智谱AI服务已注册: {model_name}")
                
                elif model_type == 'tongyi':
                    service = TongyiLLMService(model_config)
                    if service.is_available():
                        self.services[model_name] = service
                        if not self.default_service:
                            self.default_service = service
                        logger.info(f"通义千问服务已注册: {model_name}")
                
                # 可以继续添加其他LLM服务类型
                
            if not self.services:
                logger.warning("未找到可用的LLM服务，请检查配置")
            else:
                logger.info(f"共注册了 {len(self.services)} 个LLM服务")
                
        except Exception as e:
            logger.error(f"初始化LLM服务失败: {e}")
    
    def get_service(self, service_name: Optional[str] = None) -> Optional[LLMServiceInterface]:
        """获取LLM服务"""
        if service_name and service_name in self.services:
            return self.services[service_name]
        return self.default_service
    
    def get_available_services(self) -> List[str]:
        """获取可用的服务列表"""
        return list(self.services.keys())
    
    def is_service_available(self, service_name: Optional[str] = None) -> bool:
        """检查服务是否可用"""
        service = self.get_service(service_name)
        return service is not None and service.is_available()
    
    async def generate_text(self, 
                           prompt: str, 
                           max_tokens: int = 1000,
                           temperature: float = 0.7,
                           service_name: Optional[str] = None,
                           system_prompt: Optional[str] = None) -> LLMResponse:
        """生成文本内容"""
        service = self.get_service(service_name)
        if not service:
            return LLMResponse(
                content="",
                model="unknown",
                success=False,
                error="没有可用的LLM服务"
            )
        
        request = LLMRequest(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            system_prompt=system_prompt
        )
        
        return await service.generate_text(request)
    
    async def generate_structured_content(self, 
                                        prompt: str, 
                                        schema: Dict,
                                        max_tokens: int = 1000,
                                        temperature: float = 0.7,
                                        service_name: Optional[str] = None,
                                        system_prompt: Optional[str] = None) -> LLMResponse:
        """生成结构化内容"""
        service = self.get_service(service_name)
        if not service:
            return LLMResponse(
                content="",
                model="unknown",
                success=False,
                error="没有可用的LLM服务"
            )
        
        request = LLMRequest(
            prompt=prompt,
            max_tokens=max_tokens,
            temperature=temperature,
            system_prompt=system_prompt
        )
        
        return await service.generate_structured_content(request, schema)


# 全局实例
_llm_manager = None

def get_llm_service_manager() -> LLMServiceManager:
    """获取LLM服务管理器实例"""
    global _llm_manager
    if _llm_manager is None:
        _llm_manager = LLMServiceManager()
    return _llm_manager


# 便捷函数
async def generate_text(prompt: str, 
                       max_tokens: int = 1000,
                       temperature: float = 0.7,
                       service_name: Optional[str] = None,
                       system_prompt: Optional[str] = None) -> LLMResponse:
    """便捷的文本生成函数"""
    manager = get_llm_service_manager()
    return await manager.generate_text(prompt, max_tokens, temperature, service_name, system_prompt)


async def generate_structured_content(prompt: str, 
                                    schema: Dict,
                                    max_tokens: int = 1000,
                                    temperature: float = 0.7,
                                    service_name: Optional[str] = None,
                                    system_prompt: Optional[str] = None) -> LLMResponse:
    """便捷的结构化内容生成函数"""
    manager = get_llm_service_manager()
    return await manager.generate_structured_content(prompt, schema, max_tokens, temperature, service_name, system_prompt)