/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
class FABManager{constructor(){if(FABManager.instance)return FABManager.instance;FABManager.instance=this,this.shadowRoot=null,this.hasShownSettingsMenu=!1,this.actionableCoachmark=new ActionableCoachmark,this.isFABHiddenForNow=!1,this.enableTooltipTimeout=void 0,this.registerOpenCloseListeners(),this.registerFABEnabledDisabledListeners(),this.cursorYPosition=null,this.logFABShown(),this.genAIFabTopPosition=window.dcLocalStorage.getItem("genAIFabTopPosition"),this.enableFabDrag=window.dcLocalStorage.getItem("enableFabDrag")}async logFABShown(){await GenAIWebpageEligibilityService.shouldDisableTouchpoints()?this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:FabIcon:Disabled"]]):(this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:FabIcon:Enabled"]]),chrome.runtime.sendMessage({main_op:"log-info",log:{message:"FAB enabled"}}))}registerOpenCloseListeners(){chrome.runtime.onMessage.addListener((async e=>{switch(e.action){case"sidepanel_opened":this.removeFAB();break;case"sidepanel_closed":await GenAIWebpageEligibilityService.shouldShowTouchpoints()&&this.renderFAB()}}))}async registerFABEnabledDisabledListeners(){chrome.storage.onChanged.addListener((({enableGenAIFab:e},t)=>{"local"===t&&e&&("false"===e.newValue?this.removeFAB():this.renderFAB())}))}sendAnalyticsEvent=(e,t)=>{try{chrome.runtime.sendMessage({main_op:"analytics",analytics:e})}catch(e){}};isFABRendered(){return Boolean(this.shadowRoot)}removeFAB(){this.shadowRoot?.host?.remove(),this.shadowRoot=null,this.hasShownSettingsMenu=!1}customiseFABVisibility(){let e;const t=()=>{if(""!==this.genAIFabTopPosition)return document.removeEventListener("mousemove",o),void document.removeEventListener("scroll",t);this.isFABRendered()&&this.cursorYPosition<=.6*window.innerHeight&&this.removeFAB()},o=async t=>{if(this.cursorYPosition=t.clientY,!this.isFABRendered()&&this.cursorYPosition>.6*window.innerHeight){if(e)return;e=setTimeout((async()=>{await GenAIWebpageEligibilityService.shouldShowTouchpoints()&&this.renderFAB({fadeIn:!0}),e=void 0}),200)}else e&&(clearTimeout(e),e=void 0)};document.addEventListener("scroll",t),document.addEventListener("mousemove",o)}async shouldRenderFAB(){const e=chrome.runtime.sendMessage({type:"get_sidepanel_state"});return await initDcLocalStorage(),!(await e).isOpen&&"true"===window.dcLocalStorage.getItem("enableGenAIFab")}_computeCursorYPosition(){const e=document.createElement("div");e.id="invisibleElem",e.style.background="transparent",e.style.height="100vh",e.style.width="100vw",e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.zIndex="1000",e.setAttribute("aria-hidden","true"),document.body.appendChild(e);const t=()=>{e.parentNode&&e.parentNode.removeChild(e)};e.addEventListener("mouseenter",(e=>{this.cursorYPosition=e.clientY,t()})),setTimeout((()=>{t()}),100)}generateUUID(){try{let e=(new Date).getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){let o=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"===t?o:3&o|8).toString(16)}))}catch(e){return Math.random()}}async renderFAB(e={}){const{fadeIn:t=!1,enableHoveredState:o=!1,hoveredStateTimeout:s=7e3}=e;if(this.isFABHiddenForNow||this.actionableCoachmark.isRendered())return;if(!await this.shouldRenderFAB())return;if(this.isFABRendered())return;const i=document.createElement("div");i.id="aiFabShadowRoot",i.style.display="block",t&&(i.style.opacity="0",i.style.transition="opacity 0.4s ease-out, transform 0.4s ease-out"),this.shadowRoot=i.attachShadow({mode:"open"}),fetch(chrome.runtime.getURL("resources/SidePanel/sidePanelButton.html")).then((e=>e.text())).then((async e=>{const n=document.createElement("template");n.innerHTML=e;const a=n.content;this.shadowRoot.appendChild(a.cloneNode(!0));const r=this.shadowRoot.querySelector(".close-btn"),l=this.shadowRoot.querySelector(".acrobat-button"),d=this.shadowRoot.querySelector(".draggable-handle"),c=this.shadowRoot.querySelector(".acrobat-button-container"),h=e=>{""!=e&&null!=e&&(c.style.top=`${e}px`,c.style.bottom="auto")},u=()=>{this.hasShownSettingsMenu||(r.classList.remove("showCloseButton"),g?.classList?.remove("show-tooltip"),l?.classList.remove("expand-acrobat-button"))},m=()=>{this.hideSettingsMenu(),u()};this.enableFabDrag&&(h(this.genAIFabTopPosition),c.addEventListener("mouseover",(()=>{d.classList.add("draggable-handle-visible")})),c.addEventListener("mouseout",(()=>{d.classList.remove("draggable-handle-visible")})),l.classList.contains("acrobat-button-drag-handle-margin")||l.classList.add("acrobat-button-drag-handle-margin")),c.addEventListener("mousedown",(async()=>{if(this.hideSettingsMenu(),r.classList?.remove("showCloseButton"),!await GenAIWebpageEligibilityService.shouldDisableTouchpoints()){this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:FabIcon:Clicked"]]);const e=this.generateUUID();await initDcLocalStorage(),window.dcLocalStorage.setItem("sidePanelUUID",e),chrome.runtime.sendMessage({type:"open_side_panel",touchpoint:"FAB"}),chrome.runtime.sendMessage({main_op:"log-info",log:{message:"FAB clicked",url:window.location.href}}),setTimeout((()=>{this.sendAnalyticsEvent([["DCBrowserExt:DebugSidePanel:FabIcon:Clicked"]],{uuid:e}),chrome.runtime.sendMessage({main_op:"log-info",log:{message:"Debug FAB clicked",url:window.location.href,uuid:e}})}),500)}}));const g=this.shadowRoot?.querySelector(".tooltip-text"),b=this.shadowRoot.querySelector(".tooltip-text span:first-child");await GenAIWebpageEligibilityService.shouldDisableTouchpoints()&&(l.classList.add("disabled"),b.id="tooltipTextDisabled"),l.addEventListener("mouseenter",(()=>{r.classList.contains("showCloseButton")||r.classList.add("showCloseButton")})),l.addEventListener("mouseleave",(()=>u())),r.addEventListener("click",(e=>{e.preventDefault(),e.stopPropagation(),this.hasShownSettingsMenu?this.hideSettingsMenu():this.showSettingsMenu()})),r.addEventListener("mousedown",(e=>{e.stopImmediatePropagation(),e.preventDefault()}));let w=!1,p=0;d.addEventListener("mousedown",(e=>{e.stopImmediatePropagation(),e.preventDefault(),m(),w=!0,d.classList.add("draggable-handle-visible");const t=c.getBoundingClientRect();p=e.clientY-t.top}));const F=e=>{if(!w)return;let t=e.clientY-p;const o=c.offsetHeight,s=window.innerHeight-o-20;t=Math.max(20,Math.min(s,t)),this.genAIFabTopPosition=t,h(this.genAIFabTopPosition)},v=()=>{w&&(window.dcLocalStorage.setItem("genAIFabTopPosition",this.genAIFabTopPosition),d.classList.remove("draggable-handle-visible"),w=!1,this.genAIFabTopPosition&&(this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:FabIcon:Dragged"]]),chrome.runtime.sendMessage({main_op:"log-info",log:{message:"FAB dragged",fabTop:`${this.genAIFabTopPosition}px`}})))};this.enableFabDrag&&(document.addEventListener("mousemove",F),document.addEventListener("mouseup",v)),util.translateElements(".translate",this.shadowRoot),this.actionableCoachmark.isRendered()||document.documentElement.appendChild(i),o&&(l?.classList.add("expand-acrobat-button"),r?.classList.add("showCloseButton"),g?.classList.add("show-tooltip"),setTimeout((()=>{r?.classList.remove("showCloseButton"),g?.classList.remove("show-tooltip"),l?.classList.remove("expand-acrobat-button")}),s)),t&&requestAnimationFrame((()=>requestAnimationFrame((()=>i.style.opacity="1"))))})),this.enableTooltipTimeout&&clearTimeout(this.enableTooltipTimeout),this.cursorYPosition||this._computeCursorYPosition(),""==this.genAIFabTopPosition&&(this.enableTooltipTimeout=setTimeout((()=>this.customiseFABVisibility()),o?s:50))}hideSettingsMenu(){this.shadowRoot.querySelector(".close-btn").classList.remove("open");const e=this.shadowRoot?.querySelector(".fab-view-settings-dialog");if(e){const e=this.shadowRoot?.querySelector(".tooltip-text");e&&(e.style.display="block");const t=this.shadowRoot.querySelector(".fab-view-settings-dialog");t?.classList?.remove("showDialog")}this.hasShownSettingsMenu=!1}async hideFabForDomain(e){await initDcLocalStorage();const t=window.dcLocalStorage.getItem("hideFabDomainList")||[];t.includes(e)||t.push(e),window.dcLocalStorage.setItem("hideFabDomainList",t),chrome.runtime.sendMessage({main_op:"log-info",log:{message:"FAB hidden for domain",domain:e}})}async neverDisplayFab(){await initDcLocalStorage(),window.dcLocalStorage.setItem("enableGenAIFab","false")}_updateSettingsMenuPositionFromFAB(){const e=this.shadowRoot.querySelector(".fab-view-settings-dialog"),t=this.shadowRoot.querySelector(".acrobat-button"),o=this.shadowRoot.querySelector(".acrobat-button-container"),s=t.offsetHeight,i=e.offsetHeight;o.getBoundingClientRect().top<i+10?(e.style.bottom="auto",e.style.top=`${s}px`):(e.style.bottom=`${s}px`,e.style.top="auto")}showSettingsMenu(){this.shadowRoot.querySelector(".close-btn").classList.add("open"),fetch(chrome.runtime.getURL("resources/SidePanel/FABViewSettings.html")).then((e=>e.text())).then((e=>{const t=this.shadowRoot?.querySelector(".tooltip-text");t&&(t.style.display="none");const o=this.shadowRoot.querySelector(".fab-view-settings-dialog");o.innerHTML=e,o.classList.add("showDialog"),this.hasShownSettingsMenu=!0,util.translateElements(".translate",this.shadowRoot),this._updateSettingsMenuPositionFromFAB();const s=new URL(window.location.href).hostname;["hideForNow","hideFabForDomain","neverDisplayFab","preferences"].forEach((e=>{this.shadowRoot.getElementById(e)?.addEventListener("click",(async()=>{switch(e){case"hideForNow":this.isFABHiddenForNow=!0,this.removeFAB(),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:SettingsMenu:RemoveFab"]]);break;case"hideFabForDomain":this.hideFabForDomain(s),this.isFABHiddenForNow=!0,this.removeFAB(),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:SettingsMenu:RemoveFabForDomain"]]);break;case"neverDisplayFab":this.neverDisplayFab(),this.removeFAB(),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:SettingsMenu:NeverDisplay"]]);break;case"preferences":chrome.runtime.sendMessage({type:"open_options_page"}),this.sendAnalyticsEvent([["DCBrowserExt:SidePanel:SettingsMenu:PreferencesClicked"]])}}))}))}))}}