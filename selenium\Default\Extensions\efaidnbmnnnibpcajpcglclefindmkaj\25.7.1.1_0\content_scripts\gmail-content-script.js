/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
(()=>{const e="acrobatContentScriptDisconnectStart",t="acrobatContentScriptDisconnectEnd",o="acrobat-gmail-fte-config";let n,i,r,s,c,a,m,l,u,p,d,g,h;const w=()=>{d&&(r?.addAcrobatTouchPointInTheMessageView(),s?.processForListView(),i?.processForAlreadyShownNativeViewerPrompt(),i?.sendAnalyticsForImageAssetInteraction()),g&&l?.addExpressNativeViewerTouchpoint(),h&&u?.addExpressMessageViewTouchpoint()},b=new MutationObserver((function(){p?.disconnectBodyObserver?b.disconnect():(b.takeRecords(),chrome?.runtime?.id?w():(a?.removeAllAcrobatTouchPoints(),l?.removeExpressTouchpoints(),b?.disconnect()))})),f=()=>{if(document?.body)try{const e={childList:!0,subtree:!0};b.observe(document.body,e)}catch(e){}else setTimeout(f,500)},v=()=>{window.addEventListener(t,(e=>p.contentScriptDisconnectEnd(e)),{signal:p?.eventControllerSignal}),window.addEventListener(e,(()=>(e=>{if(!chrome.runtime?.id){const o=e.createStateToTransferToNewContentScript();e.contentScriptDisconnectStart(),b?.disconnect(),window.dispatchEvent(new CustomEvent(t,{detail:{state:o}}))}})(p)),{signal:p?.eventControllerSignal})},A=()=>{v(),window.dispatchEvent(new Event("orphanContentScript")),window.dispatchEvent(new CustomEvent(e)),(()=>{if(p?.adobeCleanFontAdded)return;const e=chrome.runtime.getURL("browser/css/fonts/AdobeClean-Regular.otf"),t=new FontFace("AdobeClean-Regular",`url(${e})`);t.load().then((()=>{document.fonts.add(t)})),p.adobeCleanFontAdded=!0})()},T=(e,t)=>{var i;p&&(p.gmailConfig=e,p.iconURL=chrome.runtime.getURL("browser/images/acrobat_dc_appicon_128.png"),p.iconURLListView=chrome.runtime.getURL("browser/images/acrobat_dc_trefoil_24_white.svg"),p.viewerURLPrefix=chrome.runtime.getURL("viewer.html"),p.expressConfig=t),d&&(A(),n&&n.init(),(async()=>{const e=(new Date).getTime();let t={count:0,nextDate:e};t=(await chrome.storage.local.get(o))?.[o]||t;const n=p?.gmailConfig?.fteConfig?.tooltip;((e,t)=>-1!==e?.resetDay&&t>e?.resetDay)(n,e)&&(t.count=0,t.nextDate=e),p.fteToolTip={eligibleFte:{type:""},...t},t={[o]:t},chrome.storage.local.set(t)})(),i=e,p.isAcrobatDefaultForGmailPDFs=i?.isAcrobatDefaultForSurface)};chrome.runtime.onMessage.addListener((function(e){"acrobatGmailFteStateUpdated"===e.content_op?p.fteToolTip={...p.fteToolTip,...e?.fteState}:"changeDefaultViewershipForSurface"===e.content_op?"gmail"===e?.surfaceId&&chrome.runtime.id&&(e?.isDefault?m?.takeDefaultViewerShip():m?.resetDefaultViewership(),v(),w()):"acrobatTouchPointsDisabled"===e.content_op&&(p.gmailConfig.touchPointSettingEnabled=!1,p?.isAcrobatDefaultForGmailPDFs||(d=!1,a?.removeAllAcrobatTouchPoints(),p?.disconnectAcrobatListeners()))})),(async()=>{const e=chrome.runtime.getURL("content_scripts/gmail/state.js"),t=chrome.runtime.getURL("content_scripts/gmail/gmail-response-service.js"),o=chrome.runtime.getURL("content_scripts/gmail/native-viewer-touch-point-service.js"),d=chrome.runtime.getURL("content_scripts/gmail/message-view-touch-point-service.js"),g=chrome.runtime.getURL("content_scripts/gmail/list-view-touch-point-service.js"),h=chrome.runtime.getURL("content_scripts/gsuite/util.js"),w=chrome.runtime.getURL("content_scripts/gmail/util.js"),b=chrome.runtime.getURL("content_scripts/gmail/default-viewership-service.js"),f=chrome.runtime.getURL("content_scripts/express/gmail/express-gmail-touchpoint-service.js"),v=chrome.runtime.getURL("content_scripts/express/gmail/express-gmail-message-view-touchpoint-service.js");return Promise.all([import(t),import(o),import(d),import(g),import(w),import(h),import(b),import(e),import(f),import(v)]).then((e=>{n=e[0],i=e[1],r=e[2],s=e[3],a=e[4],c=e[5],m=e[6],p=e[7]?.default,l=e[8],u=e[9]}))})().then((()=>{window.location?.search?.includes("acrobatPromotionSource")||Promise.all([chrome.runtime.sendMessage({main_op:"gmail-init"}),chrome.runtime.sendMessage({main_op:"gmail-express-init"})]).then((([e,t])=>{d=(e=>(e.touchPointSettingEnabled||e?.isAcrobatDefaultForSurface)&&(e.enableAttachmentCardPromptInGmail||window.location?.search?.includes("enableAcrobatPromptInGmail")))(e),g=function(e){return e.enableGmailNVExpressMenu}(t),h=function(e){return e.enableGmailMessageViewCTA}(t),(d||g||h)&&(T(e,t),a?.removeAllAcrobatTouchPoints(),l?.removeExpressTouchpoints(),w(),f(),d&&(m?.sendAnalyticsWithDefaultViewershipFeatureStatus(),c?.sendAnalyticsOncePerMonth("DCBrowserExt:Gmail:PromotionFeature:Enable"),p.gmailConfig?.enableGmailFteToolTip&&c?.sendAnalyticsOncePerMonth("DCBrowserExt:Gmail:FTE:Enable")))}))}))})();