/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{communicate as e}from"./communicate.js";import{privateApi as t}from"./private-api.js";import{dcLocalStorage as r}from"../common/local-storage.js";import{util as s}from"./util.js";import{getExpressAssetResponse as o}from"./express.js";import{EXPRESS as i}from"./constant.js";import{common as a}from"./common.js";import{analytics as c,events as n}from"../common/analytics.js";function u(){let e;return r.getItem("pdfViewer")?"true"===r.getItem("pdfViewer")?e="enabled":"false"===r.getItem("pdfViewer")&&(e="disabled"):e="neverEnabled",e}const d=["redirectJob","redirectAuth","user_tags","frictionless_return_user"];export function externalClients(i,l,b){if(/^https:\/\/([a-zA-Z\d-]+\.){0,}(adobe|acrobat)\.com(:[0-9]*)?$/.test(l.origin))if("WebRequest"===i.type)switch(i.task){case"detect_extension":b({status:"success",result:"true"});break;case"detect_desktop":try{if(0!=e.version&&1!=e.version){const t=e.version===SETTINGS.READER_VER||e.version===SETTINGS.ERP_READER_VER;b(t?{status:"success",result:"Reader"}:{status:"success",result:"Acrobat"})}else b({status:"success",result:"NoApp"})}catch(e){b({status:"error",code:"error"})}break;case"detect_viewer_enabled":try{b({status:"success",result:u()})}catch(e){b({status:"error",code:"error"})}break;case"enable_viewer":if(!s.isAcrobatOrigin(l.origin)&&!l.origin.includes("local-test.acrobat.com:11010"))return;try{r.setItem("pdfViewer","true"),t.setViewerState("enabled"),b({status:"success"})}catch(e){b({status:"error",code:"error"})}break;case"open_acrobat_clicked":if(!/^https:\/\/((dev|stage)+\.){0,}acrobat\.adobe\.com?$/.test(l.origin)&&!l.origin.includes("local-test.acrobat.com:11010"))return;try{const e=r.getItem("firstOpenedTabId");e?(chrome.tabs.update(e,{active:!0},(function(){i?.reload&&chrome.tabs.reload(e)})),b({status:"success"})):b({status:"error",code:"NoTab"})}catch(e){b({status:"error",code:"Error"})}break;case"upsell_redirect_to_pdf":if(!s.isAcrobatOrigin(l.origin)&&!l.origin.includes("local-test.acrobat.com:9019"))return;chrome.tabs.update(l.tab.id,{url:i.pdfUrl});break;case"storage_bulk_read":{if(!s.isAcrobatOrigin(l.origin)&&!s.isAdobeOrigin(l.origin))return;const{keys:e=[]}=i,t={};e.forEach((e=>d.includes(e)&&(t[e]=r.getItem(e)))),b({values:t});break}case"storage_bulk_write":{if(!s.isAcrobatOrigin(l.origin)&&!s.isAdobeOrigin(l.origin))return;const{data:e={}}=i;Object.entries(e).forEach((([e,t])=>d.includes(e)&&r.setItem(e,t))),b({status:200});break}case"storage_bulk_remove":{if(!s.isAcrobatOrigin(l.origin)&&!s.isAdobeOrigin(l.origin))return;const{keys:e=[]}=i;e.forEach((e=>d.includes(e)&&r.removeItem(e))),b({status:200});break}case"fetch_express_asset":{const e=new URL(a.getExpressURLs().webAppUrl).origin;if(l.origin!==e)return;const{options:t={}}=i,r=t.sessionId;o(r).then((e=>{const t=e.domain;delete e.domain;try{b(e)}catch(r){c.event(n.EXPRESS_EXECUTE_VERB_FAILED,{eventContext:r.toString(),dvDisableSessionCount:e.buffer?.length,domain:t})}}));break}default:b({status:"error",code:"invalid_task"})}else b({status:"error",code:"invalid_type"})}