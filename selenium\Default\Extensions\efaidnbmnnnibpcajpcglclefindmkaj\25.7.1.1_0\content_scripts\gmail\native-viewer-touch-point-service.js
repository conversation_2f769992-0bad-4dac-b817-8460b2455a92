/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{getElementBasedOnSelector,getFileDetailsElementInNativeViewer,createAcrobatIconElement,createURLForAttachment,isOrphanContentScript,isDriveFileDirectDownloadLink,getParentElementForNativeViewerPrompt}from"./util.js";import{sendAnalyticsWithGMailDVFeatureStatus}from"./default-viewership-service.js";import{sendAnalytics,sendAnalyticsOnce,sendAnalyticsOncePerMonth}from"../gsuite/util.js";import{createFteTooltip,shouldShowFteTooltip,updateFteToolTipCoolDown,acrobatTouchPointClicked,getAcrobatTouchPointFteEligibility,removeGsuiteFteTooltip}from"../gsuite/fte-utils.js";import state from"./state.js";const NATIVE_VIEWER_PROMPT_CLASS="acrobat-native-viewer-prompt",GMAIL_FTE_TOOLTIP_STORAGE_KEY="acrobat-gmail-fte-config",GMAIL_FTE_TOOLTIP_CONTAINER_CLASS="acrobat-fte-tooltip-container",ADD_TO_DRIVE_BUTTON_KEY="addToDriveButton",ORGANISE_IN_DRIVE_BUTTON_KEY="organiseInDriveButton",hideNativeViewerPrompt=e=>{if(!state.nativeViewerPromptState?.nativeViewerPromptVisible&&!e)return;const t=document.getElementsByClassName(NATIVE_VIEWER_PROMPT_CLASS);if(t?.length>0){const e=t[0].getElementsByClassName("acrobat-fte-tooltip-container");e?.length>0&&sendAnalytics(["DCBrowserExt:GmailFTE:NativeView:Dismissed"]),t[0].parentElement.removeChild(t[0])}state.nativeViewerPromptState.nativeViewerPromptVisible=!1},createNativeViewPromptTextElement=()=>{const e=document.createElement("span");return e.setAttribute("class","acrobat-native-viewer-prompt-text"),e.textContent=state?.gmailConfig?.acrobatPromptText||"Open with Acrobat",e},getAnalyticsType=(e,t)=>{let i="LIST_VIEW"===t?"NativeViewerPromptLV":"NativeViewerPrompt";return isDriveFileDirectDownloadLink(e)&&(i+="_DriveLink"),i},createNativeViewerPrompt=(e,t,i)=>{const n=document.createElement("a"),o=createAcrobatIconElement(),r=createNativeViewPromptTextElement(),a=createURLForAttachment(e,"GmailNativeViewer",t);return n.setAttribute("class",NATIVE_VIEWER_PROMPT_CLASS),n.setAttribute("href",a),n.setAttribute("target","_blank"),n.appendChild(o),n.appendChild(r),n.addEventListener("click",(()=>{const t=getAnalyticsType(e,i);sendAnalytics([[`DCBrowserExt:Gmail:${t}:Clicked`,{gsuiteFte:getAcrobatTouchPointFteEligibility(state?.gmailConfig?.enableGmailFteToolTip)}]]),acrobatTouchPointClicked("acrobat-gmail-fte-config"),state.fteToolTip.eligibleFte.type="",state.fteToolTip.touchPointClicked=!0})),n},processForAlreadyShownNativeViewerPrompt=()=>{const{nativeViewerPromptVisible:e,previousFileDetailsElement:t,nativeViewerAttachmentURL:i}=state?.nativeViewerPromptState;if(e){const e=getFileDetailsElementInNativeViewer();e?e!==t&&hideNativeViewerPrompt():(state.nativeViewerPromptState.previousFileDetailsElement=null,state.nativeViewerPromptState.nativeViewerAttachmentURL=null,hideNativeViewerPrompt(),removeEventListenersForDriveButton("addToDriveButton"),removeEventListenersForDriveButton("organiseInDriveButton"))}else if(t&&!isOrphanContentScript()){const e=getFileDetailsElementInNativeViewer();e||(removeEventListenersForDriveButton("addToDriveButton"),removeEventListenersForDriveButton("organiseInDriveButton")),e&&t===e&&addAcrobatPromptInNativeViewer(i)}},sendAnalyticsForImageAssetInteraction=()=>{if(!1===state?.analyticsForImageClicked){const e=getFileDetailsElementInNativeViewer();if(e){const t=getFileDetails(e);t?.mimeType?.startsWith("image/")&&(sendAnalyticsOncePerMonth("DCBrowserExt:Gmail:ImageAttachment:Opened",{eventContext:t.mimeType}),state.analyticsForImageClicked=!0)}}},removeFteTooltipFromAttachmentDiv=()=>{removeGsuiteFteTooltip(),state.fteToolTip.eligibleFte.type=""},handleFteClickOutside=(e,t)=>{const i=document.getElementsByClassName("acrobat-fte-tooltip-container");i?.length>0&&(t.contains(e.target)||(sendAnalytics(["DCBrowserExt:GmailFTE:NativeView:Dismissed"]),removeFteTooltipFromAttachmentDiv(),document.removeEventListener("click",handleFteClickOutside)))},addFteTooltipButtonEventListener=e=>{e.querySelector(".acrobat-fte-tooltip-button").addEventListener("click",(()=>{removeFteTooltipFromAttachmentDiv(),sendAnalytics(["DCBrowserExt:GmailFTE:NativeView:Clicked"]),document.removeEventListener("click",handleFteClickOutside)}))},addFteTooltipToAttachmentDiv=e=>{const t=createFteTooltip(state?.gmailConfig?.gmailFteToolTipStrings,"nativeView");addFteTooltipButtonEventListener(t),document.addEventListener("click",(e=>handleFteClickOutside(e,t)),{once:!0,signal:state?.eventControllerSignal}),state.fteToolTip.eligibleFte.type="nativeView",e.appendChild(t),sendAnalyticsWithGMailDVFeatureStatus(["DCBrowserExt:GmailFTE:NativeView:Shown"]),updateFteToolTipCoolDown(state?.gmailConfig?.fteConfig?.tooltip,"acrobat-gmail-fte-config").then((e=>{state.fteToolTip={...state?.fteToolTip,...e}}))},addFTETooltipIfEligible=e=>{const t=state?.gmailConfig?.fteConfig?.tooltip;shouldShowFteTooltip(t,state?.fteToolTip,state?.gmailConfig?.enableGmailFteToolTip).then((t=>{t&&addFteTooltipToAttachmentDiv(e)}))},addAcrobatPromptInNativeViewer=(e,t,i)=>{try{const{nativeViewerPromptVisible:n}=state?.nativeViewerPromptState;if(n)return;const o=createNativeViewerPrompt(e,t,i),r=getParentElementForNativeViewerPrompt();if(r&&r?.childNodes?.length>0){if(r.insertBefore(o,r.childNodes[0]),state.nativeViewerPromptState.nativeViewerPromptVisible=!0,state.nativeViewerPromptState.nativeViewerAttachmentURL=e,state.nativeViewerPromptState.previousFileDetailsElement=getFileDetailsElementInNativeViewer(),window.innerWidth>1200){addFTETooltipIfEligible(o);const t=getAnalyticsType(e,i);sendAnalytics([[`DCBrowserExt:Gmail:${t}:Shown`]])}}else hideNativeViewerPrompt()}catch(e){hideNativeViewerPrompt()}},getFileDetails=e=>{try{return JSON.parse(e?.textContent?.replace(/\\/g,""))}catch(e){return null}},removeEventListenersForDriveButton=e=>{if(state?.nativeViewerPromptState?.driveButtonAddedListeners[e]){const t=getElementBasedOnSelector(document,e,"nativeViewer");t&&t.removeEventListener("click",state?.nativeViewerPromptState?.driveButtonAddedListeners[e]),delete state?.nativeViewerPromptState?.driveButtonAddedListeners[e]}},driveButtonClickListener=(e,t)=>()=>{sendAnalyticsOnce(e),"addToDriveButton"===t&&setTimeout((()=>addEventForDriveButtonClick("organiseInDriveButton")),1e3)},addEventForDriveButtonClick=e=>{if(!state?.nativeViewerPromptState?.driveButtonAddedListeners[e]){const t=getElementBasedOnSelector(document,e,"nativeViewer");if(t){const i=driveButtonClickListener(`DCBrowserExt:GmailNV:${e}:Clicked`,e);t.addEventListener("click",i,{signal:state?.eventControllerSignal,once:!0}),state.nativeViewerPromptState.driveButtonAddedListeners[e]=i}}},processForNativeViewer=(e,t)=>{const i=getFileDetailsElementInNativeViewer();if(i&&!isOrphanContentScript()){const n=getFileDetails(i);if(e?.name&&n&&e?.name!==n.title)return;addAcrobatPromptInNativeViewer(e?.url,n.title,t),addEventForDriveButtonClick("addToDriveButton"),addEventForDriveButtonClick("organiseInDriveButton")}else hideNativeViewerPrompt()},sendAnalyticsIfNativeViewerLaunched=e=>{getFileDetailsElementInNativeViewer()&&sendAnalytics([[`DCBrowserExt:Gmail:NativeViewerLV:${e}`]])},removeAllTouchPoints=()=>{hideNativeViewerPrompt(!0)};export{processForAlreadyShownNativeViewerPrompt,sendAnalyticsIfNativeViewerLaunched,processForNativeViewer,removeAllTouchPoints,sendAnalyticsForImageAssetInteraction};