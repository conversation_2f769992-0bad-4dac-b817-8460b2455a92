{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "cmn-Hans-CN"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "announcement_notification_service_first_run_time": "*****************", "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1039, "left": 734, "maximized": false, "right": 1679, "top": 27, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_apps_install_state": 2, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "de039a50-6813-4aec-b0d4-ae2c448d7f49", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "install_signature": {"expire_date": "2025-10-08", "ids": ["efaidnbmnnnibpcajpcglclefindmkaj", "ngpampappnmepgilojfohadhhmbhlaek"], "invalid_ids": [], "salt": "jaQfVicTWe7whwsAGUtDAjJ3YG5vnLw34Xup1C2DnoU=", "signature": "dHuIjCGOOZh5fLpkuIIpyLUNMpYguwRttwalreFNwKGTg5WPOixphT+NjCHLPsXnFcC9at0g8FMqMCtcsEvPWKXzcQ0Z+r3gkDiH4qJY62f3m2ofo4n+oLS7cBaJFzeBpT3E3aPN4k6YS0JhX3zwAUauv3iEcjUjGJEvtObPzC2fMkMtfZd5IcIPnxgJ9+4/eLnqr0X3QxnKjzSnwGo/XBzt7b+WDVpSwoECZbIVzi8/F247W9bcOUOyLHGZ9k3LZjbcWBuwq+RHAJb5B/Wr+nR86c3Puh/byck7BgFMsWzaw5zbQR6/RyniPR7Y97qTUNR58tabF6R/ldBH5u8/0Q==", "signature_format_version": 2, "timestamp": "*****************"}, "last_chrome_version": "138.0.7204.158"}, "gaia_cookie": {"changed_time": **********.741727, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": ""}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "44b7a128-b47f-4425-9723-60ef31588688"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "zh-CN,zh"}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "GLyeBYs/KfTFU0+/4TRf2yZiBMaf33pzNHDQF5EhNkxX1rfbXyqPFnY8pGR7uObtsgehbbKYQjaGYlKkCCVeag=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_TRACKING": true, "SAVED_TAB_GROUP": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "13397123519344004", "setting": {"lastEngagementTime": 1.3397123519343998e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.158", "creation_time": "*****************", "exit_type": "Crashed", "last_engagement_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.227913, "last_time_password_store_metrics_reported": **********.229861, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "您的 Chrome", "password_hash_data_list": [], "were_old_google_logins_removed": true}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "AE186411699E1AE9160898794A3D9CF4CE2C86EE2C1B3EA59F59029C6C9859EF"}, "extensions": {"ui": {"developer_mode": "C211D1971F4029044C1EFA3F142FE896787FB3CC9CEC6BA98257B2559DE81F5A"}}, "homepage": "E0CE003D90D3D3CB2552C1B5089CC298E48ADE0323E16A203CE439CCB6A5087B", "homepage_is_newtabpage": "A0F3AA09B63298E9DACDF6E9D3EC19186E98819C94B1FB665847318F3F7B27A8", "session": {"restore_on_startup": "9B8B1A7349680ED7048E8126586FD5B85E3DB6604AEFD65E08E56012D4547C23", "startup_urls": "AEDF4BDAFF40EF94AA0377F00C3A2C2DE8D712009B75DE61FB087A7393FDD95A"}}, "browser": {"show_home_button": "3D2E1E708D0E05DD63B5E4A33E6584BED85825F0C37E9560D3EA4A974069A5DA"}, "default_search_provider_data": {"template_url_data": "35B8AB17F6981E0F81DA5911E997D3D278DFFB7C89176A54FA47053013093B1A"}, "enterprise_signin": {"policy_recovery_token": "18114D04A3EE98A4081F7C834BDF75AF87DA28AE185A2511EFE4B41687D07CC2"}, "extensions": {"ui": {"developer_mode": "EE27525AE2AB83DF5197BE85C9C176BC22686A632362870B63180D72A3E92964"}}, "google": {"services": {"account_id": "774CD9F5AC531687C430F8CF65D7D665C249995E7BDCE0FC85BBE8E7C68E5020", "last_signed_in_username": "0AA042D27E524887ABBC6670BBAC6C09C2DB413115324F2CBDB867F3678D6620", "last_username": "757DD82516A2DA46D275563273600C8DE9678DD8DF5FC1265F5FFB744AC34350"}}, "homepage": "A3E98857F9103CF99C7D821479D88BEA18169D331AE3ED667AB2FE6D5C865125", "homepage_is_newtabpage": "2E92577C67C3EDDA5CB9FB4FB1A7867E0C703E745C425E3DA49BE93EEF5AFC29", "media": {"cdm": {"origin_data": "99E33D2C392933689180DCD7B2ED803B69921AB9C26729317EF7907AA2705BAD"}, "storage_id_salt": "E73A2C36416F8056DD5F557CBC0FF82945E680520A3E4673EFA95C7FD46DFEBA"}, "module_blocklist_cache_md5_digest": "4B66E8291A355D269895193026019471893D2CC9650C1E36CE0535CB5C800792", "pinned_tabs": "B8EF4B54A4DB8CF582B65194DCF1F1393874C1B18CEB429DBFCAE30B118488F8", "prefs": {"preference_reset_time": "F65634CFC3AAAF9BF53659857E1D285949AAB0D13680AA0D8D4CA23D95A4248C"}, "safebrowsing": {"incidents_sent": "1623332B952AECB40F1CB30D8E62F607C4F105FD234DCE05F502D123F58EBDE7"}, "search_provider_overrides": "2C09A12C261693ED22B576C548D8538CC5A4774230C629CEB282C7E976002227", "session": {"restore_on_startup": "4F5368C97A1E51500C122864A909D61FD4F379049913F24BFAC688B0656599F1", "startup_urls": "F3F36E97B7503D8987A0FE3678021B8E5FD52CD2D896E97BEBE92CAC93922881"}}}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13397382669828564", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13397123469", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ/aLMjc6T5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADELGjzI3Ok+YX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13397011199000000", "uma_in_sql_start_time": "13397123469228967"}, "sessions": {"event_log": [{"crashed": false, "time": "13397123469228047", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["zh-CN", "en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[],[],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:suggesteventid\":\"2755156156905777969\",\"google:suggesttype\":[],\"google:verbatimrelevance\":851}]"}}