/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{dcTabStorage as r}from"../browser/js/tab-storage.js";import{loggingApi as e}from"./loggingApi.js";const t=new class{constructor(){this.db;this.objectStoreName="pdfFile";const r=indexedDB.open("PdfFilesDataBase",1);r.onupgradeneeded=r=>{this.db=r.target.result,this.db.objectStoreNames.contains(this.objectStoreName)||this.db.createObjectStore(this.objectStoreName)},r.onsuccess=r=>{this.db=r.target.result},r.onerror=()=>{e.error({message:"indexeddb could not be opened",error:r.error})}}extractParam(r,e){return new URLSearchParams(r).get(e)||""}storeInIndexedDB(t,o){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const s=this.db.transaction(this.objectStoreName,"readwrite");s.onerror=function(r){return e.error({message:"Error in transaction",error:r.target.error}),Promise.reject(r.target.error)};const n=s.objectStore(this.objectStoreName),i={fileBuffer:t,tabId:o,pdfUrl:this.extractParam(r.getItem("search"),"pdfurl")},a=n.put(i,o.toString());return new Promise(((r,t)=>{a.onsuccess=e=>{r(e.target.result)},a.onerror=r=>{e.error({message:"Error in updating buffer",error:r.target.error}),t(r.target.error)}}))}}catch(r){return e.error({message:"Error in updating buffer",error:r}),Promise.reject(r)}}getDataFromIndexedDB(t){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const o=this.db.transaction(this.objectStoreName,"readonly");o.onerror=function(r){return e.error({message:"Error in transaction",error:r.target.error}),Promise.reject(r.target.error)};const s=o.objectStore(this.objectStoreName).get(t.toString());return new Promise(((t,o)=>{s.onsuccess=e=>{const o=e.target.result,s=this.extractParam(r.getItem("search"),"pdfurl");o&&o.pdfUrl===s?t(o):t({})},s.onerror=r=>{e.error({message:"Error in getting buffer",error:r.target.error}),o(r.target.error)}}))}}catch(r){return e.error({message:"Error in getting buffer",error:r}),Promise.reject(r)}}deleteDataFromIndexedDB(r){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const t=this.db.transaction(this.objectStoreName,"readwrite");t.onerror=function(r){return e.error({message:"Error in transaction",error:r.target.error}),Promise.reject(r.target.error)};const o=t.objectStore(this.objectStoreName).delete(r.toString());return new Promise(((r,t)=>{o.onsuccess=e=>{r(e.target.result)},o.onerror=r=>{e.error({message:"Error in deleting buffer",error:r.target.error}),t(r.target.error)}}))}}catch(r){return e.error({message:"Error in deleting buffer",error:r}),Promise.reject(r)}}storeBufferAndCall(e,o,s,...n){e?(r.setItem("bufferTabId",s),t.storeInIndexedDB(e,s).then((()=>{o&&o(...n)}))):o&&o(...n)}storeFileByHash(r,t){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const o=this.db.transaction(this.objectStoreName,"readwrite").objectStore(this.objectStoreName),s={fileBuffer:r,blobTimeStamp:Date.now()},n=o.put(s,t);return new Promise(((r,o)=>{n.onsuccess=()=>r(t),n.onerror=r=>{e.error({message:"Error in storing file by hash",error:r.target.error}),o(r.target.error)}}))}}catch(r){return e.error({message:"Error in storing file by hash",error:r}),Promise.reject(r)}}getFileByHash(r){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const t=this.db.transaction(this.objectStoreName,"readonly"),o=t.objectStore(this.objectStoreName).get(r);return new Promise(((r,t)=>{o.onsuccess=e=>{const t=e.target.result;r(t?t.fileBuffer:null)},o.onerror=r=>{e.error({message:"Error in retrieving file by hash",error:r.target.error}),t(r.target.error)}}))}}catch(r){return e.error({message:"Error in retrieving file by hash",error:r}),Promise.reject(r)}}hasKey(r){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const t=this.db.transaction(this.objectStoreName,"readonly"),o=t.objectStore(this.objectStoreName).count(r);return new Promise(((r,t)=>{o.onsuccess=()=>{r(o.result>0)},o.onerror=r=>{e.error({message:"Error checking file hash key existence",error:r.target.error}),t(r.target.error)}}))}}catch(r){return e.error({message:"Error checking key existence",error:r}),Promise.reject(r)}}clearExpiredBlobBuffers(){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const r=this.db.transaction(this.objectStoreName,"readwrite").objectStore(this.objectStoreName),t=r.openCursor(),o=Date.now()-2592e5;return new Promise(((s,n)=>{t.onsuccess=e=>{const t=e.target.result;if(t){const{blobTimeStamp:e}=t.value;if(e&&e<o){const e={...t.value,fileBuffer:null,fileBufferDeleteTimeStamp:Date.now()};r.put(e,t.primaryKey)}t.continue()}else s(!0)},t.onerror=r=>{e.error({message:"Error in clearing old file buffer entries",error:r.target.error}),n(r.target.error)}}))}}catch(r){return e.error({message:"Error in clearing old file buffer entries",error:r}),Promise.reject(r)}}clearFileHashOldEntries(){try{if(this.db&&this.db.transaction&&"function"==typeof this.db.transaction){const r=this.db.transaction(this.objectStoreName,"readwrite").objectStore(this.objectStoreName),t=r.openCursor(),o=Date.now()-2592e6;return new Promise(((s,n)=>{t.onsuccess=e=>{const t=e.target.result;if(t){const{blobTimeStamp:e}=t.value;e&&e<o&&r.delete(t.primaryKey),t.continue()}else s(!0)},t.onerror=r=>{e.error({message:"Error in clearing old entries",error:r.target.error}),n(r.target.error)}}))}}catch(r){return e.error({message:"Error in clearing old entries",error:r}),Promise.reject(r)}}};export{t as indexedDBScript};