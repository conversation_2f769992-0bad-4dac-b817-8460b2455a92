/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{communicate as e}from"./communicate.js";import{common as n}from"./common.js";import{util as t}from"./util.js";import{Proxy as o}from"./proxy.js";import{analytics as r}from"../common/analytics.js";import{acroweb2pdf as i}from"./acro-web2pdf.js";import{sharepointModule as a}from"./sharepoint-module.js";import{SETTINGS as s}from"./settings.js";import{dcLocalStorage as l,dcSessionStorage as E}from"../common/local-storage.js";import{versionChecks as c}from"./handleVersionChecks.js";var _;_||(_=new function(){this.proxy=o.proxy.bind(this),this.LOG=(...e)=>n.LOG(...e);const _={BUTTON_CLICKED:0,PDF_DOWNLOADED:1,PDF_DOWNLOAD_FAILED:2},d="pdf_downloaded",I="pdf_downloading",m="pdf_failure";function u(e,n){if("pdfviewer"!==e.click_context)return;const t="SharePoint"===e.workflow_name||e.isSharePointURL,o="FillnSign"===e.paramName;switch(n){case _.BUTTON_CLICKED:o?t?r.event(r.e.VIEWER_SHAREPOINT_FILLSIGN_CLICKED):r.event(r.e.VIEWER_FILLSIGN_CLICKED):t?r.event(r.e.VIEWER_SHAREPOINT_CLICKED):r.event(r.e.VIEWER_OPEN_IN_ACRO_CLICKED);break;case _.PDF_DOWNLOADED:t?o?r.event(r.e.VIEWER_SHAREPOINT_FILLSIGN_CLICKED_COMPLETE):r.event(r.e.VIEWER_SHAREPOINT_CLICKED_COMPLETE):o?r.event(r.e.VIEWER_FILLSIGN_CLICKED_COMPLETE):r.event(r.e.VIEWER_OPEN_IN_ACRO_CLICKED_COMPLETE);break;case _.PDF_DOWNLOAD_FAILED:o?r.event(r.e.VIEWER_FILLSIGN_CLICKED_FAILED):r.event(r.e.VIEWER_OPEN_IN_ACRO_CLICKED_FAILED)}}function f(n){n.mimeHandled||e.sendMessage(n)}function L(e,n){e.panel_op="status",e.current_status=n,f({tabId:e.tabId,loaded:e.loaded,filename:e.filename,url:e.url,persist:e.persist,version:e.version,start:e.start,is_pdf:e.is_pdf,newUI:e.newUI,panel_op:e.panel_op,content_op:e.content_op,click_context:e.click_context,current_status:e.current_status,mimeHandled:e.mimeHandled})}this.getVersion=function(n){var t,o=function(o){if(o){clearTimeout(t),function(e){e.fillnSignEnabled&&"true"===e.fillnSignEnabled?s.FILL_N_SIGN_ENABLED=!0:s.FILL_N_SIGN_ENABLED=!1}(o),function(e){e.yoloEnabled&&"true"===e.yoloEnabled?s.ADOBE_YOLO_ENABLED=!0:s.ADOBE_YOLO_ENABLED=!1}(o),function(e){e.frictionlessEnabled&&"false"===e.frictionlessEnabled?s.FRICTIONLESS_ENABLED=!1:s.FRICTIONLESS_ENABLED=!0}(o),function(e){const n="true"===e.sharePointEnabled;a.setFeatureEnabled(n),n&&a.handleAllowedListFromNativeHost(e.sharePointUrlString),l.setItem("isSharepointFeatureEnabled",a.isFeatureEnabled())}(o),function(n){const t=+n.majorVersion;e.setVersion(t),1==n.isdummy&&e.setNMHConnectionStatus(!1)}(o),function(e){let n;E.setItem("shimFullVersion",e?.fullVersion);const t=+e.majorVersion;1===t?(n={VERSION:"Unknown"},r.shim="unknown"):0===t?(n={VERSION:"None"},r.shim="none"):(n={VERSION:e.majorVersion+"."+e.minorVersion},r.shim=e.majorVersion+"."+e.minorVersion)}(o),function(e){e.enhancedWebCaptureEnabled&&"true"===e.enhancedWebCaptureEnabled?s.ENHANCED_WEBCAPTURE_ENABLED=!0:s.ENHANCED_WEBCAPTURE_ENABLED=!1}(o);var i={0:"Admin",1:"RDC",2:"FTE",3:"Other",4:"None"}[o.installSource]||"Unknown";n({ver:+o.majorVersion,source:i,installMonth:o.installMonth,installYear:o.installYear,repromptDone:o.repromptDone,pdfOwner:o.pdfOwner,isAcrobatInstalled:o.isAcrobatInstalled,isReaderInstalled:o.isReaderInstalled})}};t=setTimeout((function(){o({messageType:"shimVersionInfo",majorVersion:"1",minorVersion:"0",repromptDone:"false",isdummy:!0})}),2e3),i.getVersion(o)},this._openInAcrobat=function(n,o,r){var a=this;return chrome.tabs.get(n.tabId,(function(o){if(o.url&&!/chrome\-extension/.test(o.url)&&(n.url=o.url),u(n,_.BUTTON_CLICKED),function(n){void 0===n.filename&&(n.filename=e.filenameFromUrl(n.url))}(n),e.version>1){if(!0===n.isSharePointURL)return i.openInAcrobat(n),L(n,d),r&&r("OP_SUCCESS"),void u(n,_.PDF_DOWNLOADED);if(n.authenticatedPDF&&1==n.authenticatedPDF)return delete n.authenticatedPDF,n.current_status===d&&n.base64PDF&&i.openInAcrobat(n),void f(n);L(n,I),function(e,n,o){L(n,I);const r=n.dataURL?n.dataURL:n.url;fetch(r).then(e.proxy((function(e){return e.status>=400&&(L(n,m),u(n,_.PDF_DOWNLOAD_FAILED),o&&o("OP_FAILURE")),e.blob()}))).then(e.proxy((function(e){if("application/pdf"===e.type){const t=new FileReader;t.onloadend=function(e){n.base64PDF=e.target.result,i.openInAcrobat(n),L(n,I)},t.readAsDataURL(e),o&&o("OP_SUCCESS"),u(n,_.PDF_DOWNLOADED)}else n.authenticatedPDF=!0,f(n)}))).catch((e=>{o&&o("OP_FAILURE"),t.consoleError(e)}))}(a,n,r)}else chrome.downloads.download({url:n.dataURL,conflictAction:"uniquify",saveAs:!0,filename:n.filename}),r&&r("APP_NOT_INSTALLED")})),!0},this.openInAcrobat=async function(n,t,o){e.legacyShim()?(await c(),this._openInAcrobat(n,t,o)):this._openInAcrobat(n,t,o)},this.openConvertedFile=function(e){i.openFile(e)}}),e.registerHandlers({open_in_acrobat:_.proxy(_.openInAcrobat),open_converted_file:_.proxy(_.openConvertedFile)});export const acroActions=_;