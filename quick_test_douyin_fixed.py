#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试抖音发布功能 - XPath修复版
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.platform_publishers import DouyinPublisher
from src.services.new_one_click_publisher import VideoInfo
from src.utils.logger import logger

def main():
    """主函数"""
    print("🚀 抖音发布功能测试 - XPath修复版")
    print("=" * 50)
    
    # 查找可用的测试视频文件
    test_videos = [
        "output/励志故事/final_video.mp4",
        "output/励志故事/videos/cogvideox/cogvideox_1752205208752.mp4",
        "test_video.mp4"
    ]

    test_video = None
    for video in test_videos:
        if os.path.exists(video):
            test_video = video
            break

    if not test_video:
        print("❌ 未找到可用的测试视频文件")
        print("💡 请确保以下任一文件存在:")
        for video in test_videos:
            print(f"   - {video}")
        return 1

    print(f"✅ 使用测试视频: {test_video}")
    
    try:
        # 创建抖音发布器
        print("🔧 创建抖音发布器...")
        publisher = DouyinPublisher()
        
        # 创建视频信息
        video_info = VideoInfo(
            file_path=test_video,
            title="AI视频生成测试 - XPath修复版",
            description="这是一个测试XPath修复后的视频上传功能。\n\n通过修复XPath语法错误，现在可以正常检测和使用上传元素了。\n\n#AI #测试 #修复 #抖音"
        )
        
        print("📋 视频信息:")
        print(f"   文件: {video_info.file_path}")
        print(f"   标题: {video_info.title}")
        print(f"   描述: {video_info.description[:50]}...")
        
        # 开始发布
        print("\n🚀 开始发布...")
        result = publisher.publish(video_info)
        
        # 输出结果
        if result['success']:
            print("🎉 发布成功！")
            print(f"   任务ID: {result.get('task_id', 'N/A')}")
            print(f"   平台: {result.get('platform', 'douyin')}")
            if 'video_url' in result:
                print(f"   视频链接: {result['video_url']}")
        else:
            print("❌ 发布失败")
            print(f"   错误信息: {result.get('error', '未知错误')}")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        logger.error(f"抖音发布测试异常: {e}", exc_info=True)
        return 1
    
    finally:
        # 清理资源
        try:
            if 'publisher' in locals():
                publisher.cleanup()
                print("🧹 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理异常: {e}")

if __name__ == "__main__":
    exit(main())
