/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import expressFteUtils from"./express-fte-utils.js";import{dcLocalStorage}from"../../common/local-storage.js";class ExpressFte{EXPRESS_FTE_CONSTANTS={POPOVER_FTE_CLASSNAME_PREFIX:"a129d813",GOOGLE_DOMAINS:["google.*","www.google.*"],ALLOWLISTED_DOMAINS:["imgur.*","*.imgur.*","unsplash.*","*.unsplash.*","pixabay.*","*.pixabay.*","pexels.*","*.pexels.*","istockphoto.*","*.istockphoto.*","stockvault.*","*.stockvault.*","picjumbo.*","*.picjumbo.*"],BLOCKED_GOOLGE_PATHNAME_PREFIXES:["/maps"],COACHMARK_IMAGES:["browser/images/ExpressRemoveBackground.png","browser/images/ExpressCropImage.png","browser/images/ExpressApplyEffects.png"]};constructor(){this.expressInitPromise=chrome.runtime.sendMessage({main_op:"express-init"}),this.expressWhatsappInitPromise=chrome.runtime.sendMessage({main_op:"whatsapp-express-init"}),this.expressGmailInitPromise=chrome.runtime.sendMessage({main_op:"gmail-express-init"}),this.expressGoogleImagePreviewInitPromise=chrome.runtime.sendMessage({main_op:"google-image-preview-express-init"}),this.addFontPromise=expressFteUtils.addFontToDocument(),this.expressInitPromise.then((e=>{this.mergedAllowList=this.EXPRESS_FTE_CONSTANTS.ALLOWLISTED_DOMAINS,e?.allowInfo?.allowListArray&&(this.mergedAllowList=this.mergedAllowList.concat(e.allowInfo.allowListArray))}))}isExpressFteEligible(){return new Promise((e=>{let t=0,s=setInterval((async()=>{try{const o=await this.expressInitPromise;o.showExpressTooltip||(clearInterval(s),e(!1)),this.inURLList(this.mergedAllowList,window.location)||(clearInterval(s),e(!1));const n=dcLocalStorage.getItem("expressFTEShown");if(3===n?.shownCount&&(clearInterval(s),e(!1)),n?.lastShownAt){let t=new Date(n.lastShownAt);await expressFteUtils.isExpressFteTooltipSecond()?t.setSeconds(t.getSeconds()+7):t.setDate(t.getDate()+7);new Date<t&&(clearInterval(s),e(!1))}const i=document.getElementsByTagName("img");let r=0;for(let e of i){if(e.offsetHeight<o.imageDimension.height||e.offsetWidth<o.imageDimension.width)continue;const t=e.getBoundingClientRect();if(!(t.top<0||t.left<0||t.right>window.innerWidth||t.bottom>window.innerHeight)&&(this.isInteractiveImage(e)&&(r+=1,r>=o.imageCount)))break}r>=o.imageCount?(clearInterval(s),e(!0)):(t+=1,t>=4&&(clearInterval(s),e(r>=o.imageCount)))}catch(t){clearInterval(s),e(!1)}}),500)}))}async renderExpressFte(){const e=await this.expressInitPromise,t=this.createPopoverFTE({title:e.expressTooltipStrings.popoverTitle,description:e.expressTooltipStrings.popoverDescription});document.body.appendChild(t)}isGoogleBlockedPathname(e){return-1!==this.EXPRESS_FTE_CONSTANTS.BLOCKED_GOOLGE_PATHNAME_PREFIXES.findIndex((t=>e.pathname.startsWith(t)))}inURLList(e,t){for(let s of e){const e=new RegExp("^"+s.replaceAll(".","\\.").replaceAll("*",".*")+"$");if(t.hostname.match(e))return!this.EXPRESS_FTE_CONSTANTS.GOOGLE_DOMAINS.includes(s)||!this.isGoogleBlockedPathname(t)}return!1}postShowCoachmarkUpdate(){const e=new URL(window.location.href).hostname;expressFteUtils.sendAnalyticsEvent([["DCBrowserExt:Express:PopoverCoachmark:Shown",{domain:e}]]),this.updateChromeStoreInfo()}hideShowCoachmarkUpdate(){expressFteUtils.sendAnalyticsEvent([["DCBrowserExt:Express:PopoverCoachmark:Dismissed"]])}async updateChromeStoreInfo(){const e=dcLocalStorage.getItem("expressFTEShown");let t=0;t=""===e?1:(e?.shownCount||0)+1;const s={lastShownAt:(new Date).toISOString(),shownCount:t};dcLocalStorage.setItem("expressFTEShown",s)}isInteractiveImage(e){const t=e.getBoundingClientRect(),s=document.elementsFromPoint(t.x+t.width/2,t.y+t.height/2);for(let o of s){if("A"===o.tagName||"INPUT"===o.tagName){if(o.contains(e)){if(o.clientHeight>=t.height&&o.clientWidth>=t.width)return!1;continue}return!1}return!!e.isSameNode(o)}return!1}createAcrobatIconElement=e=>{const t=document.createElement("img");return t.setAttribute("src",chrome.runtime.getURL("browser/images/acrobat_dc_appicon_128.png")),t.setAttribute("class",`${e}-tooltip-icon`),t};createImageContainer(){const e=document.createElement("div");e.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-image-container`);const t=document.createElement("img"),s=dcLocalStorage.getItem("expressFTEShown")?.shownCount||0;return t.setAttribute("src",chrome.runtime.getURL(this.EXPRESS_FTE_CONSTANTS.COACHMARK_IMAGES[s%this.EXPRESS_FTE_CONSTANTS.COACHMARK_IMAGES.length])),t.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-coachmark-image`),e.appendChild(t),e}createCloseButton(e){const t=document.createElement("div"),s=document.createElement("img");return s.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-close-button-icon`),s.src=chrome.runtime.getURL("browser/images/cross_10_n.svg"),t.appendChild(s),t.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-close-button`),t.addEventListener("click",(()=>{document.body.removeChild(e),this.hideShowCoachmarkUpdate()})),t}createContentContainer(e){const t=document.createElement("div");t.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-content-container`);const s=document.createElement("div");s.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-express-header`);const o=this.createAcrobatIconElement(`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-express`),n=document.createElement("div");n.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-header-content`),n.textContent=e.title,s.appendChild(o),s.appendChild(n);const i=document.createElement("div");return i.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-description-content`),i.textContent=e.description,t.appendChild(s),t.appendChild(i),t}createPopoverFTE(e){const t=document.createElement("div");t.setAttribute("class",`${this.EXPRESS_FTE_CONSTANTS.POPOVER_FTE_CLASSNAME_PREFIX}-express-tooltip`);const s=this.createImageContainer();t.appendChild(s);const o=this.createCloseButton(t);t.appendChild(o);const n=this.createContentContainer(e);return t.appendChild(n),this.postShowCoachmarkUpdate(),t}}const expressFte=new ExpressFte;export default expressFte;