/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{events as e}from"../../common/analytics.js";import{util as t}from"./content-util.js";import{privateApi as a}from"./content-privateApi.js";import{dcLocalStorage as n}from"../../common/local-storage.js";import{isEdgeBrowser as o,updateExtUserState as i,isChromeViewerOpened as s}from"../../common/util.js";import{OptionPageActions as r,OptionsPageToggles as c}from"../../common/constant.js";await n.init();const l=n.getItem("appLocale");let p,m,d,g,_;const u=new Promise((e=>_=e)),I=$("#cdn-iframe"),h=await chrome.tabs.query({active:!0,lastFocusedWindow:!0});function f(e){let t=e;return e.tab||(t={...e,tab:h[0]}),chrome.runtime.sendMessage(t)}function E(e,t){f({main_op:"analytics",analytics:[[e,t]]})}const w=e=>{try{const t=new URL(p),a=[/^https:\/\/([a-zA-Z\d-]+\.){0,}(adobe|acrobat)\.com(:[0-9]*)?$/];return e===t.origin&&!!a.find((t=>t.test(e)))}catch(e){return!1}},A=async(e,t)=>{const a=I[0];if(a&&w(t)){const n=new Promise((e=>setTimeout((()=>e(!1)),1e5)));await Promise.race([u,n])&&a.contentWindow.postMessage(e,t)}};async function b(o){let i={main_op:"send-analytics"};o.target.checked&&n.setItem("viewer-enabled-source","ownership-consent"),n.getWithTTL("ownership-upgrade")&&!o.target.checked&&(t.analytics(i,e.USE_ACROBAT_VIEWER_DISABLED_DEFAULT_OWNERSHIP,{SOURCE:"trefoilMenu"}),t.analytics(i,e.USE_ACROBAT_VIEWER_DISABLED_DEFAULT_OWNERSHIP_EXPERIMENT,{SOURCE:"trefoilMenu",EXPERIMENT:n.getItem("experiment-ownership")}),n.removeItem("ownership-upgrade"),n.removeItem("defaultOwnerShipExperiment")),n.setItem("pdfViewer",`${o.target.checked}`),f({main_op:"changeDefaultViewershipForSurface",surfaceId:"gmail",isDefault:o.target.checked,source:"extensionPopup"}),f({main_op:"changeDefaultViewershipForSurface",surfaceId:"gdrive",isDefault:o.target.checked,source:"extensionPopup"});const s=o.target.checked?"enabled":"disabled";await a.setViewerState(s);try{n.removeItem("netAccAdT"),n.removeItem("netAcc"),n.removeItem("netAccCN")}catch(e){}o.target.checked?t.isEdge()?t.analytics(i,e.USE_ACROBAT_IN_EDGE_ENABLED):t.isChrome()&&t.analytics(i,e.USE_ACROBAT_IN_CHROME_ENABLED):t.isEdge()?t.analytics(i,e.USE_ACROBAT_IN_EDGE_DISABLED):t.isChrome()&&t.analytics(i,e.USE_ACROBAT_IN_CHROME_DISABLED),setTimeout((()=>{g?f({panel_op:"viewer_menu",reload_in_native:!0,tabId:h[0].id}):d&&chrome.tabs.reload(),f(i),f({panel_op:"options_page",requestType:r.OPTIONS_UPDATE_TOGGLE,toggleId:c.ViewerOwnershipTitle,toggleVal:o.target.checked}),t.isEdge()&&f({main_op:"pdfViewerChanged",value:`${o.target.checked}`})}),20)}function T(){E(e.TREFOIL_SETTINGS_ICON_CLICKED),chrome.runtime.openOptionsPage((t=>{chrome.runtime.lastError&&E(e.TREFOIL_SETTINGS_FAILED_TO_OPEN)}))}function D(){E(e.TREFOIL_ACROBAT_LABEL_CLICKED),f({main_op:"go_to_aonline",verb:"acrobat_label"})}window.addEventListener("message",(async e=>{if(!w(e.origin))return;const t={...e.data};switch(t.main_op){case"cdnReady":_(!0);break;case"go_to_aonline":f({...t,locale:l||n.getItem("locale")});break;case"convertToPDFPopupMenu":case"appendToExistingPDFPopupMenu":case"cancelWebpageConversion":case"clearStatus":case"open_converted_file":case"get-frictionless-url":case"timing_info":f(t);break;case"relay_to_content":t.main_op="external_msg",t.data=e.data,t.timeStamp=Date.now(),f(t);break;case"branding-clicked":D();break;case"settings-clicked":T();break;case"viewership-toggle":{const{checked:e}=t;b({target:{checked:e}});break}case"open-ai-assistant":window.close(),f(g?{main_op:"openViewerAIAssistant"}:{type:"open_side_panel",touchpoint:"TrefoilMenu"});break;case"toggle-ai-assistant":n.setItem("enableGenAIFab",`${t.value}`);break;case"saveVisitorID":if(e.data.visitorID){const t=n.getItem("viewerVisitorID");n.setItem("viewerVisitorID",e.data.visitorID),t&&t!==e.data.visitorID&&E("DCBrowserExt:Analytics:viewerVisitorID:MCMID:Changed")}}})),chrome.runtime.onMessage.addListener((e=>{switch(e.panel_op){case"status":{const{action:t}=e,a={type:0===t?"convertToPDFPopupMenu":"appendToExistingPDFPopupMenu",...e};A(a,m);break}case"load-frictionless":{const t=function(e,t){if(e.hide_spinner)return void I.removeClass("loader");const a=e.frictionless_uri;let o=new URL(a);if(t&&(t.locale=l||chrome.i18n.getMessage("@@ui_locale"),Object.keys(t).forEach((e=>{o.searchParams.append(e,t[e])}))),"false"===n.getItem("logAnalytics")){let e=o.toString();e=e.concat("&app!analytics=disable");try{o=new URL(e)}catch(e){o=""}}return f({iframe_call_time:Date.now(),main_op:"timing_info"}),o}(e,{verb:e.pdf_action,workflow:e.frictionless_workflow,dropzone2:"true"});t&&A({type:"frictionless-url",url:t.href},m);break}}})),async function(){const a=performance.now();i(),E(e.EXT_MENU_ICON_CLICKED),$("#pdfOwnershipExploreAcrobat").text(t.getTranslation("pdfOwnershipExploreAcrobat")),$("#offlineModeTitle").text(t.getTranslation("popupNoConnection")),$("#offlineModeMessage").text(t.getTranslation("popupOfflineMessage"));const r="true"===n.getItem("pdfViewer");r||I.addClass("iframe-with-footer");const c=await f({main_op:"initialise-popup"}),{action:_,current_status:u,hostedURL:w,is_pdf:P,is_extension_url:v}=c;p=w,d=P,g=v,u&&(p=0===_?`${p}#/web-to-pdf`:`${p}#/add-webpage-to-pdf`);const k=new URL(p);m=k.origin;const y="false"!==n.getItem("logAnalytics"),C="false"!==n.getItem("ANALYTICS_OPT_IN_ADMIN");let O=n.getItem("viewer-locale");O||(O=n.getItem("locale")),k.searchParams.append("locale",l||O),k.searchParams.append("la",y&&C),k.searchParams.append("ao",n.getItem("aoem"));const L=n.getItem("installType")||"update",S=n.getItem("installSource");k.searchParams.append("version",`${chrome.runtime.getManifest().version}:${L}`),k.searchParams.append("installSource",S),k.searchParams.append("callingApp",chrome.runtime.id),n.getItem("enableExtMenuDarkMode")&&k.searchParams.append("theme",n.getItem("theme")||"auto");let M=!1;try{M=await chrome.tabs.sendMessage(h[0].id,{main_op:"shouldDisableGenAIForWebPagesTPs"})}catch(e){M=!0}k.searchParams.append("disableAskAIAssistant",M);const N=await async function(){let e=!1;try{e=!(!n.getItem("sidePanelRegistered")||o())&&(s(h[0].url)?await f({main_op:"isGenAIEligibleInCDN",activeTabId:h[0].id}):await chrome.tabs.sendMessage(h[0].id,{main_op:"shouldShowGenAIForWebPagesTPs"}))}catch(t){e=!1}return e}();k.searchParams.append("showAskAIAssistant",N),k.searchParams.append("genAIFabEnabled","true"===n.getItem("enableGenAIFab"));try{if(!(await fetch(k,{method:"HEAD"})).ok)throw new Error;I.attr("src",k.href),I[0].onload=()=>{const e=(performance.now()-a)/1e3,t=Number.parseFloat(e).toFixed(2);A({...c,isViewerEnabled:r,timeToLoad:t,type:"init"},m)}}catch{!function(){E(e.EXT_MENU_CDN_OFFLINE),I.remove();const t="true"===n.getItem("pdfViewer");$("#toggle-input").prop("checked",t),t||$("#footer").removeClass("hidden"),$(".settings-icon").click(T),$("#toggle-input").click(b),$("#branding").click(D),$("#offline-mode").removeClass("hidden")}()}}();