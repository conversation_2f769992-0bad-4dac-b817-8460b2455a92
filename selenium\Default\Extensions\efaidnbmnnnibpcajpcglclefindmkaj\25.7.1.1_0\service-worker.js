/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{dcLocalStorage as e}from"./common/local-storage.js";import{hydrateWithStorage as o}from"./sw_modules/hydrate.js";import{communicate as s}from"./sw_modules/communicate.js";import{tempUrlBufferClean as t}from"./sw_modules/temp-url-buffer-cleanup.js";import{onMessageListener as r}from"./sw_modules/common.js";import{externalClients as n}from"./sw_modules/externalClients.js";import{feat as a}from"./sw_modules/feat.js";import{session as m}from"./sw_modules/session.js";import{acroweb2pdf as d}from"./sw_modules/acro-web2pdf.js";import{userSubscriptionAlarmHandler as i}from"./sw_modules/user-subscription.js";import{startQAWithWebpage as c}from"./sw_modules/ch-context-menu.js";import"./sw_modules/acro-gstate.js";import{startup as p,registerActions as l,onWelcomeTabRemoved as u,contextMenuOnClickHandler as h,updateAnalyticsOptInAdmin as f,refocusLocalFteWindow as b,onLocalFileClosed as w,onLocalFteWindowClosed as g,onDownloadChanged as _}from"./sw_modules/ch-context-menu.js";import{processRequest as L,honorRequest as y,onTabsUpdated as I,onTabActivated as x,logWebpageDomain as j,handlePrefetchValidation as v,detectAndStorePrefetch as R,cleanupPrefetchForTab as A}from"./sw_modules/viewer-module.js";import{indexedDBScript as T}from"./common/indexDB.js";import{offscreenActions as C}from"./sw_modules/offscreen-actions.js";import{analytics as M}from"./common/analytics.js";import{expressModalTabCloseListener as S}from"./sw_modules/express.js";import{sidepanelState as D}from"./sw_modules/SidepanelState.js";import{loggingApi as P}from"./common/loggingApi.js";import k from"./sw_modules/ExplicitBlocklist.js";const B=["http","https","ftp","file","blob","data","filesystem","drive"];try{const C=chrome.runtime.getURL("/");chrome.runtime.onConnect.addListener((o=>{const[s,t,r]=o.name.split("_");"sidepanel"===s&&(o.onMessage.addListener((()=>{})),chrome.tabs.sendMessage(parseInt(t),{action:"sidepanel_opened"}),D.setIsOpen(parseInt(t),!0),o.onDisconnect.addListener((async()=>{chrome.tabs.sendMessage(parseInt(t),{action:"sidepanel_closed",initTimeStamp:r}),D.setIsOpen(parseInt(t),!1);const o=Date.now(),{version:s}=chrome.runtime.getManifest();await e.init(),P.info({message:"DC Acrobat Extn: Sidepanel closed",timestamp:o,duration:Math.round((o-r)/100)/10,durationInMs:o-r,adobeInternal:"true"===e.getItem("adobeInternal"),extensionVersion:s})})))})),chrome.management.getSelf(o(p)),chrome.runtime.onInstalled.addListener(o(l)),chrome.contextMenus.onClicked.addListener(o(h)),chrome.runtime.onStartup.addListener(o((()=>e.removeItem("loadedTabsInfo")))),chrome.runtime.onMessageExternal.addListener(o(a,n)),chrome.action.onUserSettingsChanged?.addListener(o((async o=>{const s=e.getItem("pinToolbarSettingsWindowId");if(s&&o.isOnToolbar)try{await chrome.windows.remove(s)}catch(e){}}))),chrome.webRequest.onHeadersReceived.addListener((e=>{o((e=>{v(e)||(L(e,!1),j(e))}))(e)}),{urls:["http://*/*","https://*/*"],types:["main_frame","sub_frame"]},["responseHeaders"]),chrome.webRequest.onBeforeRequest.addListener((e=>{o(L)(e,!0)}),{urls:["file://*/*.pdf","file://*/*.PDF"],types:["main_frame","sub_frame"]}),chrome.webRequest.onBeforeSendHeaders.addListener((e=>{o(R)(e)}),{urls:["http://*/*","https://*/*"],types:["main_frame"]},["requestHeaders"]),chrome.webRequest.onBeforeRequest.addListener(o(y),{urls:B.map((e=>C+e+"*")),types:["main_frame"]}),chrome.webNavigation.onBeforeNavigate.addListener(o(s.handlerTabs),{frameType:"outermost_frame"}),chrome.runtime.onMessage.addListener(((...e)=>{const[t,n,a]=e;switch(t?.type){case"getWindowType":n?.tab?.windowId?chrome.windows.get(n.tab.windowId).then((e=>{const o="popup"===e.type,s="app"===e.type;a({isPopup:o,isApp:s})})):a({isPopup:!1,isApp:!1});break;case"open_side_panel":c(t,n.tab||t.tab);break;case"open_options_page":chrome.runtime.openOptionsPage();break;case"send_to_sidepanel":chrome.runtime.sendMessage({...t,tabId:n.tab?.id});break;case"get_sidepanel_state":a({isOpen:D.getIsOpen(n.tab?.id)});break;case"get_blocked_domains":k.get().then((e=>a(e))).catch((()=>a([])))}return o(r,s.proxy(s.handler))(...e),!0})),chrome.tabs.onRemoved.addListener(o(s.proxy(s.close),m.proxy(m.onSessionTabRemoved),u,((...o)=>{e.getItem("LocalFileAccessTouchpointsFromViewer")||T.deleteDataFromIndexedDB(...o)}),w,s.proxy(s.getCurrentTabInfoAndUpdateLoadedTabs),S,(e=>D.clear(e)),A)),chrome.tabs.onUpdated.addListener(o(I)),chrome.storage.managed.onChanged.addListener(o(f)),chrome.extension.isAllowedFileSchemeAccess(o(s.proxy(s.setIsAllowedLocalFileAccess))),chrome.tabs.onActivated.addListener((async(...e)=>{o(x,s.proxy(s.active),b)(...e)})),chrome.tabs.onReplaced.addListener(o(s.proxy(s.tabReplace))),chrome.tabs.onMoved.addListener(o(s.proxy(s.getCurrentTabInfoAndUpdateLoadedTabs))),d.setNcPort(chrome.runtime.connectNative("com.adobe.acrobat.chrome_webcapture")),d.ncPort.onMessage.addListener(d.proxy(d.nativeMessageCallback)),d.ncPort.onDisconnect.addListener(d.proxy(d.nativeOnDisconnect)),chrome.alarms.onAlarm.addListener(o(i)),chrome.alarms.onAlarm.addListener(t.deleteTempURLBufferEntries),chrome.windows.onRemoved.addListener(o(g)),chrome.downloads.onChanged.addListener(o(_)),chrome.downloads.onCreated.addListener((e=>{e?.finalUrl&&new URL(e?.finalUrl)?.searchParams?.has("acrobatPromotionSource")&&(chrome.downloads.cancel(e?.id),M.event("DCBrowserExt:PromotionSource:Download:Cancel"))}))}catch(e){console.log(e)}