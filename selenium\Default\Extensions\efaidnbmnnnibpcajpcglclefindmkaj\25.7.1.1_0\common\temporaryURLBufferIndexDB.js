/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{loggingApi as r}from"./loggingApi.js";import{dcTabStorage as e}from"../browser/js/tab-storage.js";import{BUFFER_CLEAN_UP_INTERVAL as t,TEMP_BUFFER_CLEAN_UP_ALARM_NAME as o}from"./constant.js";const s=new class{constructor(){this.objectStoreName="pdfFile",this.dbPromise=new Promise((e=>{const t=indexedDB.open("TemporaryURLBufferIndexDB",1);t.onupgradeneeded=r=>{const e=r.target.result;e.objectStoreNames.contains(this.objectStoreName)||e.createObjectStore(this.objectStoreName)},t.onsuccess=r=>{this.db=r.target.result,e(this.db)},t.onerror=()=>{r.error({message:"indexeddb could not be opened",error:t.error})}}))}createAlarmForCleanUp(){chrome.alarms.get(o,(r=>{r||chrome.alarms.create(o,{periodInMinutes:t})}))}async storeInIndexedDB(t,o){try{this.createAlarmForCleanUp();const s=await this.getApplicationStore("readwrite"),n={fileBuffer:t,tabId:o,pdfUrl:this.extractParam(e.getItem("search"),"pdfurl")},a=s.put(n,o.toString());return new Promise(((e,t)=>{a.onsuccess=r=>{e(r.target.result)},a.onerror=e=>{r.error({message:"Error in updating buffer",error:e.target.error}),t(e.target.error)}}))}catch(e){return r.error({message:"Error in updating buffer",error:e}),Promise.reject(e)}}async getApplicationStore(e){const t=(await this.dbPromise).transaction(this.objectStoreName,e);t.onerror=function(e){return r.error({message:"Error in transaction",error:e.target.error}),Promise.reject(e.target.error)};return t.objectStore(this.objectStoreName)}async cleanTemporaryURLBufferEntries(){try{const e=(await chrome.tabs.query({}))?.map((r=>r.id.toString())),t=await this.getApplicationStore("readwrite"),o=t.openCursor();return new Promise(((s,n)=>{o.onsuccess=async r=>{const o=r.target.result;if(o){const r=o.key;e?.includes(r)||t.delete(o.primaryKey),o.continue()}else s(!0)},o.onerror=e=>{r.error({message:"Error in clearing old file buffer entries",error:e.target.error}),n(e.target.error)}}))}catch(e){return r.error({message:"Error in clearing old file buffer entries",error:e}),Promise.reject(e)}}async cleanTemporaryURLBufferEntry(e){try{const t=(await this.getApplicationStore("readwrite")).delete(e.toString());return new Promise(((e,o)=>{t.onsuccess=()=>{e(0===t?.result?.length)},t.onerror=e=>{r.error({message:"Error deleting key existence",error:e.target.error}),o(e.target.error)}}))}catch(e){return r.error({message:"Error checking key existence",error:e}),Promise.reject(e)}}async hasKey(e){try{const t=(await this.getApplicationStore("readonly")).count(e.toString());return new Promise(((e,o)=>{t.onsuccess=r=>{e(r.target.result>0)},t.onerror=e=>{r.error({message:"Error in getting buffer",error:e.target.error}),o(e.target.error)}}))}catch(e){return r.error({message:"Error checking key existence",error:e}),Promise.reject(e)}}async getDataFromIndexedDB(t){try{const o=(await this.getApplicationStore("readonly")).get(t.toString());return new Promise(((t,s)=>{o.onsuccess=r=>{const o=r.target.result,s=this.extractParam(e.getItem("search"),"pdfurl");o&&o.pdfUrl===s?t(o):t({})},o.onerror=e=>{r.error({message:"Error in getting buffer",error:e.target.error}),s(e.target.error)}}))}catch(e){return r.error({message:"Error in getting buffer",error:e}),Promise.reject(e)}}extractParam(r,e){return new URLSearchParams(r).get(e)||""}async getCount(){try{const e=(await this.getApplicationStore("readwrite")).getAllKeys();return new Promise(((t,o)=>{e.onsuccess=r=>{t(r.target.result.length)},e.onerror=e=>{r.error({message:"Error in getting count",error:e.target.error}),o(e.target.error)}}))}catch(e){return r.error({message:"Error in getting count",error:e}),Promise.reject(e)}}};export{s as tempURLBufferIndexedDB};