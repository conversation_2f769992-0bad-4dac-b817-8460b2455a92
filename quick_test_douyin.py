#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试抖音发布功能
验证修复后的核心功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.services.platform_publishers import DouyinPublisher, BrowserManager
from src.services.new_one_click_publisher import VideoInfo
from src.utils.logger import logger

def test_browser_creation():
    """测试浏览器管理器创建"""
    print("🧪 测试浏览器管理器创建...")

    try:
        # 测试Firefox（固定使用）
        firefox_browser = BrowserManager()
        print("✅ Firefox浏览器管理器创建成功")

        return True
    except Exception as e:
        print(f"❌ 浏览器管理器创建失败: {e}")
        return False

def test_douyin_publisher_creation():
    """测试抖音发布器创建"""
    print("🧪 测试抖音发布器创建...")

    try:
        # 测试抖音发布器（固定使用Firefox）
        douyin_publisher = DouyinPublisher()
        print("✅ 抖音发布器创建成功")

        return True
    except Exception as e:
        print(f"❌ 抖音发布器创建失败: {e}")
        return False

def test_video_info_creation():
    """测试视频信息对象创建"""
    print("🧪 测试视频信息对象创建...")
    
    try:
        video_info = VideoInfo(
            file_path="test_video.mp4",
            title="测试视频标题",
            description="这是一个测试视频描述 #AI #测试"
        )
        
        print(f"✅ 视频信息创建成功:")
        print(f"   - 文件路径: {video_info.file_path}")
        print(f"   - 标题: {video_info.title}")
        print(f"   - 描述: {video_info.description}")
        
        return True
    except Exception as e:
        print(f"❌ 视频信息创建失败: {e}")
        return False

def test_selectors():
    """测试选择器配置"""
    print("🧪 测试选择器配置...")

    try:
        publisher = DouyinPublisher()
        selectors = publisher.selectors

        print("✅ 选择器配置:")
        for key, value in selectors.items():
            print(f"   - {key}: {value}")

        return True
    except Exception as e:
        print(f"❌ 选择器配置测试失败: {e}")
        return False

def test_platform_url():
    """测试平台URL配置"""
    print("🧪 测试平台URL配置...")

    try:
        publisher = DouyinPublisher()
        url = publisher.platform_url

        print(f"✅ 抖音平台URL: {url}")

        if "creator.douyin.com" in url:
            print("✅ URL配置正确")
            return True
        else:
            print("❌ URL配置可能有问题")
            return False

    except Exception as e:
        print(f"❌ 平台URL测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 抖音发布功能快速测试")
    print("=" * 50)
    
    tests = [
        ("浏览器管理器创建", test_browser_creation),
        ("抖音发布器创建", test_douyin_publisher_creation),
        ("视频信息对象创建", test_video_info_creation),
        ("选择器配置", test_selectors),
        ("平台URL配置", test_platform_url)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   - {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！")
        print("\n💡 下一步:")
        print("1. 运行主程序: python main.py")
        print("2. 选择'新一键发布'标签页")
        print("3. 程序将自动启动Firefox浏览器")
        print("4. 在Firefox中登录抖音创作者中心")
        print("5. 使用一键发布功能发布视频")
        return 0
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    exit(main())
