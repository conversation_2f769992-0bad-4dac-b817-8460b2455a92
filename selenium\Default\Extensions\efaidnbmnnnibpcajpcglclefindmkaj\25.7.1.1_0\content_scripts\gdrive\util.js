/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import state from"./state.js";import{sendAnalyticsOncePerMonth,getElementListForSelectors}from"../gsuite/util.js";const FILE_DATA_ID_ATTRIBUTE="data-id",GDRIVE_TOUCH_POINT_CLASS="acrobat-gdrive-touch-point",GDRIVE_PDF_ELEMENT_PROCESSED="acrobat-processed",GDRIVE_PDF_ELEMENT_EXCLUDED="acrobat-excluded",checkDivImageType=(e,t)=>{const i=e?.getElementsByTagName("path");if(i&&i.length>0){const e=i[0]?.getAttribute("d");if(t?.includes(e))return!0}return!1},isFileElementSelected=e=>{const t="true"===e.getAttribute("aria-selected"),i="true"===e?.childNodes[0]?.getAttribute("aria-selected");return t||i},getSelectedFiles=e=>Array.from(e).filter((e=>isFileElementSelected(e))),getEligiblePdfFileElementList=(e,t,i)=>e.filter((e=>{if(e.hasAttribute("acrobat-excluded"))return!1;const r=0===e.getElementsByClassName(GDRIVE_TOUCH_POINT_CLASS)?.length,l=checkDivImageType(e,t),s=getElementListForSelectors(i?.exclude,e)?.length>0;return r&&l&&!s?(e.setAttribute("acrobat-processed",!0),!0):(l&&s?sendAnalyticsOncePerMonth(`DCBrowserExt:GDrive:${state?.selectedView}:PdfFileNotSupported`,{eventContext:state?.driveUrlPath}):l&&!s||e.setAttribute("acrobat-excluded","true"),!1)})),setDrivePath=()=>{const e=window.location.pathname;if(""!==state?.driveUrlPath&&state?.prevDriveUrlPath===e)return state?.driveUrlPath;const t=e?.split("/");return t?.length>=3&&(t?.length>=5&&"u"===t[2]?state.driveUrlPath=t[4]:t?.length>=3&&(state.driveUrlPath=t[2])),state.prevDriveUrlPath=e,state?.driveUrlPath},getSelectorsForCurrentPathAndSelectedView=()=>{const e=state?.config?.selectors[state?.selectedView];let t=state?.driveUrlPath;return e?.pathSelector?(e?.pathSelector[t]||(t="default"),e?.pathSelector[t]):{fileElement:[],fileTitle:[],touchpointContainer:[]}},setSelectedView=()=>{isListViewEligible()?state.selectedView="GoogleDriveListView":isGridViewEligible()&&(state.selectedView="GoogleDriveGridView")},isListViewEligible=()=>state?.config?.enableGDriveListViewTouchPoint&&state?.config?.selectors?.GoogleDriveListView&&(checkViewToggleButton(state?.config?.selectors?.GoogleDriveListView)||state?.config?.selectors?.GoogleDriveListView?.defaultViewPath?.includes(state?.driveUrlPath)),isGridViewEligible=()=>state?.config?.enableGDriveGridViewTouchPoint&&state?.config?.selectors?.GoogleDriveGridView&&checkViewToggleButton(state?.config?.selectors?.GoogleDriveGridView),checkViewToggleButton=e=>{const t=e?.currView,i=t?.btnKey,r=getElementListForSelectors(t?.viewBtn);if(r?.length>=0){if(0===r[0]?.childNodes?.length)return!1;const e=r[0]?.childNodes[i];if(!e)return!1;return"true"===e.getAttribute("aria-checked")}return"quota"!==state?.driveUrlPath&&sendAnalyticsOncePerMonth(`DCBrowserExt:GDrive:${state?.selectedView}:ViewButtonNotPresent`,{eventContext:state?.driveUrlPath}),!1},createUrlForAcrobatTouchPoint=(e,t)=>{const i=`https://drive.usercontent.google.com/download?id=${e?.id}&authuser=${state?.userId}&acrobatPromotionSource=${t}`;return chrome.runtime.getURL("viewer.html")+"?pdfurl="+encodeURIComponent(i)+"&pdffilename="+encodeURIComponent(e?.title)},createCustomClassNameBasedOnViewAndPath=(e,t,i,r,l)=>"GoogleDriveListView"===t?`${e}-${t}`:"GoogleDriveGridView"===t?"home"===i?`${e}-${t}-${i}`:r===l?`${e}-${t}-pdfFileHost`:"recent"===i?`${e}-${t}-recent`:`${e}-${t}`:"",getPdfFileDetails=(e,t)=>{const i={id:"",title:""},r=e?.getAttribute("data-id"),l=getElementListForSelectors(t?.fileTitle,e);if(r&&(i.id=r),l?.length>0){const e=l[0]?.innerText;e&&isFileTypePdfInTitle(e)&&(i.title=e)}return i},isFileTypePdfInTitle=e=>e.endsWith(".pdf")||e.endsWith(".PDF"),isEventTargetWithinElement=(e,t)=>!!t&&t.contains(e?.target),areFilesPdfs=e=>{if(!e)return!1;const t=Array.isArray(e)?e:[e];if(0===t.length)return!1;const i=state?.config?.selectors[state?.selectedView]?.pdfSvgPath;return t.every((e=>checkDivImageType(e,i)))},buildClassSelectorString=e=>e?e.map((e=>`.${e}`)).join(","):"",areFileDetailsValid=e=>""!==e?.id&&""!==e?.title,isKeyboardEventOnFileElement=e=>e?.target?.hasAttribute("data-id")||e?.target?.parentElement?.hasAttribute("data-id");export{createUrlForAcrobatTouchPoint,createCustomClassNameBasedOnViewAndPath,getEligiblePdfFileElementList,getSelectorsForCurrentPathAndSelectedView,getSelectedFiles,getPdfFileDetails,setDrivePath,setSelectedView,isFileElementSelected,isEventTargetWithinElement,areFilesPdfs,buildClassSelectorString,isFileTypePdfInTitle,areFileDetailsValid,isKeyboardEventOnFileElement};