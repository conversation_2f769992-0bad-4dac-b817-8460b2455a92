/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
class GenAIWebpageEligibilityService{static isReadable=null;static shouldShow=null;static encodeMap=this.getEncodeMap();static isEdgeBrowser(){return navigator.userAgentData?navigator.userAgentData.brands.some((e=>"Microsoft Edge"===e.brand)):navigator.userAgent.includes("Edg/")}static async isPopupOrApp(){const e=await chrome.runtime.sendMessage({type:"getWindowType"}),{isPopup:i,isApp:t}=e;return i||t}static reset(){this.isReadable=null,this.shouldShow=null}static trackDomain(e,i){window.dcLocalStorage.getItem("allDomainTracking")&&chrome.runtime.sendMessage({main_op:"log-info",log:{message:e,domain:i}})}static async getExplicitBlockList(){try{return await chrome.runtime.sendMessage({type:"get_blocked_domains"})||[]}catch(e){return[]}}static isSupportedLanguage(e){return["en-US","en-GB"].includes(e)||e.startsWith("en")}static getEncodeMap(){const e="abcdefghijklmnopqrstuvwxyz0123456789.-",i={};for(let t=0;t<38;t++)i[e[t]]="tubg6lxdq8jc1m9-vw.pfhskar0oi5zn372ye4"[t];return i}static encodeDomain(e){return e.toLowerCase().split("").map((e=>this.encodeMap[e]||e)).join("")}static async _shouldShowTouchpoints(){if(this.isEdgeBrowser())return!1;await initDcLocalStorage();if(!window.dcLocalStorage.getItem("sidePanelRegistered"))return!1;const e=chrome.runtime.getURL(""),i=window.location.href,t=new URL(i).hostname;if(document.contentType&&document.contentType.includes("application/pdf"))return this.trackDomain("Page Disqualified - Webpage is a PDF",t),!1;if(await this.isPopupOrApp())return this.trackDomain("Page Disqualified - Webpage is in a popup or app",t),!1;const a=window.dcLocalStorage.getItem("appLocale")||chrome.i18n.getMessage("@@ui_locale");if(!this.isSupportedLanguage(a))return this.trackDomain("Page Disqualified - Unsupported language",t),!1;if(i.startsWith(e)||GENAI_WEBPAGE_BLOCKLIST.find((e=>i.includes(e))))return this.trackDomain("Page Disqualified - Webpage is in the blocklist",t),!1;const s=window.dcLocalStorage.getItem("hideFabDomainList")||[];if((window.dcLocalStorage.getItem("genaiWebpageBlockList")||[]).includes(t))return this.trackDomain("Page Disqualified - Webpage is in the floodgate blocklist",t),!1;if(s.includes(t))return this.trackDomain("Page Disqualified - Webpage is in the user blocklist",t),!1;if(!await this.isCurrentWebPageReadable())return this.trackDomain("Page Disqualified - Webpage is not readable",t),!1;return(await this.getExplicitBlockList()).includes(this.encodeDomain(t))?(this.trackDomain("Page Disqualified - Webpage is in the explicit blocklist","re.dac.ted"),!1):(this.trackDomain("Page Qualified - Webpage is readable",t),!0)}static async isCurrentWebPageReadable(){await initDcLocalStorage();if("true"===window.dcLocalStorage.getItem("bypassReadabilityEligibility"))return!0;if(null!==this.isReadable)return this.isReadable;const e=chrome.runtime.getURL("resources/SidePanel/sidePanelUtil.js"),{isProbablyReaderableAsync:i}=await import(e);return this.isReadable=await i(document),this.isReadable}static async shouldShowTouchpoints(){return null!==this.shouldShow||(this.shouldShow=await this._shouldShowTouchpoints()),this.shouldShow}static async shouldDisableTouchpoints(){return!1}}