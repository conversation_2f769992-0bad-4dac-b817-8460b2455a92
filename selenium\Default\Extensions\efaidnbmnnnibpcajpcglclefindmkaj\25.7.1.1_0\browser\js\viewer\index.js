/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{dcLocalStorage as e,dcSessionStorage as t}from"../../../common/local-storage.js";import{dcTabStorage as a}from"../tab-storage.js";import{util as n}from"../content-util.js";import{signInUtil as r}from"./signInUtils.js";import{privateApi as i}from"../content-privateApi.js";import{COOLDOWN_FOR_LFT_PROMPT as o,OptionPageActions as s,LOCAL_FILE_PERMISSION_URL as c,LOCAL_FTE_WINDOW as d}from"../../../common/constant.js";import{indexedDBScript as l}from"../../../common/indexDB.js";import{loggingApi as m}from"../../../common/loggingApi.js";import{updateExtUserState as p,isNewUser as g,isEdgeBrowser as f,isDemoMode as u,getGenAIServiceVariant as h}from"../../../common/util.js";import I from"./ResponseHeaders.js";import w from"./BookmarkUtils.js";import b from"./LruUtil.js";import{analytics as v,events as y}from"../../../common/analytics.js";import{fileUtil as _}from"./fileUtil.js";import{getDefaultViewershipStatusEventParam as S}from"../../../content_scripts/gsuite/util.js";import{initializeBlobCacheCleanupAlarm as L}from"../../../content_scripts/blob-cache-cleanup.js";import{EXPERIMENT_VARIANTS_STORAGE_KEY as R}from"../../../sw_modules/constant.js";import{tempURLBufferIndexedDB as U}from"../../../common/temporaryURLBufferIndexDB.js";await e.init(),await t.init();const P=e.getItem("appLocale"),T="acrobatPromotionSource";let E=!1;!function(){let d,k,D,C,F,A,B,x,M,V,O,N,$,W,H,G,j,z,q,J,Y,K,X,Z,Q,ee="",te=!1;const ae=chrome.runtime.getURL("viewer.html"),ne=chrome.runtime.getURL("signInHandler.html"),re="file:",ie="blob:",oe=["https:","http:",re];let se,ce;se=new Promise((e=>ce=e)),le({main_op:"chrome_viewer_opened",activeTabId:me(document.location.search,"tabId")});function de(e){const t=a.getItem("search");return new URLSearchParams(t).get(e)}function le(e,t){return W?(H=H||1,e.tabId=H,e.mimeHandled=!0,chrome.runtime.sendMessage(e,t)):chrome.runtime.sendMessage(e,t)}function me(e,t){return new URLSearchParams(e).get(t)||""}async function pe(){if(C=me(document.location.search,"pdfurl"),K=me(document.location.search,"tabId"),X=me(document.location.search,"aw"),Q=await(async e=>{if(!e)return!1;try{const t=new URL(e),a=t.protocol;let n=-1!==oe.indexOf(a);return n=a===re?t.pathname.toLowerCase().endsWith(".pdf"):n,a===ie&&(n=await l.hasKey(`chrome-extension://${chrome.runtime.id}/${t.href}`),n&&(L(),te=!0)),n}catch(e){return!1}})(C),!Q)return void(F=!1);te&&(C=`chrome-extension://${chrome.runtime.id}/${C}`),function(){const e=new URLSearchParams(document.location.search),n=t.getItem("rtParams");if(n){const a=n.split(",").map((t=>e.has(t)?`&${t}=${e.get(t)}`:null)).join("")||"";t.setItem("payPalUrl",a),t.removeItem("rtParams")}e.has("dialog!dropin")&&a.setItem("dialog!dropin",e.get("dialog!dropin")),e.has("load!dropin")&&a.setItem("load!dropin",e.get("load!dropin"))}();const e=a.getItem("search");if((!e||me(e,"pdfurl")!==C||e.length<document.location.search)&&a.setItem("search",document.location.search),D=me(document.location.search,"pdffilename")||me(e,"pdffilename")||Be(C),document.title=D,!te){const e="/"+C+location.hash;history.replaceState({},D,e)}}function ge(t,a){e.setItem(`reloadurl-${t.id}`,C);const n=new URL(C);if(function(e){e?.searchParams?.has(T)&&m.info({message:"reloadInNativeViewer",source:e.searchParams.get(T)})}(n),rt(n)&&!a?.includes("text/html")){const e=n?.searchParams?.get("id");window.location.href=`https://drive.google.com/file/d/${e}`}else window.location.href=C}function fe(e=!1,t){if(W)try{e||le({main_op:"viewer-type",viewer:"mime-native"}),setTimeout((()=>{i.reloadWithNativeViewer({contentLength:parseInt(d)||0})}),100)}catch(e){he("DCBrowserExt:Viewer:FallbackToNative:Failed")}else try{setTimeout((()=>{chrome.tabs.getCurrent((e=>ge(e,t)))}),500)}catch(e){he("DCBrowserExt:Viewer:FallbackToNative:Failed")}}const ue=t=>{try{const a=new URL(e.getItem("cdnUrl")),n=[/^https:\/\/([a-zA-Z\d-]+\.){0,}(adobe|acrobat)\.com(:[0-9]*)?$/];return t===a.origin&&!!n.find((e=>e.test(t)))}catch(e){return!1}};function he(e){const t={main_op:"analytics"};t.analytics=[[e]],le(t)}function Ie(){return at(C)?`${ae}?pdfurl=${encodeURIComponent(C)}&pdffilename=${encodeURIComponent(document.title)}`:C}function we(){let e,t=ae;if(W)if(te){const t=new URLSearchParams(window.location.search),a=t.get("pdfurl"),n=t.get("pdffilename");e="?pdfurl="+encodeURIComponent(a)+"&pdffilename="+encodeURIComponent(n)}else e="?mimePdfUrl="+encodeURIComponent(C),t=ne;else e=a.getItem("search"),e||(e="?pdfurl="+encodeURIComponent(C));return new URL(t+e)}const be=["AdobeID","openid","DCAPI","sign_user_read","sign_user_write","sign_user_login","sign_library_read","sign_library_write","agreement_send","agreement_read","agreement_write","ab.manage","additional_info.account_type","sao.ACOM_ESIGN_TRIAL","widget_read","widget_write","workflow_read","workflow_write"];function ve(t={}){if(e.getItem("csi")){const e=Ie(),a={...t,pdfUrl:e};return void r.cdnSignIn(a)}const a=we(),i=e.getItem("cdnUrl"),o=t.sign_up?1:0,s=n.generateStateCSRF(),c=e.getItem("enableCSRF"),d=JSON.stringify({touchp:t.touchpoint||"",signIn:!0,...c&&{state:s}}),l=e.getItem("theme")||"auto",m=`${i}?la=true&locale=${P||e.getItem("locale")}&theme=${l}&ru=${encodeURIComponent(a.href)}&rp=${d}&su=${o}#/susi`;chrome.tabs.update({url:m,active:!0})}function ye(){const e={main_op:"signout",pdfUrl:Ie(),callingApp:chrome.runtime.id};r.cdnSignIn(e)}function _e(t={}){if(e.getItem("csi")){const e={...t,pdfUrl:C};return void r.cdnSignIn(e)}let n=new URL(ne);const i=t.idp_token;return n.searchParams.append("socialSignIn","true"),n.searchParams.append("mimePdfUrl",encodeURIComponent(C)),a.setItem("idp_token",i),n.href}function Se(e={}){W?chrome.tabs.update({url:_e(e),active:!0}):r.socialSignIn(e,we(),C)}function Le(t={}){if(e.getItem("csi")){try{const e=new URL(JSON.parse(t.url));delete t.url;const a={...t,pdfUrl:Ie()},n=r.getCDNSignURL(a);e.searchParams.append("redirect_uri",n),chrome.tabs.update({url:e.href,active:!0})}catch(e){chrome.tabs.update({url:C})}return}const i=t.application||"google",o=e.getItem("viewerImsClientIdSocial"),s=e.getItem("imsURL"),c=n.uuid(),d=we();d.hash=d.hash+"signIn=true";const l=new URL(s+"/ims/authorize/v1"),m={ac:n.getAppCode(),csrf:c};a.setItem("csrf",c),l.searchParams.append("response_type","token"),l.searchParams.append("idp_flow","social.deep_link.web"),l.searchParams.append("client_id",o),l.searchParams.append("provider_id",i),l.searchParams.append("redirect_uri",d),l.searchParams.append("scope",be.join(",")),l.searchParams.append("locale",P||e.getItem("locale")),l.searchParams.append("state",JSON.stringify(m)),l.searchParams.append("xApiClientId",o),l.searchParams.append("xApiClientLocation ",i),chrome.tabs.update({url:l.href,active:!0})}const Re={isSharePointURL:!1,isSharePointFeatureEnabled:!1,isFrictionlessEnabled:!0,featureFlags:[],isFillAndSignRegisteryEnabled:!1};class Ue{constructor(e){this.iframeElement=void 0,this.parentDiv=e}createIframe=t=>{const a=window.document,n=(e.getItem("cdnUrl"),a.createElement("iframe"));n.setAttribute("src",t),n.setAttribute("id","dc-view-frame"),n.setAttribute("allowfullscreen","allowfullscreen"),n.setAttribute("allow","clipboard-read; clipboard-write; local-fonts; payment;"),n.style.width="100vw",n.style.height="100vh",n.style.border="none",n.style.overflow="hidden",this.parentDiv.appendChild(n),this.iframeElement=a.getElementById("dc-view-frame")};_sendMessage=(e,t)=>{this.iframeElement&&ue(t)&&function(e){let t=Date.now();return new Promise((function a(n,r){A&&(F||W)?n(F||W):e&&Date.now()-t>=e?r(new Error("timeout")):setTimeout(a.bind(this,n,r),30)}))}(1e6).then((a=>a&&this.iframeElement.contentWindow.postMessage(e,t)))};sendStartupConfigs=(e,a)=>{this._sendMessage({type:"nativeConfigs",nativeConfigs:Re,extUrl:encodeURI(e),returnParamsUrl:t.getItem("payPalUrl"),isInstallTypeUpsell:E},a),this.sendExperimentInfo(a)};sendFileMetaData=(e,t,a,n,r,i,o,s)=>{this._sendMessage({fileUrl:r,fileName:i,fileSize:a,acceptRanges:n,handShakeTime:t,type:e,isFrictionlessEnabled:Re.isFrictionlessEnabled,isReloadOrBackForward:s,isMimeHandled:W},o)};sendSubmitFormResponse=(e,t)=>{this._sendMessage({type:"submitForm",response:e},t)};sendRecentUrl=async(e,t,a,n=!1)=>{await chrome.extension.isAllowedFileSchemeAccess()||(t=t?.filter((e=>!e.url.startsWith("file://")))),this._sendMessage({type:"RecentUrls",permission:e,showOverlay:n,recentUrls:t},a)};sendProgress=(e,t,a,n)=>{this._sendMessage({total:t,loaded:a,type:e},n)};sendInitialBuffer=(e,t,a,n,r)=>{this._sendMessage({type:e,downLoadstartTime:t,downLoadEndTime:a,buffer:n},r)};sendBufferRanges=(e,t,a,n)=>{this._sendMessage({type:e,range:t,buffer:a},n)};preview=(e,t,n,r,i,o,s)=>{const c="true"===a.getItem("bufferFromIndexedDB");a.removeItem("bufferFromIndexedDB"),this._sendMessage({fileSize:n,type:e,fileBuffer:t,fileName:r,downLoadstartTime:i,downLoadEndTime:o,fromIndexedDB:c,isLocalBlobFile:te},s)};openInAcrobatResponse=(e,t,a)=>{this._sendMessage({type:e,res:t},a)};postLog=(e,t,a,n,r)=>{this._sendMessage({type:e,reqId:t,message:a,error:n},r)};sendCertificateValidationResponse=(e,t)=>{this._sendMessage({type:"certificateValidationResponse",response:e},t)};sendExperimentInfo=t=>{const a=e.getItem(R);let n=Array.isArray(a)?a.sort():[];M._sendMessage({type:"experimentInfo",activeExperiments:n},t)}}function Pe(t,a){try{O=void 0!==O?O:"false"!==e.getItem("logAnalytics")&&"false"!==e.getItem("ANALYTICS_OPT_IN_ADMIN"),O&&(M&&k?M.postLog("log",V,t,a,k.origin):setTimeout((()=>{M&&k&&M.postLog("log",V,t,a,k.origin)}),500))}catch(e){}}function Te(){let e;return e=W?C:window.location.href,e}function Ee(){const t=Te(),n=(t.split("#")||[]).pop();if(n.indexOf("access_token=")>-1)try{const i=new URLSearchParams(n).get("access_token"),{client_id:o}=JSON.parse(window.atob(i.split(".")[1]))||{},s=e.getItem("viewerImsClientId");if([s,e.getItem("viewerImsClientIdSocial")].includes(o)){const e=a.getItem("csrf");a.removeItem("csrf");const n=r.parseCSRF(new URL(t));(!e||!n||n!==e)&&(he("DCBrowserExt:Viewer:User:Error:NonMatchingCsrfToken:FailedToLogin"),ye())}}catch{}}function ke(t,a,n,r,i){i&&t.forEach((e=>{n.has(e)&&a.searchParams.append(e,n.get(e))})),r&&t.forEach((t=>{""!==e.getItem(t)&&a.searchParams.append(t,e.getItem(t))}))}function De(e){let t=e?.toLowerCase();return t?.startsWith("gmail")?"gmail":t?.startsWith("googledrive")?"gdrive":t?.startsWith("embeddedpdf")?"embeddedPDF":t?.startsWith("outlook")?"outlook":""}const Ce=()=>{try{let r;r=W&&!te?new URLSearchParams(new URL(C).search):new URLSearchParams(window.location.search);const i=function(t){const a=e.getItem("cdnUrl");try{if("prod"===e.getItem("env"))return a;const n=t.get("ephemeralUrl");if(!n)return a;const r=new URL(n);return"https:"===r.protocol&&["stage.acrobat.adobe.com","dev.acrobat.adobe.com"].includes(r.hostname)&&r.pathname.startsWith("/ephemeral/dc-chrome-extension/")?n:a}catch(e){return console.error("Error validating ephemeral URL:",e?.message||"Unknown error"),a}}(r),o=new URL(i);if(!ue(o.origin))return Pe("Invalid CDN URL detected","Invalid Origin"),void fe();k||(k=o);let s=e.getItem("viewer-locale");s||(s=e.getItem("locale"));const c="false"!==e.getItem("logAnalytics"),d="false"!==e.getItem("ANALYTICS_OPT_IN_ADMIN"),l=c&&d?"true":d?"optinOff":"gpoOff",m="true"===e.getItem("betaOptOut"),p=r.get("blob-uri");u(p,C)&&(o.searchParams.append("blob-uri",p),o.searchParams.append("isDemoMode",!0),f()&&["returnURL","returnTabId"].forEach((e=>{const t=r.get(e);t&&o.searchParams.set(e,t)}))),"true"===r.get("ogap")&&o.searchParams.append("ogap","true"),r.get("olfs")&&o.searchParams.append("olfs",r.get("olfs")),o.searchParams.append("locale",P||s),o.searchParams.append("logAnalytics",l),o.searchParams.append("callingApp",chrome.runtime.id),o.searchParams.append("betaOptOut",m),o.searchParams.append("lfa",e.getItem("isAllowedLocalFileAccess")||"false"),o.searchParams.append("enableCaretMode",z),t.getItem("signInTp")&&o.searchParams.append("touchp",t.getItem("signInTp")),o.searchParams.append("rvu",e.getItem("userState")?.rvu??null);const I=e.getItem("installType")||"update",w=e.getItem("installSource");o.searchParams.append("version",`${chrome.runtime.getManifest().version}:${I}`),o.searchParams.append("installSource",w),o.searchParams.append("storage",JSON.stringify(e.getItem("viewerStorage")||{})),o.searchParams.append("fgContextVars",JSON.stringify(e.getItem("fgContextVars")||{})),o.searchParams.append("tabId",K),o.searchParams.append("anonUserUUID",e.getItem("anonUserUUID")),o.searchParams.append("fglc",e.getItem("fgLastCallTimestamp")),"false"===e.getItem("staticFteCoachmarkShown")&&o.searchParams.append("showFTECoachmark","true"),"true"!==de("googlePrint")&&!0!==G||"false"===a.getItem("googleAppsPrint")||o.searchParams.append("googleAppsPrint","true"),o.searchParams.append("sdp",e.getItem("sdp")?"1":"0"),o.searchParams.append("sds",e.getItem("sds")?"1":"0"),o.searchParams.append("lf",e.getItem("lf")?"1":"0");const b=Y.read(C);b&&(delete b.filename,delete b.lastVisited,o.searchParams.append("docState",JSON.stringify(b))),o.searchParams.append("nu",g()),o.searchParams.append("rs",e.getItem("rs")?"1":"0"),o.searchParams.append("nm",e.getItem("supportNightMode")?"1":"0"),o.searchParams.append("dpt",e.getItem("isDarkPageThemeEnabled")?"1":"0");const v=t.getWithTTL("pdfMarkerAction");v&&(o.searchParams.append("action",v),t.removeItem("pdfMarkerAction")),o.searchParams.append("adminDisableGenAI","true"===e.getItem("DISABLE_GENAI_BY_ADMIN")?"1":"0"),o.searchParams.append("serVar",h());const y=["dropin!","provider!","app!","forceDisableGenAI"],_=["analytics","logToConsole","enableLogging","frictionless","sessionId","linearization","ev","ao"],S=["rrv","fgRearch","isDeskTop","isAcrobat","theme","defaultOwnerShipExperiment","sessionId","ev","ao","ip","rate","genAI","mv","pi","ks","edd","tpt","lft","fsu","dcs","egal","ots","egaf","gga","pnb","subv2","s3d","ips","ripe","od","kwgn","rat"];let L=a.getItem("signinTouchPointData");L=JSON.parse(L||"{}"),L&&"object"==typeof L&&Object.keys(L).length&&(o.searchParams.append("tp",L.touchpoint),o.searchParams.append("acmt",L.allowCommentsInShare?"1":"0")),a.removeItem("signinTouchPointData");e.getItem("env");const R=new URLSearchParams(new URL(C).search).get(T),U=De(R);if(R&&U){const t=("true"===e.getItem(`${U}-pdf-default-viewership`)).toString();o.searchParams.append("aDFrSfce",t),console.log("surface id ",U),o.searchParams.append("sfceId",U),o.searchParams.append("sfceDVFe","true"===e.getItem(`${U}-pdf-dv-feature-enablement-status`)),o.searchParams.append("aDFrSfceUsrDsbl",function(t,a){return"true"===e.getItem(`${t}-pdf-default-viewership-user-disabled`)||"GoogleDriveNativeViewNDV"===a}(U,R))}n=o,["dialog!dropin","load!dropin"].forEach((e=>{""!==(a.getItem(e)||"")&&n.searchParams.append(e,a.getItem(e))})),ke(_,o,r,!1,!0),ke(S,o,r,!0,!1);let E=o.href;y.forEach((e=>{r.forEach(((t,a)=>{a.startsWith(e)&&(E=E+"&"+a+"="+t)}))})),""===t.getItem("payPalUrl")||""===a.getItem("dialog!dropin")&&""===a.getItem("load!dropin")||(E+=t.getItem("payPalUrl"));const D=a.getItem("access_token");return a.removeItem("access_token"),e.setItem("lastPdfOpenTimestamp",(new Date).getTime()),`${E}${D?`/#${D}`:""}`}catch(e){he("DCBrowserExt:Viewer:Iframe:Creation:Failed"),fe()}var n},Fe=(a,n,r="localStorage")=>{if(n){const i="localStorage"===r?e.getItem(a):t.getItem(a);let o;i&&i.tabsInfo?(o=i.tabsInfo,o.includes(n)||o.push(n)):o=[n],"localStorage"===r?e.setItem(a,{tabsInfo:o}):t.setItem(a,{tabsInfo:o})}},Ae=()=>{try{!function(){try{let e=Te();e&&e.indexOf("#")>-1&&(r.saveAccessToken(e),r.signInAnalyticsLogging(e),r.checkSignInFromEditVerbPaywall(e),e=e.split("#")[0],W?C=e:(window.location.hash=e,history.replaceState(null,document.title,e)))}catch(e){}}(),W&&(K=H);const a=window.document.getElementById("Adobe-dc-view");W||(d=de("clen")||-1),M=new Ue(a);const n=Ce();M.createIframe(n),p(),window.addEventListener("message",(a=>{!a.data||!ue(a.origin)||B||"hsready"!==a.data.type&&"ready"!==a.data.type||(B=!0,x=(new Date).getTime(),V=a.data.requestId,"on"===a.data.killSwitch?(he("DCBrowserExt:Viewer:KillSwitch:Turned:On"),e.setItem("pdfViewer","false"),i.setViewerState("disabled"),e.setItem("killSwitch","on"),W?fe(!0):setTimeout((()=>{window.location.href=C}),200)):e.getItem("killSwitch")&&(he("DCBrowserExt:Viewer:KillSwitch:Turned:Off"),e.removeItem("killSwitch")),t.getItem("signInTp")&&t.removeItem("signInTp"))}))}catch(e){Pe("Error create Iframe",e)}};function Be(e){if(D)return D;let t=e;try{const a=e.split("?")[0].split("/").filter((e=>e.length>0)),n=a.length>0?a[a.length-1]:"untitled";t=n;const r=n.length-4;(n.length<4||n.toLowerCase().indexOf(".pdf")!==r)&&(t+=".pdf")}catch(e){Pe("Error in getFileNameFromURL",e)}return t}function xe(e,t){return function(a){if(this.readyState==this.HEADERS_RECEIVED){if(!function(e,t){const a=e.getResponseHeader("content-type"),n=at(t),r=e.getResponseHeader("content-disposition");if(a){const e=a.toLowerCase().split(";",1)[0].trim();if(!n&&r&&/^\s*attachment[;]?/i.test(r))return!1;if("application/pdf"===e)return!0;if("application/octet-stream"===e&&r&&/\.pdf(["']|$)/i.test(r))return!0}return!1}(e,t))return Pe("Fall back to native - not pdf from headers"),fe(!1,e.getResponseHeader("content-type"));if(F=!0,"true"===X){const e=this.getResponseHeader("accept-ranges"),a=this.getResponseHeader("content-length");e&&"bytes"===e&&a&&Number(a)>0&&le({main_op:"setupWorkerOffscreen",pdfURL:t,pdfSize:+a,acceptRanges:!0,tabId:K})}}}}function Me(e,t){let a=!1;return function(n){n.lengthComputable&&(d=n.total,e.sendProgress("progress",n.total,n.loaded,t),a||(!function(e){const t=Y.read(C)||{},a={main_op:"getFileSize",fileSize:e,tabId:K,docLastOpenState:t,target:"offscreen"};chrome.runtime.sendMessage(a)}(d),a=!0))}}function Ve(e,t){"PDF"===function(e){if(e)try{let t=new URL(e).pathname;return t.substr(t.lastIndexOf(".")+1).toUpperCase()}catch(e){return""}return""}(e)&&(F=!0);const a=new XMLHttpRequest;a.open("GET",e),a.responseType="arraybuffer",a.onreadystatechange=function(){4===a.readyState&&(200!==a.status&&0!=a.status||(t({buffer:a.response,mimeType:a.getResponseHeader("content-type")}),Ne(a.response)))},a.send(null)}async function Oe(t){try{const n=a.getItem("bufferTabId"),r=nt(C)&&await U.hasKey(t);if(n||r){const e=n&&await l.getDataFromIndexedDB(n)||await U.getDataFromIndexedDB(t);if(e&&e.fileBuffer)return a.setItem("bufferFromIndexedDB",!!n),F=!0,{buffer:e.fileBuffer}}else{const t=e.getItem("tabIdMap");if(t){const n=(W?await chrome.tabs.query({active:!0,currentWindow:!0}):[await chrome.tabs.getCurrent()])[0];if(n&&t[n.id]){a.setItem("bufferTabId",t[n.id]);const r=await l.getDataFromIndexedDB(t[n.id]);if(Object.keys(t).length>1?(delete t[n.id],e.setItem("tabIdMap",t)):e.removeItem("tabIdMap"),r&&r.fileBuffer)return a.setItem("bufferFromIndexedDB",!0),F=!0,{buffer:r.fileBuffer}}}}}catch(e){}return a.setItem("bufferFromIndexedDB",!1),{}}function Ne(e){const t=Y.read(C)||{},a=new Blob([e],{type:"application/pdf"}),n={main_op:"getFileBuffer",fileBufferBlob:URL.createObjectURL(a),tabId:K,docLastOpenState:t,target:"offscreen"};chrome.runtime.sendMessage(n)}function $e(e,t,a){return new Promise(((n,r)=>{const i=C;if(i.startsWith("file://"))return void Ve(i,n);const o=new XMLHttpRequest;o.open("GET",i),o.responseType="arraybuffer",t&&o.setRequestHeader("If-Range","randomrange"),o.onreadystatechange=xe(o,i),o.onprogress=Me(e,a),o.onload=()=>{if(o.status>=200&&o.status<400)n({buffer:o.response,mimeType:o.getResponseHeader("content-type"),downLoadEndTime:(new Date).getTime()}),Ne(o.response),async function(e){if(nt(C)){const t=(await chrome.tabs.getCurrent())?.id;try{await U.storeInIndexedDB(e,t)}catch(e){}}}(o.response);else{const e={status:o.status,statusText:o.statusText};r({message:"Invalid response fetching content",error:e})}},o.onerror=e=>{te?l.getFileByHash(C).then((async e=>{if(e)F=!0,d=e.byteLength,n({buffer:e,mimeType:"application/pdf",downLoadEndTime:(new Date).getTime(),fileSize:e.byteLength});else{m.info({message:"User tried opening blob file after expiry"});const e=C.substring(C.indexOf("blob"));chrome.tabs.update({url:e})}})).catch((e=>{r({message:"Error to get file contents from indexedDB",error:e})})):r({message:"Error to download file contents",error:e})},o.ontimeout=e=>{r({message:"Timeout to download file contents",error:e})},o.send()}))}function We(e,t){he(`DCBrowserExt:Viewer:SignIn:AdobeYolo:${e}:clicked`),chrome.tabs.query({active:!0,currentWindow:!0},(function(e){var t=e[0]&&e[0].id;Fe("adobeYoloTabsInfo",t,"sessionStorage")})),le({main_op:"launchJumpUrl",details:{source:e,userGuid:t}},(t=>{M._sendMessage({type:"adobeYoloJumpResponse",response:t,source:e},k.origin)}))}function He(e,t,...a){W?l.storeBufferAndCall(e,t,H,...a):chrome.tabs.getCurrent((function(n){l.storeBufferAndCall(e,t,n.id,...a)}))}function Ge(e){M._sendMessage({type:"redirectToAcrobatWeb",response:e},k.origin)}function je(){W?chrome.tabs.reload(H):chrome.tabs.getCurrent((e=>{chrome.tabs.reload(e.id)}))}function ze(r,d){switch(d.data.main_op){case"open_in_acrobat":case"fillsign":!async function(t,a){const r={main_op:"open_in_acrobat"};if("fillsign"===a.data.main_op?r.paramName="FillnSign":a.data.paramName&&(r.paramName=a.data.paramName),r.url=a.data.url,r.click_context="pdfviewer",r.timeStamp=Date.now(),r.filename=a.data&&a.data.filename,a.data.fileBuffer){const e=new Blob([a.data.fileBuffer],{type:"application/pdf"});r.dataURL=URL.createObjectURL(e)}if(j=function(e){"fillsign"===a.data.main_op?t.openInAcrobatResponse("FILLSIGN_IN_DESKTOP_APP",e,a.origin):t.openInAcrobatResponse("OPEN_IN_DESKTOP_APP",e,a.origin),Pe(`Open In Acrobat - (${a.data.main_op}) response- ${e}`)},e.getItem("isSharepointFeatureEnabled"))if(Re.isSharePointURL)r.workflow_name="SharePoint",r.isSharePointURL=!0,le(r,j);else{const e=await n.checkForSharePointURL(r.url);r.isSharePointURL=e,e&&(r.workflow_name="SharePoint"),le(r,j)}else le(r,j)}(r,d);break;case"complete_conversion":he("DCBrowserExt:Viewer:Verbs:Conversion:Redirection"),function(e){const t={};t.main_op=e.data.main_op,t.conversion_url=decodeURIComponent(e.data.conversion_url),t.timeStamp=Date.now(),le(t)}(d);break;case"updateLocale":he("DCBrowserExt:Viewer:User:Locale:Updated"),e.setItem("viewer-locale",d.data.locale),le({main_op:"localeChange",locale:d.data.locale}),chrome.tabs.reload();break;case"setInitialLocale":let f=!1;e.getItem("viewer-locale")||(f=!0,e.setItem("viewer-locale",d.data.locale),he("DCBrowserExt:Viewer:User:Locale:Initial")),d.data.reloadReq&&f&&chrome.tabs.reload();break;case"error-sign-in":!function(e){const t=n.uuid();a.setItem("csrf",t);const r=new URL(e),i=we();i.hash=i.hash+`state=${t}&signInError=true`,r.searchParams.set("redirect_uri",i),chrome.tabs.update({url:r.href,active:!0})}(d.data.url);break;case"deleteViewerLocale":e.getItem("viewer-locale")&&(e.removeItem("viewer-locale"),chrome.tabs.reload());break;case"signin":he("DCBrowserExt:Viewer:Ims:Sign:In"),a.setItem("signInSource",d.data.source),a.setItem("signinTouchPointData",JSON.stringify({touchpoint:d.data.tp,allowCommentsInShare:d.data.allowCommentsInShare})),he(`DCBrowserExt:Viewer:Ims:Sign:In:${d.data.source}`),He(d.data.fileBuffer,ve,d.data);break;case"googleSignIn":he("DCBrowserExt:Viewer:Ims:Sign:In"),he(`DCBrowserExt:Viewer:Ims:Sign:In:${d.data.source}`),a.setItem("signInSource",d.data.source),He(d.data.fileBuffer,Le,d.data);break;case"signup":he("DCBrowserExt:Viewer:Ims:Sign:Up"),a.setItem("signUpSource",d.data.source),he(`DCBrowserExt:Viewer:Ims:Sign:Up:${d.data.source}`),He(d.data.fileBuffer,ve,d.data);break;case"reload_viewer":chrome.tabs.reload();break;case"reload_current_tab":je();case"upsell_event":!function(e){if(e&&e.url){const a=new URL(decodeURIComponent(e.url));e.returnUrlParams&&t.setItem("rtParams",e.returnUrlParams.toString()),"_blank"===e.target?chrome.tabs.create({url:a.href,active:!0}):chrome.tabs.update({url:a.href,active:!0})}}(d.data);break;case"upsell_remove_urlParams":t.removeItem("rtParams"),t.removeItem("payPalUrl"),a.removeItem("dialog!dropin"),a.removeItem("load!dropin");break;case"fetchLocalRecents":const u=new URL(e.getItem("cdnUrl")).origin;if(d.data.fetchRecents){const e=d.data.showOverlay;!async function(e,t,a=!1){const n=Y.getAllItems();e.sendRecentUrl(!0,n,t,a)}(M,u,e)}else M.sendRecentUrl(!0,null,u);break;case"socialSignIn":he("DCBrowserExt:Viewer:Ims:Sign:In"),he(`DCBrowserExt:Viewer:Ims:Sign:In:${d.data.source}`),a.setItem("signInSource",d.data.source),He(d.data.fileBuffer,Se,d.data);break;case"openRecentFileLink":const h={};h.main_op=d.data.main_op,h.recent_file_url=decodeURIComponent(d.data.recent_file_url),h.file_name=d.data.file_name,le(h);break;case"updateCurrentURL":!async function(e){const{redirectURL:t,copyToClipboard:a}=e;if(a)try{await navigator.clipboard.writeText(a)}catch(e){}const n=W?H:(await chrome.tabs.getCurrent())?.id;chrome.tabs.update(n,{url:t})}(d.data);break;case"saveFileBufferAndReload":case"saveFileBufferAndReload":He(d.data.fileBuffer,je);break;case"userSubscriptionData":if(W){const e={};e.eventType=d.data.main_op,e.userSubscriptionData=d.data.userSubscriptionData,e.data=d.data,e.main_op=d.data.main_op;le(e,(function(e){e&&"showUninstallPopUp"===e.main_op&&M._sendMessage({type:"showUninstallPopUp"},k.origin)}))}break;case"uninstall":W&&le({main_op:"uninstall",defaultUrl:C});break;case"submit_form":fetch(d.data.resource,d.data.options).then((e=>{M.sendSubmitFormResponse(e.ok,d.origin)})).catch((()=>{M.sendSubmitFormResponse(!1,d.origin)}));break;case"ownerShipExperimentShown":e.removeItem("defaultOwnerShipExperiment");break;case"openAcrobatOptions":chrome.runtime.openOptionsPage(),he(`DCBrowserExt:Viewer:ManagePref:clicked:${d.data.source}`);break;case"openOnboardingTutorialFile":const I={};I.main_op=d.data.main_op,I.onboardingDemoFileURL=d.data.onboardingDemoFileURL,I.blobParam=d.data.blobParam,I.pdfURL=d.data.pdfURL,le(I);break;case"returnToYourFile":const b={};b.main_op=d.data.main_op,b.returnTabId=d.data.returnTabId,b.returnURL=d.data.returnURL,le(b);break;case"openLocalFileThoughFilePicker":!async function(e,t,a,n,r,i){l.storeFileByHash(t,`chrome-extension://${chrome.runtime.id}/${n}`).then((()=>{const t={};t.main_op=e,t.file_name=a,t.fileURL=n,t.openGenAIAssistantPanel=r,t.openLocalFileSource=i,le(t)}))}(d.data.main_op,d.data.fileBuffer,d.data.file_name,d.data.fileURL,d.data.openGenAIAssistantPanel,d.data.openLocalFileSource);break;case"openExtensionSettings":if("pinToolbar"===d.data.action)chrome.tabs.query({active:!0,currentWindow:!0},(function(e){const t=e[0];n.openExtensionSettingsInWindow({tab:t,action:d.data.action})}));else{const t=e.getItem("openSettingsInWindow");t?chrome.tabs.query({active:!0,currentWindow:!0},(function(t){const a=t[0];e.setItem("lastOpenTabId",a.id),n.openExtensionSettingsInWindow({tab:a,action:d.data.action})})):chrome.tabs.create({url:c,active:!0}),v.event(y.LOCAL_FILE_ACCESS_TOUCHPOINT_SETTINGS_OPENED,{VARIANT:t?"InWindow":"InTab"}),e.setWithTTL("LocalFileAccessTouchpointsFromViewer",!0,o),"localFileAccessBannerChallenger"===d.data.variant?e.setWithTTL("LocalFileAccessBannerChallengerTouchpointsFromViewer",!0,o):"localFileAccessBannerControl"===d.data.variant&&e.setWithTTL("LocalFileAccessBannerControlTouchpointsFromViewer",!0,o),le({main_op:"triggerBufferSave"})}break;case"encryptedWriteFile":({secureString:ee}=d.data),Ke(document.title);break;case"launchJump":He(d.data.fileBuffer,We,d.data.source,d.data.userGuid);break;case"saveAsEvent":!async function(e){try{if(he("DCBrowserExt:Viewer:SaveToMyComputer:"+(J?"fileHandlerExist":"fileHandlerNotExist")),J)Z=!1;else{const t={suggestedName:`${e.fileName}.pdf`,types:[{description:"PDF file",accept:{"application/pdf":[".pdf"]}}]};J=await window.showSaveFilePicker(t),Z=!0,Ke(J?.name)}M._sendMessage({type:"newSaveToLocalResponse",newAsset:Z,updatedFileName:J?.name},k.origin)}catch(e){J=null,Pe("Save As Handler Error",e),M._sendMessage({type:"newSaveToLocalResponse",error:e},k.origin)}}(d.data);break;case"downloadFile":!async function(e){let t=null;try{let a=e.fileUrl;const n=e.fileName||D||"untitled";if(!a){const n=new Blob([e.fileBuffer],{type:"application/pdf"});t=URL.createObjectURL(n),a=t}await chrome.downloads.download({url:a,conflictAction:"uniquify",saveAs:!0,filename:e?.fileNameWithExtension?e?.fileNameWithExtension:`${n}.pdf`}),m.info({message:"File downloaded",url:a,fileName:n,fileNamefromViewer:e.fileName})}catch(e){Pe("downloadFile error",e),M._sendMessage({type:"downloadFileError"},k.origin)}finally{t&&URL.revokeObjectURL(t)}}(d.data);break;case"rememberSaveLocationPreference":!function(t){let a="";t.cloudStorage&&!e.getItem("selectedSaveLocationPreference")?a="PreferenceMigrationSuccess":t.cloudStorage||(a="SaveDialogRememberMe");a&&he(`DCBrowserExt:Viewer:ChangeSaveLocationPreference:${a}`);(!t.cloudStorage||t.cloudStorage&&!e.getItem("selectedSaveLocationPreference"))&&(e.setItem("saveLocation",t.saveLocation),e.setItem("selectedSaveLocationPreference",!0),le({panel_op:"options_page",requestType:s.OPTIONS_UPDATE_TOGGLE,toggleId:"saveLocationPreferenceTitle",toggleVal:t.saveLocation}))}(d.data);break;case"appRenderingDone":pt();break;case"saveFileBuffer":He(d.data.fileBuffer);break;case"deleteFileBuffer":const _=a.getItem("bufferTabId");_&&l.deleteDataFromIndexedDB(_),a.removeItem("bufferTabId");case"appRenderingDone":pt();break;case"writeToLocalSavedFile":!async function(e){try{const t=await J.createWritable();await t.write(e.fileBuffer),await t.close(),M._sendMessage({type:"newSaveToLocalResponse",newAsset:Z,updatedFileName:J?.name,isFileWriteStage:!0},k.origin)}catch(e){J=null,Pe("Write to Local File Error",e),M._sendMessage({type:"newSaveToLocalResponse",error:e,isFileWriteStage:!0},k.origin)}}(d.data);break;case"bookmarkWeb":w(d.data.url,Ge,he);break;case"updateDocumentViewState":!function(e){const{documentViewState:t}=e;Y.writeAndSyncWithHistory(C,t)}(d.data);break;case"validateEdgeCertificateForDigitalSignature":i.validateCertificate(d.data).then((e=>M.sendCertificateValidationResponse(e,d.origin)));break;case"documentViewThemeChange":!function(t){e.getItem("theme")!==t.data&&(e.setItem("theme",t.theme),le({panel_op:"options_page",requestType:s.OPTIONS_UPDATE_TOGGLE,toggleId:"appearancePrefTitle",toggleVal:t.theme}));e.getItem("isDarkPageThemeEnabled")!==t.isDarkPageThemeEnabled&&e.setItem("isDarkPageThemeEnabled",t.isDarkPageThemeEnabled)}(d.data);break;case"enableGenAIFeaturesToggledFromViewer":g=d.data,e.getItem("egaf")!==g.isEnabled&&(e.setItem("egaf",g.isEnabled.toString()),le({panel_op:"options_page",requestType:s.OPTIONS_UPDATE_TOGGLE,toggleId:"enableGenAIFeaturesTitle",toggleVal:g.isEnabled}));break;case"genAIEligible":!function(t){e.setItem("genAIEligible",t?.isEligible?.toString()),ce(t?.isEligible||!1)}(d.data);break;case"rrvLayerRemoved":chrome.runtime.sendMessage({main_op:"rrvLayerRemoved",tabId:d.data.tabId,target:"offscreen"});break;case"setFloodgateResponseFromViewer":chrome.runtime.sendMessage({main_op:"handleViewerFloodgateResponse",fgResponse:d.data.fgResponse});break;case"getSignedInCachedffResponse":p=d.data.userId,M._sendMessage({type:"signedInCachedffResponse",ffResponse:e.getItem(`ffResponse_${p}`)||"{}"},k.origin);break;case"changeDefaultViewershipForSurface":chrome.runtime.sendMessage({...d.data})}var p,g}function qe(e){try{const t=new TextDecoder("utf-8").decode(e.buffer);let a=!1;-1!=t.indexOf("Linearized 1")?a=!0:-1!=t.indexOf("Linearized")&&he("DCBrowserExt:Viewer:Linearization:Linearized:Version:Other"),M._sendMessage({type:"Linearization",linearized:a},k.origin)}catch(e){he("DCBrowserExt:Viewer:Linearization:Linearized:Detection:Failed"),Pe("Linearization Detection failed",e)}}function Je(t,a,n,r){n.then((n=>{const i=n.downLoadEndTime,o=n.buffer;n.buffer.byteLength;t.preview("preview",o,d,D,r,i,a.origin),M._sendMessage({type:"NavigationStartTime",time:window.performance&&window.performance.timing&&window.performance.timing.navigationStart},k.origin),!0===e.getItem("isSaveLocationPrefEnabled")&&M._sendMessage({type:"changeSaveLocationPreference",saveLocation:e.getItem("saveLocation"),onLoad:!0},k.origin)})).catch((e=>(he("DCBrowserExt:Viewer:Error:FallbackToNative:FileDownload:Failed"),fe()))).finally((()=>{e.removeItem("sessionStarted")}))}class Ye{constructor(){this.request={main_op:"analytics"}}analytics=e=>{this.request.analytics||(this.request.analytics=[]),this.request.analytics.push([e])};sendAnalytics=()=>{le(this.request)}}function Ke(e){e&&(document.title=e+ee)}const Xe=(t,a,n)=>{const r=n?"viewerStorage":"viewerStorageAsync",i=e.getItem(r)||{};i[t]=a,e.setItem(r,i)},Ze=t=>{const a=e.getItem("viewerStorage")||{},n=e.getItem("viewerStorageAsync")||{};delete a[t],delete n[t],e.setItem("viewerStorage",a),e.setItem("viewerStorageAsync",n)};function Qe(t,n,r,i){return o=>{try{if(o.data&&o.origin&&ue(o.origin)&&(e=>{try{return e&&e.source&&e.source.top.location.origin==="chrome-extension://"+chrome.runtime.id}catch(e){return!1}})(o)){if(o.data.main_op)return ze(t,o);switch(o.data.type){case"ready":if(W?async function(t,n,r,i){let o=new Ye;A=!0;let s=C;if(te){const e=C.substring(C.indexOf("blob"));s=ae+"?pdfurl="+encodeURIComponent(e)+"&pdffilename="+encodeURIComponent(D)}document.title=D;const c=q.getHeaderValue("accept-ranges"),l=!a.getItem("bufferTabId")&&c&&"bytes"===c.toLowerCase()?"true":"false";dt(),t.sendFileMetaData("metadata",x,d,l,s,D,n.origin,!1),st(),r&&r.then((e=>{t.sendInitialBuffer("initialBuffer",e.startTime,e.endTime,e.buffer,n.origin),qe(e)})).catch((e=>{t.sendInitialBuffer("initialBuffer",0,0,-1,n.origin),o.analytics("DCBrowserExt:Viewer:Error:Linearization:InitialBufiled")})),e.removeItem("isReload"),e.removeItem("isBackForward");const m=window.performance&&window.performance.timing&&window.performance.timing.navigationStart,p=Oe(),g=await p;if(te){const a=new URL(e.getItem("cdnUrl"));Je(t,n,$e(M,!1,a.origin),m)}else g.buffer?Je(t,n,p,m):(fetch(i.streamUrl).then((e=>{let a=0;return new Response(new ReadableStream({start(r){const i=e.body.getReader();!function e(){i.read().then((({done:i,value:o})=>{i?r.close():(a+=o.byteLength,t.sendProgress("progress",d,a,n.origin),r.enqueue(o),e())})).catch((e=>{r.error(e)}))}()}}))})).then((e=>e.arrayBuffer())).then((a=>{d=a.byteLength,Ne(a),t.preview("preview",a,a.byteLength,D,m,(new Date).getTime(),n.origin),M._sendMessage({type:"NavigationStartTime",time:window.performance&&window.performance.timing&&window.performance.timing.navigationStart},n.origin),!0===e.getItem("isSaveLocationPrefEnabled")&&M._sendMessage({type:"changeSaveLocationPreference",saveLocation:e.getItem("saveLocation"),onLoad:!0},n.origin)})).catch((e=>(o.analytics("DCBrowserExt:Viewer:Error:FallbackToNative:FileDownload:Failed"),fe()))),o.sendAnalytics());Pe("Viewer loaded")}(t,o,r,n):function(e,t,n,r,i){A=!0;let o=C;if(te){const e=C.substring(C.indexOf("blob"));o=ae+"?pdfurl="+encodeURIComponent(e)+"&pdffilename="+encodeURIComponent(D)}const s=!a.getItem("bufferTabId")&&de("chunk")||"false",c=window.performance.getEntriesByType("navigation").map((e=>e.type)).includes("reload"),l=window.performance.getEntriesByType("navigation").map((e=>e.type)).includes("back_forward");dt(),e.sendFileMetaData("metadata",x,d,s,encodeURI(o),D,t.origin,c||l),st(),n?n.then((a=>{e.sendInitialBuffer("initialBuffer",a.startTime,a.endTime,a.buffer,t.origin),qe(a)})).catch((a=>{e.sendInitialBuffer("initialBuffer",0,0,-1,t.origin)})):e.sendInitialBuffer("initialBuffer",0,0,-1,t.origin),Je(e,t,r,i),Pe("Viewer loaded")}(t,o,r,n,i),le({main_op:"getUserInfoFromAcrobat"},(e=>{M._sendMessage({type:"adobeYoloUserData",...e},k.origin)})),o.data.visitorID){const t=e.getItem("viewerVisitorID");e.setItem("viewerVisitorID",o.data.visitorID),t&&t!==o.data.visitorID&&he("DCBrowserExt:Analytics:viewerVisitorID:MCMID:Changed")}break;case"getFileBufferRange":!function(e,t){let a={url:C};_.getFileBufferRange(a,e.data.range).then((a=>{$||($=!0),t.sendBufferRanges("bufferRanges",`${e.data.range.start}-${e.data.range.end}`,a.buffer,e.origin)})).catch((a=>{he("DCBrowserExt:Viewer:Error:Linearization:Range:Failed"),t.sendBufferRanges("bufferRanges",`${e.data.range.start}-${e.data.range.end}`,-1,e.origin)}))}(o,t);break;case"previewFailed":N||(he("DCBrowserExt:Viewer:Error:FallbackToNative:Preview:Failed"),N=!0,fe());break;case"lastUserGuid":e.setItem("lastUserGuid",o.data.value);break;case"signin":he("DCBrowserExt:Viewer:Ims:Sign:In"),ve();break;case"signout":he("DCBrowserExt:Viewer:Ims:Sign:Out"),e.removeItem("viewer-locale"),e.removeItem("userDetailsFetchedTimeStamp"),e.removeItem("discoveryExpiryTime"),e.removeItem("viewer-locale"),He(o.data.fileBuffer,ye);break;case"googleAppsPrintShown":a.setItem("googleAppsPrint","false"),he("DCBrowserExt:Viewer:GoogleApps:Print:Shown");break;case"signInExperimentShown":chrome.tabs.query({active:!0,currentWindow:!0},(function(t){const a=t[0],n=(new Date).getTime();e.setItem("signInExperimentShown",JSON.stringify({currTabId:a.id,timestamp:n}))}));break;case"disableViewer":e.setItem("pdfViewer","false"),chrome.tabs.reload();break;case"signInExperimentClosed":case"signInExperimentSkipped":e.setItem("signInExperimentSuppressed","true");break;case"enableBeta":e.setItem("betaOptOut","false"),chrome.tabs.reload();break;case"disableBeta":e.setItem("betaOptOut","true"),chrome.tabs.reload();break;case"updateTitle":Ke(o.data.title);break;case"viewer_set_item":Xe(o.data.key,o.data.value,o.data.startup);break;case"viewer_remove_item":Ze(o.data.key);break;case"adminStatus":e.setItem("isAdminUser",o.data.isAdmin)}}}catch(e){he("DCBrowserExt:Viewer:Error:MessageHandler:Unknown")}}}function et(){if(!B)return he("DCBrowserExt:Viewer:Error:Handshake:TimedOut"),fe(),!1}const tt=t=>{try{e.getItem("enableCSRF")&&Ee();const n=q.getHeaderValue("content-length");d=n;const r=q.getHeaderValue("accept-ranges"),i=r&&"bytes"===r.toLowerCase();C=t.originalUrl,Ae();const o=new URLSearchParams(new URL(C).search).get("blob-uri");u(o,C)?D="Acrobat Tutorial.pdf":D||(D=function(){let e;const t=q.getHeaderValue("content-disposition");if(t&&/\.pdf(["']|$)/i.test(t)){const a=/filename[^;=\n\*]?=((['"]).*?\2|[^;\n]*)/.exec(t);null!=a&&a.length>1&&(e=a[1].replace(/['"]/g,""))}return e||(e=Be(C)),decodeURIComponent(e)}());const s={url:C},c=new URL(e.getItem("cdnUrl"));k||(k=c);let l=null;const m="false"!==de("linearization")&&!a.getItem("bufferTabId");m&&i&&n>0&&(l=_.getFileBufferRange(s,{start:0,end:1024})),window.addEventListener("message",Qe(M,t,l)),ct(),setTimeout(et,25e3)}catch(e){Pe("InitMimeHandlerScript failed",e),fe()}},at=e=>e&&new URLSearchParams(e)?.has(T)||de(T)?.length>0,nt=e=>"outlookPDF"===new URLSearchParams(e).get(T)||"outlookPDF"===de(T),rt=e=>"https://drive.usercontent.google.com"===e?.origin&&e?.searchParams?.has(T),it=e=>chrome.runtime.sendMessage({main_op:"getFloodgateFlag",flag:e}),ot=()=>{try{if(C&&at(C)){const t=me(new URL(C)?.search,T),a=De(t),n="true"===e.getItem(`${a}-pdf-default-viewership`);Promise.all([it(`dc-cv-${a}-default-viewership`),it(`dc-cv-${a}-default-viewership-control`)]).then((([e,r])=>{!function(e,t){const a={main_op:"analytics"};a.analytics=[[e]],t&&a.analytics[0].push(t),le(a)}(`DCBrowserExt:Viewer:ExtnViewerPdfOpened:${t}`,S(n,e,a,e||r))}))}}catch(e){}};function st(){if(a.getItem("signInAction")){const e=a.getItem("signInAction");M._sendMessage({type:"signInInformation",action:e,source:"signIn"===e?a.getItem("signInSource"):a.getItem("signUpSource")},k.origin),a.removeItem("signInSource"),a.removeItem("signUpSource"),a.removeItem("signInAction")}}async function ct(){chrome.storage.onChanged.addListener(((t,a)=>{"local"===a&&Object.entries(t).forEach((([t,{newValue:a}])=>{switch(t){case"theme":M._sendMessage({type:"themeChange",theme:a},k.origin);break;case"ANALYTICS_OPT_IN_ADMIN":{const t="false"!==e.getItem("logAnalytics"),n="false"!==a;M._sendMessage({type:"analyticsTrackingChange",value:t&&n},k.origin);break}case"saveLocation":M._sendMessage({type:"changeSaveLocationPreference",saveLocation:a},k.origin);break;case"isDarkPageThemeEnabled":M._sendMessage({type:"darkPageThemeChange",isDarkPageThemeEnabled:a},k.origin);break;case"egaf":M._sendMessage({type:"enableGenAIFeaturesToggled",enableGenAIFeatures:a},k.origin);break;case"akamai":le({main_op:"reRegisterUninstallUrl"})}}))})),await async function(){return E=await i.isInstalledViaUpsell(),E}(),M._sendMessage({type:"setAsyncStorage",storage:e.getItem("viewerStorageAsync")},k.origin),le({main_op:"viewer-startup",url:C,startup_time:Date.now(),viewer:!0},(e=>{Re.isSharePointURL=!!e.isSharePointURL,Re.isSharePointFeatureEnabled=!!e.isSharePointEnabled,Re.isFrictionlessEnabled=!!e.isFrictionlessEnabled,Re.featureFlags=e.featureFlags,Re.isFillAndSignRegisteryEnabled=e.isFillnSignEnabled;const t=we().href;M.sendStartupConfigs(t,k.origin)})),le({main_op:"get-features&groups",cachePurge:"LAZY"},(e=>{M._sendMessage({type:"featureGroups",featureGroups:e.featureGroups,featureFlags:e.featureFlags,ffResponse:e.ffResponse},k.origin)})),W?setTimeout((()=>Fe("loadedTabsInfo",H)),2e3):le({main_op:"updateLoadedTabsInfo"}),te||Y.writeAndSyncWithHistory(C,{filename:D,lastVisited:Date.now()})}function dt(){M._sendMessage({type:"fgFlagsResponse",ffResponse:e.getItem("ffResponse_anon")||"{}",anonUserUUID:e.getItem("anonUserUUID")},k.origin)}function lt(e){le({main_op:"caret_mode_toggle_handler",toggleCaretModeValue:e})}function mt(t,a,n){switch(t.panel_op&&!0===t.reload_in_native&&(delete t.is_viewer,chrome.tabs.reload(t.tabId)),t.content_op){case"showLocalFileAccessToast":t.tabId&&t.tabId!==e.getItem("lastOpenTabId")||M._sendMessage({type:"showLocalFileAccessToast"},k.origin);break;case"rapidRenditionResponse":M._sendMessage({type:"rapidRenditionResponse",pageRendition:t.pageRendition,perfMarker:t.perfMarker},k.origin);break;case"rapidRenditionError":M._sendMessage({type:"rapidRenditionError",error:t.error},k.origin)}switch(t.main_op){case"relay_to_content":if("dismiss"===t.content_op){delete t.content_op,delete t.reload_in_native;let e=document.getElementById("__acrobatDialog__");return void(e&&(e.remove(),e=null))}"caret_mode_toggle_handler"===t.content_op&&M._sendMessage({type:"toggleCaretMode",toggleCaretModeValue:t.status},k.origin);break;case"reset":M._sendMessage({type:"toggleAnalytics",logAnalytics:t.analytics_on},k.origin);break;case"showUninstallPopUp":M._sendMessage({type:"showUninstallPopUp"},k.origin);break;case"jumpUrlSuccess":(!W||t.tabInfo&&t.tabInfo.includes(H))&&M._sendMessage({type:"adobeYoloJumpUrlSuccess"},k.origin);break;case"triggerBufferSave":M._sendMessage({type:"triggerBufferSave"},k.origin);break;case"downloadFileSuccess":M._sendMessage({type:"downloadFileSuccess"},k.origin);break;case"openViewerAIAssistant":M._sendMessage({type:"openViewerAIAssistant"},k.origin);break;case"isGenAIEligibleInCDN":if(t.activeTabId==K)return se.then(n),!0}return!1}function pt(){const t=e.getItem("userState");let a=!1;if(void 0!==t?.rvu&&(a=!0),!0!==t.rvu){const t={rvu:a};e.setItem("userState",t)}}document.addEventListener("DOMContentLoaded",function(e){const t=(new Date).getTime();let a=window.setInterval((function(){(function(){const e=document.getElementById("dc-view-frame");return e&&e.contentWindow&&1===e.contentWindow.length}()||(new Date).getTime()-t>15e3)&&(window.clearInterval(a),e.call(this))}),200)}((function(){const e=document.getElementById("dc-view-frame");e&&e.contentWindow&&e.contentWindow.focus()}))),void 0!==chrome.runtime&&(Y=new b,i.isMimeHandlerAvailable().then((async function(t){if(chrome.runtime.onMessage.addListener(mt),t){if(W=!0,!window.navigator.onLine&&e.getItem("offlineSupportDisable"))return void fe();e.getItem("sessionStarted")||(e.setItem("sessionId",n.uuid()),e.setItem("sessionStarted",!0));let t=await i.getStreamInfo()||{};if(q=new I(t.responseHeaders),H=t.tabId,0===Object.keys(t).length){const e=new URLSearchParams(window.location.search),a=e.get("pdfurl"),n=new URL(a);if(n.protocol===ie){D=e.get("pdffilename");if(await l.hasKey(`chrome-extension://${chrome.runtime.id}/${n.href}`)){const e=await l.getFileByHash(`chrome-extension://${chrome.runtime.id}/${n.href}`);L(),te=!0,F=!0,q=new I({"content-type":"application/pdf","content-length":e.byteLength}),t={mimeType:"application/pdf",originalUrl:`chrome-extension://${chrome.runtime.id}/${n.href}`,responseHeaders:q},H=(await chrome.tabs.getCurrent())?.id}}}let a=await le({main_op:"check-is-google-print"});G=a&&a.isGooglePrint,z=await i.caretModeStatus(),i.addCaretModeListener(lt),le({main_op:"viewer-preview",startup_time:Date.now(),viewer:!0},(()=>tt(t)));const r=q.getHeaderValue("content-length"),o=q.getHeaderValue("accept-ranges"),s=o&&"bytes"===o.toLowerCase();r>0&&s&&le({main_op:"setupWorkerOffscreen",pdfURL:t.originalUrl,pdfSize:+r,acceptRanges:s});e.getItem("firstOpenedTabId")||e.setItem("firstOpenedTabId",H)}else await pe(),(async()=>{try{if(e.getItem("enableCSRF")&&Ee(),!1===Q)return void(F=!1);Ae();const t=de("clen")||-1,n=de("chunk")||!1,r="false"!==de("linearization")&&!a.getItem("bufferTabId"),i={url:C},o=(new Date).getTime(),s=new URL(e.getItem("cdnUrl")),c=new URLSearchParams(new URL(C).search).get("blob-uri");u(c,C)?D="Acrobat Tutorial.pdf":(D=de("pdffilename"),D=D?encodeURIComponent(D):Be(C)),document.title=decodeURIComponent(D),k||(k=s);let d=null;const l=r&&n&&t>0;l&&(d=_.getFileBufferRange(i,{start:0,end:1024}));const m=(await chrome.tabs.getCurrent())?.id,p=Oe(m),g=(await p).buffer?p:$e(M,l,s.origin);window.addEventListener("message",Qe(M,g,d,o)),setTimeout(et,25e3),ot()}catch(e){Pe("InitScript failed",e),fe()}})(),ct()})))}();