/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
import{buildClassSelectorString,isFileTypePdfInTitle}from"./util.js";import{sendAnalytics,sendErrorLog,getElementListForSelectors}from"../gsuite/util.js";import{isDefaultViewer,openPdfInNewTabForDV}from"./default-viewership-service.js";import state from"./state.js";const checkForSearchBarDiv=()=>{try{const e=state?.config?.selectors?.GoogleDriveSearchBar?.searchBar;getElementListForSelectors(e).forEach((e=>{addSearchBarEventListener(e)}))}catch(e){sendErrorLog("Error in GSuite","Error in search bar processing in GDrive")}},addSearchBarEventListener=e=>{e&&!e.hasAttribute("search-bar-listener-added")&&(e.setAttribute("search-bar-listener-added","true"),e.addEventListener("click",(()=>{sendAnalytics([["DCBrowserExt:Gdrive:Search:Clicked"]])}),{signal:state?.eventControllerSignal}))},checkForSearchResultsTable=()=>{try{const e=state?.config?.selectors?.GoogleDriveSearchBar?.tableBody?.roleSelector,r=state?.config?.selectors?.GoogleDriveSearchBar?.tableBody?.classSelector.join(",");let t=document.getElementsByClassName(r);t=Array.from(t).filter((r=>r.matches(e))),t.forEach((e=>{addSearchTableBodyEventListener(e)}))}catch(e){sendErrorLog("Error in GSuite","Error in search table body processing in GDrive")}},addSearchTableBodyEventListener=e=>{e&&!e.hasAttribute("search-table-body-listener-added")&&(e.setAttribute("search-table-body-listener-added","true"),e.addEventListener("click",(r=>handleSearchTableClick(r,e)),{signal:state?.eventControllerSignal,capture:!0}))},handleSearchTableClick=(e,r)=>{const t=state?.config?.selectors?.GoogleDriveSearchBar?.tableRow?.roleSelector,i=e.target.closest(t);i&&processPdfSearchClickEvent(e,r,i)},processPdfSearchClickEvent=(e,r,t)=>{const i=state?.config?.selectors?.GoogleDriveSearchBar?.tableRow?.iconSelector.join(",");if(!i)return;const a=t?.querySelector(i);a&&(state?.config?.enableGDriveSearchBarAnalytics&&sendAnalytics([["DCBrowserExt:Gdrive:Search:PDF:Clicked"]]),isDefaultViewer()&&processDVSearchClick(e,r,t))},processDVSearchClick=(e,r,t)=>{try{const i=getPdfRowsFromSearchTable(r).indexOf(t);if(isPdfIndexValid(i)){const r=state?.searchFileList[i];isValidPdfFile(t,r)&&openPdfInNewTabForDV(e,r,"GoogleDriveSearch")}}catch(e){sendErrorLog("Error in GSuite","Processing error in search for GDrive default viewership")}},getPdfRowsFromSearchTable=e=>{const r=state?.config?.selectors?.GoogleDriveSearchBar?.tableRow?.roleSelector,t=state?.config?.selectors?.GoogleDriveSearchBar?.tableRow?.iconSelector?.join(",");return t?Array.from(e.querySelectorAll(r)).filter((e=>e.querySelector(t))):[]},isPdfIndexValid=e=>{const r=e>=0&&e<state?.searchFileList?.length;return r||sendErrorLog("Error in GSuite","PDF index error in search for GDrive default viewership"),r},isValidPdfFile=(e,r)=>areFileDetailsValid(r)&&validatePdfFileMatch(e,r),areFileDetailsValid=e=>!(!e?.id||!e?.title)||(sendErrorLog("Error in GSuite","PDF details missing in search for GDrive default viewership"),!1),validatePdfFileMatch=(e,r)=>{const t=buildClassSelectorString(state?.config?.selectors?.GoogleDriveSearchBar?.tableRow?.fileTitle);if(!t)return!1;const i=e?.querySelector(t);if(!i)return!1;const a=i.textContent,o=a===r?.title;return o||sendErrorLog("Error in GSuite","PDF file name mismatch in search for GDrive default viewership"),o&&isFileTypePdfInTitle(a)};export{checkForSearchBarDiv,checkForSearchResultsTable};