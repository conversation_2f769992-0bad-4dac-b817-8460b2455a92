/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
*  Copyright 2015 Adobe Systems Incorporated
*  All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Adobe Systems Incorporated and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Adobe Systems Incorporated and its
* suppliers and are protected by all applicable intellectual property laws,
* including trade secret and or copyright laws.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Adobe Systems Incorporated.
**************************************************************************/
var dc={Deferred:function(e,t){"use strict";var r=this,n=e||new Promise((function(e,n){r.resolve=function(){r.timer&&clearTimeout(r.timer),e()},r.reject=function(){r.timer&&clearTimeout(r.timer),n&&n()},t&&(r.timer=setTimeout(dc.wrap((function(){r.time_out=!0,delete r.timer,r.resolve()})),6e4))}));return this.promise=function(){return n},this.clearTimer=function(){this.timer&&clearTimeout(r.timer)},this.then=function(e){return n.then(e)},this.done=function(e){return n.then(e,e)},this},cloneTime:{},promises:[],URIs:{},uriCounter:0,doc_prefix:"",analytics:[],iframesToProcess:[],collectedStyles:[],collectedSymbols:[],svgSymbolCache:{},postData:null,topWindow:window.self===window.top,output:{refs:[],origin:location.origin,currentSize:0,hasError:!1},knownExtensions:["png","jpg","jpeg","gif","webp","bmp","svg","woff","woff2","ttf","otf","eot","css","mp3","mp4","wav","ogg","avi","mov","webm","flv","wmv","m4v","3gp","3gpp","3gpp2","3g2","m3u8","m3u8","zip"],wrap:function(e,t){"use strict";return function(){var e;try{return this.func.apply(this.context,this.args.concat(Array.prototype.slice.call(arguments,0)))}catch(t){throw t.handled||(t.handled=!0,e="DCBrowser:Error:JS:"+t.stack.match(/get-html-enhanced\.js:(\d*):(\d*)/)[0]+":"+t.message.replace(/\s/g,"_").replace(/"\S*?"/g,""),console.log(e),chrome.runtime.sendMessage({progress_op:"html-blob",main_op:OP,error_analytics:e,error:"web2pdfHTMLJSError"})),t}}.bind({func:e,context:t||{},args:Array.prototype.slice.call(arguments,1)})},random:function(){"use strict";return"W"+Math.ceil(1e5*Math.random()).toString()},log:function(e){"use strict";DEBUG&&console.log(e)},getDomain:function(e){"use strict";var t=document.createElement("a");return t.href=e,t.origin},utf8ByteLength:function(e){"use strict";if(!e)return 0;var t=encodeURI(e),r=t.match(/%/g);return r?t.length-2*r.length:t.length},resolveURL:function(e,t){"use strict";const r=["http:","https:","data:","blob:"];try{e=e||document.location.origin;const n=new URL(t,e);return r.includes(n.protocol)?n.href:null}catch(e){return null}},fetchResourceFromBackground(e,t){"use strict";return new Promise(((r,n)=>{console.log("Fetching resource from background: "+e),chrome.runtime.sendMessage({main_op:"fetch_resource",url:e,isBlob:!!t},(function(t){console.log("Response from background: ",t,"for url: "+e),t&&t.success?r(t.data):n(t.error||"Failed to fetch resource")}))}))},registerURI:function(e,t,r){"use strict";var n=0===r?"refs/"+dc.doc_prefix:dc.doc_prefix,o="."+t.replace(/[\?#]\S*/,"").split(".").pop();return(o.length>5||1===o.length)&&(o=""),(t=dc.resolveURL(e,t))?(dc.URIs[t]||(dc.URIs[t]={placeholder:n+dc.uriCounter.toString()+o},dc.uriCounter+=1),dc.URIs[t]):null},processStyle:function(e,t){if(1===e.nodeType){var r="";try{for(var n=0;n<t.sheet.cssRules.length;n++)r+=t.sheet.cssRules[n].cssText}catch(e){console.log("Error in getting cssText from style node")}r=r.replace(/\n+/g," "),e.innerHTML=r,e.innerHTML=dc.replaceCssRefs(document.location.origin,e.innerText||e.textContent,-1)}},collectShadowRootStyles:function(e,t){e&&e.querySelectorAll("style").forEach((function(e){var r=e.cloneNode(!1),n=e.textContent.trim();if(""===n&&e.sheet)try{for(var o=0;o<e.sheet.cssRules.length;o++)n+=e.sheet.cssRules[o].cssText}catch(e){console.log("Error reading cssRules from style node for shadow root",e)}n=n.replace(/\n+/g," "),r.innerHTML=n,t.content.appendChild(r)}))},collectAdoptedStyleSheets:function(e){if(!e||!e.adoptedStyleSheets)return[];let t=[];return e.adoptedStyleSheets.forEach((e=>{try{let r=Array.from(e.cssRules).map((e=>e.cssText)).join("\n"),n=document.createElement("style");r=dc.replaceCssRefs(document.location.origin,r,0),r=r.replace(/\n+/g," "),n.textContent=r,t.push(n)}catch(e){console.log("Error reading adoptedStyleSheet:",e)}})),t},collectAllSymbols:function(){document.querySelectorAll("svg").forEach((e=>{"none"===window.getComputedStyle(e).display&&e.querySelector("symbol")&&dc.collectedSymbols.push(e.cloneNode(!0))}))},getType:function(e,t){"use strict";var r="image";return t&&(t.endsWith(".ttf")?(dc.analytics.push("FONT_TTF"),r="font"):t.endsWith(".otf")?(dc.analytics.push("FONT_OTF"),r="font"):t.endsWith(".woff")?(dc.analytics.push("FONT_WOFF"),r="font"):t.endsWith(".eot")?(dc.analytics.push("FONT_EOT"),r="font"):t.endsWith(".svg")&&(r="svg_image",dc.analytics.push("SVG_IMAGE"))),e&&("text/xml"===e||"application/vnd.ms-fontobject"===e?(dc.analytics.push("FONT_EOT"),r="font"):"font/woff2"===e&&(dc.analytics.push("FONT_WOFF2"),r="font"),e.startsWith("image/svg")&&(r="svg_image",dc.analytics.push("SVG_IMAGE"))),r},readImage:function(e,t){"use strict";var r;e.status<400&&e.response&&e.response.type&&e.response.type.startsWith("image/")?(t.type=dc.getType(e.response.type,t.placeholder),-1!==EXCLUDE_ENHANCED.indexOf(t.type)?(t.data=null,t.promise.resolve()):((r=new FileReader).onloadend=dc.wrap((function(e){t.promise.time_out||(t.data=e.target.result,t.promise.resolve())})),r.onerror=dc.wrap((function(){t.promise.time_out||(dc.log("FAILED to load: "+e.responseURL),t.data=null,t.promise.resolve())})),r.readAsDataURL(e.response))):(dc.log("FAILED to load: "+e.responseURL),t.promise.resolve())},getDataURI:function(e,t,r){"use strict";if(0===t.indexOf("data:"))return t;if(!t)return null;var n,o,c=dc.registerURI(e,t,r);if(!c)return null;if(c.promise)return c.placeholder;n=new XMLHttpRequest,c.promise=new dc.Deferred(null,!0),dc.promises.push(c.promise);const s=dc.resolveURL(e,t);return s?(o=s.replace(/^https?:/,""),n.open("GET",o,!0),n.responseType="blob",n.onload=dc.wrap((function(e){c.promise.time_out||null===dc||dc.readImage(this,c)}),n),n.onerror=function(){if(!c.promise.time_out){dc.fetchResourceFromBackground(s,!0).then((function(e){c.type=dc.getType(null,c.placeholder),-1!==EXCLUDE_ENHANCED.indexOf(c.type)?c.data=null:c.data=e,c.promise.resolve()})).catch((function(e){dc.log("FAILED to load image via background: "+o),c.data=null,c.error=!0,c.promise.resolve()}))}},n.send(),c.placeholder):null},CreateStyleSheet(e){const t=new CSSStyleSheet;return t.replaceSync(e),t},processAllStyleSheets:async function(){"use strict";const e=document.styleSheets,t=[CSSRule.IMPORT_RULE,CSSRule.PAGE_RULE];for(let c=0;c<e.length;c++){const s=e[c];let a=null;try{a=s.cssRules}catch(e){if(s.href){let e=await dc.fetchResourceFromBackground(s.href,!1);a=dc.CreateStyleSheet(e).cssRules}}if(!a)continue;let i="";for(let e=0;e<a.length;e++){const r=a[e];if(!t.includes(r.type)){let e=r.cssText;e=e.replace(/url\((['"]?)([^'"\)]+)\1\)/g,(function(e,t,r){const n=dc.resolveURL(s.href||document.location.origin,r);return n?n.startsWith("data:")?e:"url("+n+")":""})),i+=e}}if(s.href){if(0===i.length)continue;var r=dc.registerURI(null,s.href,0);if(!r)continue;const e=dc.resolveURL(null,s.href);if(!e){r.promise.resolve();continue}i=dc.replaceCssRefs(e,i,0),r.data=i,r.type="css";var n=document.createElement("link");n.setAttribute("href",r.placeholder),n.setAttribute("rel","stylesheet");var o=document.querySelector(`link[href="${s.href}"]`);if(o)for(let e=0;e<o.attributes.length;e++){const t=o.attributes[e],r=t.name.toLowerCase();if("href"!==r&&"rel"!==r)try{dc.shouldCleanAttribute(t.name,t.value)||n.setAttribute(t.name,t.value)}catch(e){}}s.media&&n.setAttribute("media",s.media),dc.collectedStyles.push(n)}else{let e=document.createElement("style");e.innerHTML=dc.replaceCssRefs(s.href||document.location.origin,i,0),s.media&&e.setAttribute("media",s.media),dc.collectedStyles.push(e)}}},isImageURL:function(e){"use strict";try{const t=new URL(e);return/\.(png|jpe?g|gif|webp|bmp|svg)$/i.test(t.pathname)}catch(e){return!1}},isFontURL:function(e){"use strict";try{const t=new URL(e);return/\.(woff|woff2|ttf|otf|eot)$/i.test(t.pathname)}catch(e){return!1}},isUnknownExtension:function(e){"use strict";try{const t=new URL(e).pathname.split(".").pop();return!dc.knownExtensions.includes(t)}catch(e){return!1}},replaceURL:function(e,t,r){"use strict";var n=new RegExp(e.replace(/([\.\^\$\*\+\?\(\)\[\{\\\|])/g,"\\$1")+"|(?:"+t.replace(/([\.\^\$\*\+\?\(\)\[\{\\\|])/g,"\\$1")+")+","g");return r.replace(n,t)},isImageHead:async function(e){"use strict";try{const t=await fetch(e,{method:"HEAD"});return!!t.headers.get("content-type").startsWith("image/")}catch(e){return!1}},replaceCssRefs:function(e,t,r){"use strict";var n,o,c,s=function(e){var t=e.split("/");return t.length<4?e:t.slice(0,-1).join("/")}(e),a=[],i=/([\s\S]{0,10})url\s*\(([\s\S]*?)\)/gm;for(t=t.replace(/\/\*[\s\S]*?\*\//gm,""),n=i.exec(t);n;)o=(c=n[2]).replace(/('|")/g,""),c&&0!==o.indexOf("data:")&&a.push({url:o,imprt:-1!==n[1].indexOf("@import"),originalURL:c}),n=i.exec(t);return a.forEach(dc.wrap((function(e){if("acro-html"===OP){let n=dc.resolveURL(s,e.url);t=n&&dc.isImageURL(n)||dc.isUnknownExtension(n)?dc.replaceURL(e.originalURL,dc.getDataURI(s,n,r+1),t):dc.replaceURL(e.originalURL,n||"",t)}else t=dc.replaceURL(e.originalURL,dc.getDataURI(s,e.url,r+1)||"",t)}))),t},processStyleAttr:function(e){"use strict";var t=e.getAttribute?e.getAttribute("style"):"";t&&e.setAttribute("style",dc.replaceCssRefs(document.location.origin,t,-1))},resolveRefs:function(e,t){"use strict";var r,n,o;function c(e,t){t.forEach((t=>e.hasAttribute(t)&&e.removeAttribute(t)))}if(n=t.attributes)for(r=0;r<n.length;r+=1)0===n[r].name.toLowerCase().indexOf("on")&&e.removeAttribute(n[r].name);function s(e,t,r,n){const o=`${r.replace(/[^a-zA-Z0-9]/g,"_")}_${n}`,c=t.cloneNode(!0);c.setAttribute("id",o);let s=document.createElement("svg");s.setAttribute("xmlns","http://www.w3.org/2000/svg"),s.style.display="none",s.appendChild(c);dc.collectedSymbols.some((e=>null!==e.querySelector(`#${o}`)))||dc.collectedSymbols.push(s),e.setAttribute("href","#"+o)}function a(e,t){-1!==["IMG","INPUT"].indexOf(e.tagName)&&(e.dataset._html_to_pdf_src_=dc.getDataURI(null,t.currentSrc||t.src,0)||"",e.removeAttribute("src"),c(e,["srcset","sizes","loading","decoding","crossorigin"])),"svg"===e.tagName&&dc.analytics.push("SVG"),"image"===e.tagName&&function(e){var t,r,n=e.attributes,o=n.length;for(t=0;t<o;t+=1)"href"!==(r=n[t]).localName&&"src"!==r.localName||dc.shouldCleanAttribute(r.name,r.value)||e.setAttribute(r.name,dc.getDataURI(null,r.value,-1)||"")}(e)}function i(e,t){let r=document.createElement("IMG");l(t,r);try{r.dataset._html_to_pdf_src_=t.toDataURL("image/png")}catch(e){return dc.analytics.push("TAINTED_CANVAS"),null}return dc.analytics.push("CANVAS"),r}function l(e,t){const r=e.attributes;for(let e=0;e<r.length;e++){const n=r[e];if("src"!==n.name&&"href"!==n.name)try{dc.shouldCleanAttribute(n.name,n.value)||t.setAttribute(n.name,n.value)}catch(e){}}}switch(e.nodeType===Node.TEXT_NODE&&t.parentNode&&t.parentNode.tagName&&"style"===t.parentNode.tagName.toLowerCase()&&(e.innerHTML=dc.replaceCssRefs(document.location.origin,e.textContent,-1)),e.tagName){case"PICTURE":!function(e,t){const r=t.querySelector("img");if(r){const n=r.cloneNode(!1);let o=r.currentSrc||r.getAttribute("src");(!o||"string"==typeof o&&o.length<1)&&function(e,t){const r=t.querySelectorAll("source");for(let t of r){let r=t.getAttribute("srcset");if(r){let t=r.split(",").map((e=>{let[t,r]=e.trim().split(/\s+/);return{url:t,size:parseFloat(r)||1}}));t.sort(((e,t)=>t.size-e.size)),e=t.length>0?t[0].url:e;break}}}(o,t),o&&(c(n,["srcset","sizes","loading","decoding","crossorigin"]),dc.cleanAttributes(n),n.dataset._html_to_pdf_src_=dc.getDataURI(null,o,0)||"",n.removeAttribute("src"),e.appendChild(n))}}(e,t);break;case"IMG":case"IMAGE":case"svg":case"SVG":a(e,t);break;case"INPUT":!function(e,t){"image"===e.type&&a(e,t),"file"!==e.type&&e.setAttribute("value",t.value),["radio","checkbox"].includes(e.type)&&(e.removeAttribute("checked"),t.checked&&e.setAttribute("checked","checked"))}(e,t);break;case"OPTION":!function(e,t){e.removeAttribute("selected"),t.selected&&e.setAttribute("selected","selected")}(e,t);break;case"EMBED":!function(e){"application/x-shockwave-flash"===e.type&&dc.analytics.push(dc.topWindow?"FLASH":"FLASH_IN_IFRAME"),"acro-html"===OP&&e.setAttribute("src",e.src)}(e);break;case"OBJECT":!function(e){"application/x-shockwave-flash"===e.type&&dc.analytics.push(dc.topWindow?"FLASH":"FLASH_IN_IFRAME")}(e);break;case"IFRAME":case"FRAME":case"AUDIO":e=null;break;case"A":!function(e){(o=e.getAttribute("href"))&&0===o.indexOf("/")&&e.setAttribute("href",e.href)}(e);break;case"STYLE":!function(e,t){if(1===e.nodeType){if(""===t.textContent.trim()){var r="";try{for(var n=0;n<t.sheet.cssRules.length;n++)r+=t.sheet.cssRules[n].cssText}catch(e){console.log("Error in getting cssText from style node")}r=r.replace(/\n+/g," "),e.innerHTML=r}e.innerHTML=dc.replaceCssRefs(document.location.origin,e.innerText||e.textContent,-1)}}(e,t);break;case"CANVAS":e=i(0,t);break;case"PARAM":!function(e){"application/x-shockwave-flash"===e.parentElement.type&&dc.analytics.push(dc.topWindow?"FLASH":"FLASH_IN_IFRAME")}(e);break;case"VIDEO":e=function(e,t){if(t.poster||t.getAttribute("poster")){let r=document.createElement("IMG");return l(t,r),r.dataset._html_to_pdf_src_=dc.getDataURI(null,t.poster||t.getAttribute("poster"),0)||"",r}{function n(e){try{const t=document.createElement("canvas"),r=t.getContext("2d");l(e,t);const n=e.getBoundingClientRect();return t.width=n.width,t.height=n.height,r.drawImage(e,0,0,t.width,t.height),i(0,t)}catch(e){return console.log("Error in extracting video frame"),null}}return n(t)}}(0,t);break;case"use":!function(e,t){let r=t.getAttribute("href")||t.getAttribute("xlink:href");if(r&&r.includes("#")&&(r.startsWith("url(")||!r.startsWith("#"))){let[t,n]=r.split("#");t.startsWith("url(")&&(t=t.slice(4,-1));const o=dc.resolveURL(document.location.origin,t);if(!o)return;if(dc.svgSymbolCache[o]){const r=dc.svgSymbolCache[o][n];r&&s(e,r,t,n)}else{let r=new dc.Deferred(null,!0);dc.promises.push(r);const c=new XMLHttpRequest;c.open("GET",o,!0),c.onload=dc.wrap((function(){if(this.status<400){const r=this.responseText,c=(new DOMParser).parseFromString(r,"image/svg+xml").querySelectorAll("symbol");dc.svgSymbolCache[o]={},c.forEach((e=>{dc.svgSymbolCache[o][e.id]=e}));const a=dc.svgSymbolCache[o][n];a&&s(e,a,t,n)}else console.error("Error loading SVG:",this.status,this.statusText);r.resolve()}),c),c.onerror=dc.wrap((function(){console.error("Error loading SVG:",c.statusText),r.resolve()}),c),c.send()}}}(e,t)}return e&&dc.processStyleAttr(e),e},getDocType:function(e){"use strict";var t,r=e.doctype;function n(e){return e.replace(/[&<>]/g,(function(e){return{"&":"&amp;","<":"&lt;",">":"&gt;"}[e]}))}return null===r?"":(t="<!DOCTYPE "+r.name,r.publicId?t+=' PUBLIC "'+n(r.publicId)+'"':r.systemId&&(t+=" SYSTEM"),n(r.systemId)&&(t+=' "'+r.systemId+'"'),t+">")},shouldProcessChildren:function(e){return"PICTURE"!==e.tagName},shouldCleanAttribute:function(e,t){return!e||e.startsWith("on")||"function"==typeof t||"string"==typeof t&&t.toLowerCase().includes("javascript:")||e.startsWith("data-")&&!e.includes("_html_to_pdf_src_")&&t&&t.length>100},cleanAttributes:function(e){if(!e||e.nodeType!=Node.ELEMENT_NODE||!e.attributes)return;Array.from(e.attributes).filter((e=>dc.shouldCleanAttribute(e.name,e.value))).forEach((t=>e.removeAttribute(t.name)))},htmlTree:function(e){"use strict";function t(e){return"none"!==(e.style?e.style.display:"").toLowerCase()&&(8!==e.nodeType&&(-1===["BASE","SCRIPT","NOSCRIPT"].indexOf(e.tagName)&&("LINK"!==e.tagName&&("LINK"===e.tagName&&"preconnect"===e.getAttribute("rel")&&e.remove(),("IMG"!==e.tagName||1!==e.width||1!==e.height)&&("IFRAME"!==e.tagName&&"STYLE"!==e.tagName)))))}var r,n,o;if(r=e.cloneNode(!1),r=dc.resolveRefs(r,e),dc.cleanAttributes(r),r&&e.shadowRoot){const n=document.createElement(e.tagName);Array.from(e.attributes).forEach((e=>{try{dc.shouldCleanAttribute(e.name,e.value)||n.setAttribute(e.name,e.value)}catch(e){}}));const o=document.createElement("template");o.setAttribute("shadowrootmode",e.shadowRoot.mode),Array.from(e.shadowRoot.childNodes).forEach((e=>{if(t(e)){const t=dc.htmlTree(e);t&&o.content.appendChild(t)}})),dc.collectShadowRootStyles(e.shadowRoot,o),dc.collectAdoptedStyleSheets(e.shadowRoot).forEach((e=>o.content.appendChild(e))),n.appendChild(o),r=n,r=dc.resolveRefs(r,e)}if(r&&e.hasChildNodes()&&dc.shouldProcessChildren(e))for(n=e.firstChild;n;)t(n)&&(o=dc.htmlTree(n))&&r.appendChild(o),n=n.nextSibling;return r},processIframes:function(){"use strict";var e,t;for(e=0;e<dc.iframesToProcess.length;e+=1)(t=dc.iframesToProcess[e])&&!t.promise&&(t.promise=new dc.Deferred,dc.promises.push(t.promise),chrome.runtime.sendMessage({main_op:"relay-msg",index:e,frameID:window.frames[e].WINDOW_ID,parentID:window.WINDOW_ID,tabId:TABID}))},receiveIframe:function(e){"use strict";if("serialize_iframe"===e.html_op&&e.tabId===TABID&&e.parentID===window.WINDOW_ID){dc.analytics=dc.analytics.concat(e.content_analytics),dc.output.refs=dc.output.refs.concat(e.refs);var t=0;e.refs.forEach(dc.wrap((function(e){t+=e.data?e.data.length:0}))),dc.output.currentSize+=t,dc.iframesToProcess[e.index].promise.resolve()}},_serialize:function(e){"use strict";var t,r,n=new dc.Deferred;dc.doc_prefix=e.frameID,t=dc.htmlTree(document.documentElement);var o=dc.processAllStyleSheets();(function e(){return o.catch((e=>{})).then((()=>{r=dc.promises.length,dc.Deferred.all(dc.promises).done(dc.wrap((function(){dc.promises.length===r?n.resolve():(r=dc.promises.length,e())})))})),n.promise()})().then(dc.wrap((function(){var r,n,o,c;const s=t.querySelector("head")||(()=>{const e=document.createElement("head");return t.insertBefore(e,t.firstChild),e})();for(r in dc.collectedStyles.length>0&&dc.collectedStyles.forEach((e=>{s.appendChild(e)})),dc.collectAllSymbols(),dc.collectedSymbols.length>0&&dc.collectedSymbols.forEach((e=>{s.appendChild(e)})),function(e){try{if(e){let t=e.querySelector('meta[http-equiv="Content-Security-Policy"]');t||(t=document.createElement("meta"),t.setAttribute("http-equiv","Content-Security-Policy"),e.insertBefore(t,e.firstChild)),t.setAttribute("content","default-src 'none'; script-src 'none'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src http: https: data:;")}if(e&&!e.querySelector("meta[charset]")){const t=document.createElement("meta");t.setAttribute("charset","utf-8"),e.appendChild(t)}}catch(e){}}(s),dc.output.html=dc.getDocType(document)+t.outerHTML.replace(/data\-_html_to_pdf_src_=/gm,"src="),dc.output.html=dc.output.html.replace(/[^\u0020-\u007E]/g,(function(e){return e})),dc.output.currentSize+=dc.output.html.length,dc.URIs)if(dc.URIs.hasOwnProperty(r)){if(dc.output.currentSize>1048576*maxSize){n="web2pdfHTMLTooLarge",dc.output.hasError=!0;break}var a={placeholder:dc.URIs[r].placeholder,type:dc.URIs[r].type,data:dc.URIs[r].data};dc.URIs[r].data?dc.output.currentSize+=dc.URIs[r].data.length:dc.URIs[r].error&&(a.url=r),dc.output.refs.push(a)}o=dc.analytics.filter((function(e,t,r){return t===dc.analytics.indexOf(e)})),dc.topWindow?(c=((new Date).getTime()-dc.cloneTime.start)/100,chrome.runtime.sendMessage({progress_op:"html-blob",main_op:OP,blob:dc.output,content_analytics:o,cloneTiming:c,error:n,error_analytics:n}),dc=null):(dc.output.refs.push({placeholder:window.WINDOW_ID+".html",type:"html",data:dc.output.html}),dc.output.currentSize+=dc.output.html.length,dc.output.currentSize>1048576*maxSize&&(n="web2pdfHTMLTooLarge"),c=((new Date).getTime()-dc.cloneTime.start)/100,chrome.runtime.sendMessage({tabid:TABID,main_op:"relay-msg",complete:!0,index:e.index,refs:dc.output.refs,frameID:window.WINDOW_ID,parentID:e.parentID,content_analytics:o,cloneTiming:c,error:n,error_analytics:n}),dc=null)})))}};function receiveIframe(e){}function serialize(e){"use strict";e.frameID===window.WINDOW_ID&&window.setTimeout(dc.wrap(dc._serialize),0,e)}dc.Deferred.all=function(e){"use strict";return new dc.Deferred(Promise.all(e))},window.WINDOW_ID=dc.random(),dc.topWindow&&(dc.cloneTime.start=new Date,serialize({frameID:window.WINDOW_ID}));