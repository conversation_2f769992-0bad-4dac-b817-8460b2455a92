# -*- coding: utf-8 -*-
"""
平台内容优化器
针对不同平台优化生成的内容
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from src.services.ai_content_generator import GeneratedContent, PlatformConfig
from src.utils.logger import logger


@dataclass
class ValidationResult:
    """内容验证结果"""
    valid: bool
    issues: List[str]
    suggestions: List[str]


@dataclass
class OptimizedContent(GeneratedContent):
    """优化后的内容"""
    validation_result: Optional[ValidationResult] = None


class PlatformContentOptimizer:
    """平台内容优化器"""
    
    def __init__(self):
        self.platform_rules = self._load_platform_rules()
        logger.info("平台内容优化器初始化完成")
    
    def _load_platform_rules(self) -> Dict[str, Dict[str, Any]]:
        """加载平台规则"""
        rules = {}
        
        # 抖音规则
        rules['douyin'] = {
            'title': {
                'max_length': 60,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_patterns': [
                    r'^\d+个?(.+)',  # 数字开头
                    r'^(.+?)是怎么(.+)',  # 问题式标题
                    r'^(.+?)竟然(.+)',  # 惊奇式标题
                ]
            },
            'description': {
                'max_length': 500,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_sections': ['内容介绍', '看点', '关注']
            },
            'tags': {
                'max_count': 20,
                'recommended_tags': ['抖音热门', '推荐', '创意', '生活']
            }
        }
        
        # B站规则
        rules['bilibili'] = {
            'title': {
                'max_length': 80,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_patterns': [
                    r'^【(.+)】(.+)',  # 方括号开头
                    r'^(.+)：(.+)',  # 冒号分隔
                ]
            },
            'description': {
                'max_length': 2000,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_sections': ['简介', 'P1', 'P2', '相关链接']
            },
            'tags': {
                'max_count': 12,
                'recommended_tags': ['哔哩哔哩', 'B站', '原创']
            }
        }
        
        # 小红书规则
        rules['xiaohongshu'] = {
            'title': {
                'max_length': 50,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_patterns': [
                    r'^我的(.+)经验分享',  # 经验分享
                    r'^测评\|(.+)',  # 测评
                ]
            },
            'description': {
                'max_length': 1000,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_sections': ['前言', '主要内容', '总结']
            },
            'tags': {
                'max_count': 20,
                'recommended_tags': ['小红书', '种草', '测评', '分享']
            }
        }
        
        # 快手规则
        rules['kuaishou'] = {
            'title': {
                'max_length': 30,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_patterns': [
                    r'^(.{5,20})$',  # 简短标题
                ]
            },
            'description': {
                'max_length': 200,
                'banned_words': ['敏感', '违规', '政治'],
                'recommended_sections': []
            },
            'tags': {
                'max_count': 10,
                'recommended_tags': ['快手', '生活', '搞笑']
            }
        }
        
        # YouTube规则
        rules['youtube'] = {
            'title': {
                'max_length': 100,
                'banned_words': ['sensitive', 'inappropriate', 'political'],
                'recommended_patterns': [
                    r'^How to (.+)',  # How to
                    r'^(\d+) (.+) Tips',  # 数字开头
                    r'^(.+) \| (.+)',  # 竖线分隔
                ]
            },
            'description': {
                'max_length': 5000,
                'banned_words': ['sensitive', 'inappropriate', 'political'],
                'recommended_sections': ['Introduction', 'Timestamps', 'Links', 'Subscribe']
            },
            'tags': {
                'max_count': 500,
                'recommended_tags': ['youtube', 'video', 'tutorial']
            }
        }
        
        return rules
    
    def optimize_for_platform(self, 
                             content: GeneratedContent, 
                             platform: str) -> OptimizedContent:
        """为特定平台优化内容"""
        try:
            # 获取平台规则
            rules = self.platform_rules.get(platform, self.platform_rules.get('douyin'))
            
            # 创建优化后的内容
            optimized = OptimizedContent(
                titles=content.titles.copy(),
                description=content.description,
                tags=content.tags.copy(),
                platform=platform,
                confidence_score=content.confidence_score,
                generation_time=content.generation_time
            )
            
            # 优化标题
            optimized.titles = self._optimize_titles(optimized.titles, rules['title'], platform)
            
            # 优化描述
            optimized.description = self._optimize_description(optimized.description, rules['description'], platform)
            
            # 优化标签
            optimized.tags = self._optimize_tags(optimized.tags, rules['tags'], platform)
            
            # 验证内容
            optimized.validation_result = self.validate_content(optimized, platform)
            
            logger.info(f"为平台 {platform} 优化内容成功")
            return optimized
            
        except Exception as e:
            logger.error(f"优化内容失败: {e}")
            # 返回原始内容
            return OptimizedContent(
                titles=content.titles,
                description=content.description,
                tags=content.tags,
                platform=platform,
                confidence_score=content.confidence_score,
                generation_time=content.generation_time,
                validation_result=ValidationResult(
                    valid=False,
                    issues=[f"优化失败: {str(e)}"],
                    suggestions=["请手动检查内容"]
                )
            )
    
    def _optimize_titles(self, titles: List[str], rules: Dict[str, Any], platform: str) -> List[str]:
        """优化标题"""
        optimized_titles = []
        
        for title in titles:
            # 截断标题
            if len(title) > rules['max_length']:
                title = title[:rules['max_length'] - 3] + "..."
            
            # 过滤敏感词
            for word in rules['banned_words']:
                title = title.replace(word, "**")
            
            # 应用平台特定优化
            if platform == 'douyin':
                # 抖音标题优化：添加表情符号
                if not any(char in title for char in ['😂', '🔥', '👍', '❤️', '✨']):
                    title = "🔥 " + title
            
            elif platform == 'bilibili':
                # B站标题优化：添加方括号
                if not title.startswith(('【', '[')) and len(title) < rules['max_length'] - 4:
                    title = f"【精选】{title}"
            
            elif platform == 'xiaohongshu':
                # 小红书标题优化：添加分隔符
                if '|' not in title and len(title) < rules['max_length'] - 3:
                    parts = title.split(' ', 1)
                    if len(parts) > 1:
                        title = f"{parts[0]} | {parts[1]}"
            
            elif platform == 'youtube':
                # YouTube标题优化：确保英文
                if not re.search(r'[a-zA-Z]', title):
                    title = "Video: " + title
            
            optimized_titles.append(title)
        
        return optimized_titles
    
    def _optimize_description(self, description: str, rules: Dict[str, Any], platform: str) -> str:
        """优化描述"""
        # 截断描述
        if len(description) > rules['max_length']:
            description = description[:rules['max_length'] - 3] + "..."
        
        # 过滤敏感词
        for word in rules['banned_words']:
            description = description.replace(word, "**")
        
        # 应用平台特定优化
        if platform == 'douyin':
            # 抖音描述优化：添加话题标签
            if '#' not in description:
                description += "\n\n#抖音创作者 #精选内容"
        
        elif platform == 'bilibili':
            # B站描述优化：添加分P信息
            if 'P1' not in description and len(description) < rules['max_length'] - 50:
                description += "\n\nP1: 视频主要内容\n\n欢迎点赞、投币、收藏三连支持！"
        
        elif platform == 'xiaohongshu':
            # 小红书描述优化：添加个人感受
            if len(description) < rules['max_length'] - 50:
                description += "\n\n这是我的个人体验分享，希望对你有帮助~"
        
        elif platform == 'youtube':
            # YouTube描述优化：添加时间戳和订阅信息
            if 'Subscribe' not in description and len(description) < rules['max_length'] - 100:
                description += "\n\nTimestamps:\n0:00 Introduction\n1:00 Main Content\n\nDon't forget to like and subscribe!"
        
        return description
    
    def _optimize_tags(self, tags: List[str], rules: Dict[str, Any], platform: str) -> List[str]:
        """优化标签"""
        # 限制标签数量
        if len(tags) > rules['max_count']:
            tags = tags[:rules['max_count']]
        
        # 确保有推荐标签
        for recommended_tag in rules['recommended_tags']:
            if recommended_tag not in tags and len(tags) < rules['max_count']:
                tags.append(recommended_tag)
        
        # 应用平台特定优化
        if platform == 'douyin':
            # 抖音标签优化：添加热门标签
            if '抖音热门' not in tags and len(tags) < rules['max_count']:
                tags.insert(0, '抖音热门')
        
        elif platform == 'bilibili':
            # B站标签优化：添加分区标签
            if '原创' not in tags and len(tags) < rules['max_count']:
                tags.insert(0, '原创')
        
        elif platform == 'xiaohongshu':
            # 小红书标签优化：添加种草标签
            if '种草' not in tags and len(tags) < rules['max_count']:
                tags.insert(0, '种草')
        
        elif platform == 'youtube':
            # YouTube标签优化：确保英文标签
            english_tags = []
            for tag in tags:
                if re.search(r'[a-zA-Z]', tag):
                    english_tags.append(tag)
                else:
                    # 尝试添加英文前缀
                    english_tags.append(f"video {tag}")
            tags = english_tags
        
        return tags
    
    def validate_content(self, content: GeneratedContent, platform: str) -> ValidationResult:
        """验证内容是否符合平台规范"""
        issues = []
        suggestions = []
        
        # 获取平台规则
        rules = self.platform_rules.get(platform, self.platform_rules.get('douyin'))
        
        # 验证标题
        if not content.titles:
            issues.append("缺少标题")
            suggestions.append("添加至少一个标题")
        else:
            for i, title in enumerate(content.titles):
                if len(title) > rules['title']['max_length']:
                    issues.append(f"标题 {i+1} 超过长度限制 ({len(title)}/{rules['title']['max_length']})")
                    suggestions.append(f"缩短标题 {i+1} 到 {rules['title']['max_length']} 字符以内")
                
                for word in rules['title']['banned_words']:
                    if word in title:
                        issues.append(f"标题 {i+1} 包含敏感词 '{word}'")
                        suggestions.append(f"移除标题中的敏感词 '{word}'")
        
        # 验证描述
        if not content.description:
            issues.append("缺少描述")
            suggestions.append("添加视频描述")
        elif len(content.description) > rules['description']['max_length']:
            issues.append(f"描述超过长度限制 ({len(content.description)}/{rules['description']['max_length']})")
            suggestions.append(f"缩短描述到 {rules['description']['max_length']} 字符以内")
        
        for word in rules['description']['banned_words']:
            if word in content.description:
                issues.append(f"描述包含敏感词 '{word}'")
                suggestions.append(f"移除描述中的敏感词 '{word}'")
        
        # 验证标签
        if not content.tags:
            issues.append("缺少标签")
            suggestions.append("添加相关标签")
        elif len(content.tags) > rules['tags']['max_count']:
            issues.append(f"标签数量超过限制 ({len(content.tags)}/{rules['tags']['max_count']})")
            suggestions.append(f"减少标签数量到 {rules['tags']['max_count']} 个以内")
        
        # 平台特定验证
        if platform == 'youtube':
            # 检查是否有英文内容
            if content.titles and not any(re.search(r'[a-zA-Z]', title) for title in content.titles):
                issues.append("YouTube标题应该包含英文")
                suggestions.append("添加英文标题或将现有标题翻译为英文")
            
            if content.description and not re.search(r'[a-zA-Z]', content.description):
                issues.append("YouTube描述应该包含英文")
                suggestions.append("添加英文描述或将现有描述翻译为英文")
        
        return ValidationResult(
            valid=len(issues) == 0,
            issues=issues,
            suggestions=suggestions
        )


# 全局实例
_optimizer = None

def get_platform_content_optimizer() -> PlatformContentOptimizer:
    """获取平台内容优化器实例"""
    global _optimizer
    if _optimizer is None:
        _optimizer = PlatformContentOptimizer()
    return _optimizer